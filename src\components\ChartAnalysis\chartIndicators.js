// 所有图表用到的指标配置
const chartIndicators = {
  // 柱状图
  barIndicators: [
    {
      key: 'oper_rev',
      name: '营业收入',
      extra: [
        { key: 'oper_rev_growth_rate', name: '营业额增长率' }
      ]
    },
    {
      key: 'tot_profit',
      name: '利润总额',
      extra: [
        { key: 'tot_profit_growth_rate', name: '利润总额增长率' }
      ]
    },
    {
      key: 'deductedprofit',
      name: '扣非归母净利润',
      extra: [
        { key: 'deductedprofit_growth_rate', name: '扣非净利润增长率' }
      ]
    },
    // ...可继续添加更多柱状图主指标
  ],

  // 雷达图
  radarIndicators: [
    { key: 'roe_basic', name: '净资产收益率ROE(加权)' },
    { key: 'deductedprofit_rate', name: '扣非归母净利润率' },
    { key: 'deductedprofit_growth_rate', name: '扣非归母净利润增长率' },
    { key: 'oper_rev_growth_rate', name: '营业收入增长率' },
    { key: 'gross_margin_yoy_growth_rate', name: '毛利率同比' },
    { key: 'cash_collection_ratio', name: '营业收现率' },
    // ...可继续添加
  ],

  // 资产结构分区
  assetStackKeys: [
    { key: 'fix_assets', name: '固定资产' },
    { key: 'cont_assets', name: '合同资产' },
    { key: 'monetary_cap', name: '货币资金' },
    { key: 'oth_cur_assets_total', name: '其他流动资产' },
    { key: 'oth_non_cur_assets', name: '其他非流动资产' },
    { key: 'intang_assets', name: '无形资产' },
    // ...
  ],

  // 负债结构分区
  debtStackKeys: [
    { key: 'st_borrow', name: '短期借款' },
    
    { key: 'lt_borrow', name: '长期借款' },
    
    { key: 'acct_payable', name: '应付账款' },
    { key: 'cont_liab', name: '合同负债' }, 
    { key: 'oth_cur_liab', name: '其他流动负债' },
    { key: 'oth_non_cur_liab', name: '其他非流动负债' },
    // ...
  ],

  // 资产负债率
  debtToAssetsKeys: [
    { key: 'debt_to_assets_ratio', name: '资产负债率' }
  ],
  // 总资产周转率
  totalAssetTurnoverKeys: [
    { key: 'total_asset_turnover', name: '总资产周转率' }
  ],
  // 净资产周转率
  equityTurnoverKeys: [
    { key: 'equity_turnover', name: '净资产周转率' }
  ],
  // 毛利率
  grossMarginKeys: [
    { key: 'gross_margin', name: '毛利率' }
  ],
  // 三费费率
  threeFeeRatioKeys: [
    { key: 'three_fee_ratio', name: '三费费率' }
  ],
  // 归母净利率
  netProfitMarginKeys: [
    { key: 'deductedprofit_rate', name: '扣非归母净利润率' }
  ],
  // 净资产收益率
  roeBasicKeys: [
    { key: 'roe_basic', name: '净资产收益率' }
  ],
  // 两金分析
  twoFundNetKeys: [
    { key: 'inventories', name: '存货' },
    { key: 'acct_rcv', name: '应收账款' },
  ],
  //两金占营业收入 比重及同比变动
  twoFundIncomeKeys: [
    { key: 'two_fund_to_revenue_ratio', name: '两金占营业收入比重' },
    { key: 'two_fund_to_revenue_growth_ratio', name: '两金占营业收入比重同比' }
  ],
  // 存货周转率
  inventoryTurnoverKeys: [
    { key: 'inventory_turnover', name: '存货周转率' }
  ],
  // 应收账款周转率
  receivablesTurnoverKeys: [
    { key: 'receivables_turnover', name: '应收账款周转率' }
  ],
  // 经营活动现金流
  cashFlowIndicators: [
    { key: 'net_cash_flows_oper_act', name: '经营活动产生的现金流量净额' },
    { key: 'net_cash_flows_inv_act', name: '投资活动产生的现金流量净额' },
    { key: 'net_cash_flows_fnc_act', name: '筹资活动产生的现金流量净额' }
  ],
  //经营活动产生的现金流量净额
  netCashFlowsOperActKeys: [
    { key: 'net_cash_flows_oper_act', name: '经营活动产生的现金流量净额' }],
      // 营收效率
  revenuePerRdPersonnelKeys: [
    { key: 'sales_collection_ratio', name: '销售回款率' }
  ],
  // 营业收现率
  cashCollectionRatioKeys: [
    { key: 'cash_collection_ratio', name: '营业收现率' }
  ],
  // 现金周期
  cashCycleKeys: [
    { key: 'cash_conversion_cycle', name: '现金周期' }
  ],

  // 研发投入
  rdInvestKeys: [
    { key: 'rd_intensity', name: '研发投入强度' },
    
    { key: 'rd_capitalization_rate', name: '研发投入资本化率' },
    
    { key: 'stmnote_rdexp', name: '研发投入' },
  ],
  // 研发费用
  rdExpKeys: [
    { key: 'rd_exp', name: '研发费用' },
    { key: 'rd_to_three_fee_ratio', name: '研发费用占比' }
  ],
  // 人效
  revenuePerPersonKeys: [
    { key: 'salary_per_capita_10k', name: '人均薪酬（万元）' },
    
    { key: 'labor_cost_profit_margin', name: '人工成本利润率' },
    
    { key: 'personnel_cost_ratio', name: '人事费用率' },
  ],
  // 分红
  divAualCashDividendKeys: [
    { key: 'div_aualcashdividend_curr', name: '分红金额' ,unit:'万元' },
    { key: 'cash_dividend_ratio_curr', name: '现金分红率' ,unit:'%' },
  ],
  // 员工数
  employeeKeys: [
    { key: 'employee', name: '员工数' }
  ],
  // 非经常性损益
  nonRecurringProfitLossKeys: [
    { key: 'non_recurring_profit_loss', name: '非经常性损益（万元）',unit:'万元' }
  ]
};

export default chartIndicators; 