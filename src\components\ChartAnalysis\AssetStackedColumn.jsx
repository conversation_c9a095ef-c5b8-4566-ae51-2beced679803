import React, { useEffect, useRef, useState } from 'react';
import { Chart } from '@antv/g2';

const AssetStackedColumn = ({ data, height = 320, title, style, isFullscreen = false, isIndividualFullscreen = false }) => {
  const container = useRef();
  
  // 监听单个全屏状态变化
  useEffect(() => {
    const handleFullscreenChange = () => {
      // setIsIndividualFullscreen(!!document.fullscreenElement); // 移除 useState 和 setIsIndividualFullscreen
    };
    document.addEventListener('fullscreenchange', handleFullscreenChange);
    document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
    document.addEventListener('mozfullscreenchange', handleFullscreenChange);
    document.addEventListener('MSFullscreenChange', handleFullscreenChange);
    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
      document.removeEventListener('webkitfullscreenchange', handleFullscreenChange);
      document.removeEventListener('mozfullscreenchange', handleFullscreenChange);
      document.removeEventListener('MSFullscreenChange', handleFullscreenChange);
    };
  }, []);
  
  // 解析高度 - 移到useEffect外部，与CustomLine保持一致
  let computedHeight = height;
  if (isIndividualFullscreen) {
    // 单个全屏时，使用视口高度的90%
    computedHeight = window.innerHeight * 0.9;
  } else if (isFullscreen) {
    // 整体全屏时，直接使用传入的 height（已经通过 scale 计算过）
    computedHeight = height;
  } else if (!height) {
    computedHeight = 320; // 默认高度
  } else if (style && style.height) {
    if (typeof style.height === 'number') {
      computedHeight = style.height;
    } else if (typeof style.height === 'string' && style.height.endsWith('px')) {
      computedHeight = parseInt(style.height);
    } else if (typeof style.height === 'string') {
      computedHeight = parseInt(style.height);
    }
  }
  
  // 如果height是字符串100%用容器高度
  if (typeof height === 'string' && height === '100%') {
    computedHeight = '100%';
  }

  // 获取label显示值的函数
  const getLabelValue = (d) => {
    if (d.value === 0) return '';
    if (d.displayValue !== undefined && d.displayValue !== null) {
      return d.displayValue;
    }
    return Math.round(d.value);
  };

  // 获取总计显示值的函数
  const getTotalDisplayValue = (company, totals, validData) => {
    // 查找该公司是否有displayValue数据
    const companyItems = validData.filter(item => item.company === company);
    const hasDisplayValue = companyItems.some(item => item.displayValue !== undefined && item.displayValue !== null);
    
    if (hasDisplayValue) {
      // 如果有displayValue，尝试累加显示值
      const totalValue = totals[company];
      return Number(totalValue).toFixed(2); // 转为数字后保留2位小数
    }
    
    return Number(totals[company]).toFixed(2); // 转为数字后保留2位小数
  };

 
  
  useEffect(() => {
   
    
    if (!container.current) { 
      return;
    }
    if (!data || data.length === 0) { 
      return;
    }
    
    // 清空容器
    container.current.innerHTML = ''; 
    
    // 简单的数据验证和清理，支持displayValue字段
    const validData = data.filter(item => 
      item && 
      item.company && 
      item.type && 
      typeof item.value === 'number' &&
      !isNaN(item.value)
    ).map(item => ({
      ...item,
      company: String(item.company || ''),
      type: String(item.type || ''),
      value: Number(item.value || 0),
      // 保留displayValue字段
      displayValue: item.displayValue
    }));
    
    if (validData.length === 0) { 
      return;
    }
     
    
    let chart = null;
    let animationFrame = null;
    
    // 使用requestAnimationFrame确保DOM完全准备好
    const createChart = () => { 
      
      if (!container.current) { 
        return;
      }
      
      // 计算最终高度
      let finalHeight = computedHeight;
      if (computedHeight === '100%') {
        const containerHeight = container.current.offsetHeight; 
        // 如果容器高度为0，说明还没有准备好，延迟重试
        if (containerHeight <= 0) { 
          animationFrame = requestAnimationFrame(createChart);
          return;
        }
        finalHeight = containerHeight;
      }
       
      
      try { 
        chart = new Chart({
          container: container.current,
          autoFit: true,
          height: finalHeight,
          title: {
            title: title,
            titleFontSize: 24,
          },interaction: {
            tooltip: {
              css: {
                '.g2-tooltip-list-item': {
                  'font-size': '18px',
                },
              },
            },
          },
        });
         
        
        // 计算缩放比例
        const baseHeight = 320;
        const scaleRatio = finalHeight / baseHeight;
        const isEnlarged = scaleRatio > 1.1;
         
        
        // 计算公司总计
        const totals = {};
        validData.forEach(d => {
          if (!totals[d.company]) totals[d.company] = 0;
          totals[d.company] += d.value;
        });
        
        // 为每个数据项添加同公司的所有数据，用于tooltip显示
        const dataWithCompanyInfo = validData.map(item => {
          // 获取同公司的所有数据
          const companyData = validData.filter(compItem => compItem.company === item.company);
          
          return {
            ...item,
            companyTotal: Number(totals[item.company]).toFixed(2),
            companyAllData: companyData.map(compItem => ({
              type: compItem.type,
              value: compItem.value,
              displayValue: compItem.displayValue
            }))
          };
        });
         
        
        // 获取所有图例类型
        const allTypes = [...new Set(validData.map(item => item.type))];
        
        chart
          .interval()
          .data(dataWithCompanyInfo)
          .encode('x', 'company')
          .encode('y', 'value')
          .encode('color', 'type')
          .transform({ type: 'stackY' })
          .axis({ y: false, x: { title: false,labelFill:'#000000',labelFillOpacity:2 } })
          .label({
            text: (d) => getLabelValue(d),
            style:   {
              fontSize: 14,}
          })
          .legend('color', {
            position: 'top',
            maxRows: 1,
          })
          .tooltip({
            items: [
              // 为每个图例类型创建一个tooltip项
              ...allTypes.map(type => (datum) => {
                // 从预存储的companyAllData中获取数据
                const typeData = datum.companyAllData && datum.companyAllData.find(item => item.type === type);
                if (!typeData) return null;
                  
                  const displayValue = typeData.displayValue !== undefined && typeData.displayValue !== null 
                    ? typeData.displayValue 
                    : Number(typeData.value || 0).toFixed(2);
                  
                  return {
                    name: String(type || ''),
                    value: displayValue
                  };
                }
              ),
              (datum) => ({
                name: '总计',
                value: datum.companyTotal
              })
            ]
          });
        
        // 使用interaction方法配置tooltip的位置和挂载属性
        chart.interaction('tooltip', {
          mount: 'body', 
          position: 'bottom',
          offset: [40, 10],
          css: {
            '.g2-tooltip': {
              'z-index': '99999',
              'max-height': 'none',
              'overflow': 'visible',
              'font-size': '14px',
            },
            '.tooltip-item': {
              'font-size': '14px',
            },
          }
        });
        
        // 顶部总计标签 - 显示在每个堆叠柱状图的顶部
        chart
          .text()
          .data(Object.keys(totals).map(company => ({
            company: String(company),
            total: getTotalDisplayValue(company, totals, validData),
            totalValue: totals[company] // 使用实际的总计数值进行定位
          })))
          .encode('x', 'company')
          .encode('y', 'totalValue') // 使用总计数值进行Y轴定位
          .encode('text', 'total')
          .tooltip(false)
          .style({
            textAlign: 'center',
            fontSize: isEnlarged ? Math.max(12, Math.min(20, 12 * scaleRatio)) : 14,
            fontWeight: 'bold',
            fill: '#333',
            dy: isEnlarged ? -5 * scaleRatio : -5,
          });
          chart
          .labelTransform({ type: 'overlapHide' })
          .labelTransform({ type: 'contrastReverse' });
         
        chart.render();

        // 添加 resize 监听器，当容器尺寸变化时重新渲染
        const resizeObserver = new ResizeObserver(() => {
          if (chart && !chart.destroyed) {
            chart.render();
          }
        });
        
        if (container.current) {
          resizeObserver.observe(container.current);
        }

        return () => {
          resizeObserver.disconnect();
          if (animationFrame) {
            cancelAnimationFrame(animationFrame);
          }
          if (chart) {
            try {
              chart.destroy(); 
            } catch (error) {
              console.error('Chart destroy error:', error);
            }
          }
        };
      } catch (error) {
        console.error('Chart creation/render error:', error);
        console.error('Error stack:', error.stack);
      }
    };
    
    // 开始创建图表 
    animationFrame = requestAnimationFrame(createChart);
    
    return () => {
      if (animationFrame) {
        cancelAnimationFrame(animationFrame);
      }
      if (chart) {
        try {
          chart.destroy(); 
        } catch (error) {
          console.error('Chart destroy error:', error);
        }
      }
    };
  }, [data, computedHeight, style, isFullscreen, isIndividualFullscreen]);
  
  return <div ref={container} style={{ width: '100%', height: '100%' }} />;
};

export default AssetStackedColumn; 