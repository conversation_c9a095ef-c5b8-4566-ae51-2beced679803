from flask import Blueprint, jsonify, request, current_app
from ..services.metric_definition_service import MetricDefinitionService
from ..utils.error_handler import handle_exception

bp = Blueprint('metric_definitions', __name__, url_prefix='/api/metrics/definitions')

@bp.route('', methods=['GET'])
def get_metric_definitions():
    """获取指标定义"""
    table_name = request.args.get('table', 'L2Metrics')
    report_date = request.args.get('reportDate')
    try:
        definitions = MetricDefinitionService.get_definitions(table_name, report_date=report_date)
        if isinstance(definitions, dict) and 'error' in definitions:
            current_app.logger.error(f"Service error getting definitions: {definitions['error']}")
            return jsonify(definitions), 500
        return jsonify(definitions)
    except Exception as e:
        error_response, status_code = handle_exception(e, "获取指标定义")
        return jsonify(error_response), status_code
