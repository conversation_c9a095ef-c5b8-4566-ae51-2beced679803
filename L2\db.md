# 数据库表结构设计

## 核心表结构

### 1. **L2BenchmarkIndicators**
- 存储财务指标的基准定义和公式信息。
- 关键字段：
  - `NO`: 主键，自增。
  - `IndicatorNameCN`: 指标中文名称。
  - `IndicatorNameEN`: 指标英文名称。
  - `IndicatorDesc`: 指标描述。
  - `FormulaDesc`: 公式描述。
  - `Formula`: 实际用于计算的公式。
  - `LowerBound`: 指标下限值。
  - `UpperBound`: 指标上限值。
  - `RangeValue`: 范围值（例如范围说明）。
  - `Category`: 指标分类。
  - `IsActive`: 是否启用，默认为 1。
  - `CreateTime`: 创建时间，默认为当前时间。
  - `UpdateTime`: 更新时间，默认为当前时间。
- 约束：唯一约束 `(IndicatorNameEN, IndicatorNameCN)`。

### 2. **L2Metrics**
- 存储实际计算出的财务指标结果。
- 关键字段：
  - `NO`: 主键，自增。
  - `TickerSymbol`: 股票代码。
  - `CompanyName`: 公司名称。
  - `ReportDate`: 报告日期。
  - `IndicatorNameCN`: 指标中文名称。
  - `IndicatorNameEN`: 指标英文名称。
  - `IndicatorDesc`: 指标描述。
  - `FormulaDesc`: 公式描述。
  - `Formula`: 计算公式。
  - `FormulaEN`: 英文版公式。
  - `CalculatedValue`: 计算结果值。
  - `LowerBound`: 指标下限值。
  - `UpperBound`: 指标上限值。
  - `RangeValue`: 范围值。
  - `EvaluationResult`: 评估结果（如“优秀”、“良好”等）。
  - `Remarks`: 备注。
  - `CreateTime`: 创建时间，默认为当前时间。
  - `UpdateTime`: 更新时间，默认为当前时间。
- 约束：唯一约束 `(TickerSymbol, ReportDate, IndicatorNameEN)`。

### 3. **L2MetricsErrorLog**
- 存储计算失败的财务指标日志。
- 关键字段：
  - `NO`: 主键，自增。
  - `TickerSymbol`: 股票代码。
  - `ReportDate`: 报告日期。
  - `IndicatorNameCN`: 指标中文名称。
  - `IndicatorNameEN`: 指标英文名称。
  - `IndicatorDesc`: 指标描述。
  - `FormulaDesc`: 公式描述。
  - `Formula`: 公式。
  - `FormulaEN`: 英文版公式。
  - `ErrorMessage`: 错误信息。
  - `ProcessedFormula`: 出错时处理的公式。
  - `CreateTime`: 创建时间，默认为当前时间。

### 4. **L2MetricsCalculationLog**
- 存储每次财务指标计算的日志信息。
- 关键字段：
  - `ID`: 主键，自增。
  - `TickerSymbol`: 股票代码。
  - `ReportDate`: 报告日期。
  - `CalculationTime`: 计算时间，默认为当前时间。
  - `SuccessCount`: 成功计算的指标数量。
  - `FailureCount`: 失败的指标数量。
  - `ExecutionTime`: 执行时间（秒）。
  - `LogMessage`: 日志信息。

## 表间关系

- L2BenchmarkIndicators 与 CompanyInfo 关联，用于确定公司对应的财务指标基准。
- CompanyInfo 与 WindMetrics 关联，获取公司基本财务数据。
- WindMetrics 与 L2Metrics 关联，用于计算复杂指标。
- L2Metrics 与 L2MetricsErrorLog 和 L2MetricsCalculationLog 关联，记录计算失败日志和统计信息。

## 总结

数据库设计包含四个核心表：
- L2BenchmarkIndicators：存储所有需要计算的指标及其公式和基准。
- L2Metrics：保存每次计算的结果。
- L2MetricsErrorLog：记录计算失败的详细日志。
- L2MetricsCalculationLog：记录每次计算的整体统计信息。