
from decimal import Decimal, InvalidOperation
import math
from WindPy import w
import uuid
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor
from database.db_handler import DatabaseHandler
from logging_config import setup_logging
import os

logger = setup_logging()

def is_none_or_nan(value):
    """轻量级方法：检查值是否为None或NaN
    替代pandas的pd.isna，避免引入pandas依赖
    """
    if value is None:
        return True
    if isinstance(value, float) and math.isnan(value):
        return True
    return False

def update_yoy_data():
    db = DatabaseHandler()
    w.start()
    
    # 查询2025年且创建时间在20250318之前的指标
    select_metrics_query = "SELECT Name, WindCode FROM MetricsDefinition "
    try:
        with db as db:
            cursor = db.connection.cursor()
            cursor.execute(select_metrics_query)
            metrics = cursor.fetchall()
            logger.info(f"成功获取 {len(metrics)} 条2025年且创建时间在20250318之前的指标定义")
    except Exception as e:
        logger.error(f"获取2025年且创建时间在20250318之前的指标定义失败: {str(e)}", exc_info=True)
        return
    
    # 查询api日志表
    select_log_query = "SELECT TickerSymbol, RptDate FROM WindApiCallLog WHERE YEAR(RptDate) = 2023"
    try:
        with db as db:
            cursor = db.connection.cursor()
            cursor.execute(select_log_query)
            log_records = cursor.fetchall()
    except Exception as e:
        logger.error(f"查询api日志表失败: {str(e)}", exc_info=True)
        return
    
    indicator_codes = [metric[1].strip() for metric in metrics]
    fields_str = ",".join(indicator_codes)
    
    def check_company_date_execution(company_symbol, date_obj):
        record_file = 'company_date_execution_record.txt'
        if os.path.exists(record_file):
            with open(record_file, 'r') as f:
                lines = f.readlines()
                for line in lines:
                    symbol, date_str = line.strip().split(',')
                    if symbol == company_symbol and date_str == date_obj.strftime('%Y-%m-%d'):
                        return True
        return False
    def _check_report_exists( symbol, rpt_date):
        """检查报告是否已发布"""
        result = w.wss(
            symbol,
            "stm_issuingdate",
            f"rptDate={rpt_date};rptType=1"
        )

        # 验证数据有效性
        if result.ErrorCode != 0 or is_none_or_nan(result.Data[0][0]):
            logger.debug(
                f"无效报告日期: {symbol} {rpt_date} | Wind返回: {result.Data[0][0]}")
            return False
        return True

    def record_company_date_execution(company_symbol, date_obj):
        record_file = 'company_date_execution_record.txt'
        with open(record_file, 'a') as f:
            f.write(f'{company_symbol},{date_obj.strftime("%Y-%m-%d")}\n')
    
    # 查询股票代码对应的公司信息
    select_company_query = "SELECT TickerSymbol, CompanyId FROM CompanyInfo"
    try:
        with db as db:
            cursor = db.connection.cursor()
            cursor.execute(select_company_query)
            company_records = cursor.fetchall()
            company_dict = {record[0]: record[1] for record in company_records}
            logger.info(f"成功获取 {len(company_dict)} 条股票代码对应的公司信息")
    except Exception as e:
        logger.error(f"获取股票代码对应的公司信息失败: {str(e)}", exc_info=True)
        return
    
    for symbol, date_obj in log_records:
        if str(date_obj).endswith('1231'):
            continue
        try:
            if check_company_date_execution(symbol, date_obj):
                logger.info(f'{symbol} 在 {date_obj.strftime("%Y-%m-%d")} 已经执行过，跳过。')
                continue
            prev_year_date = date_obj.replace(year=date_obj.year - 1)
            rpt_date = prev_year_date.strftime("%Y%m%d")
            
            if not _check_report_exists(symbol, rpt_date):
                logger.warning(f"跳过未发布报告: {symbol} {rpt_date}")
                return None
          
            
            # 查询指标日期3的数据（同比数据）
            yoy_data = get_wind_data(symbol, fields_str, rpt_date, 3) or {}
            with DatabaseHandler() as local_db:
                cursor = local_db.connection.cursor()
                insert_metrics_data(cursor,  yoy_data, date_obj, symbol, company_dict)
                local_db.connection.commit()
            
            # 记录执行完成状态
            with open('execution_record.txt', 'w') as f:
                f.write('completed')
            logger.info('数据补充完成。')
        except Exception as e:
            logger.error(f'执行过程中出现错误: {str(e)}', exc_info=True)
            # 记录执行中断状态
            with open('execution_record.txt', 'w') as f:
                f.write('interrupted')
def _check_report_exists( symbol, rpt_date):
        """检查报告是否已发布"""
        result = w.wss(
            symbol,
            "stm_issuingdate",
            f"rptDate={rpt_date};rptType=1"
        )

        # 验证数据有效性
        if result.ErrorCode != 0 or pd.isna(result.Data[0][0]):
            logger.debug(
                f"无效报告日期: {symbol} {rpt_date} | Wind返回: {result.Data[0][0]}")
            return False
        logger.debug("找到同比报告")
        return True

def get_wind_data(TickerSymbol, fields_str, rpt_date, rpt_type):
    if not _check_report_exists(TickerSymbol, rpt_date):
        logger.warning(f"未找到发布报告: {TickerSymbol} {rpt_date}")
        return None
    _check_report_exists
    result = w.wss(
        TickerSymbol,
        fields_str,
        f"unit=1;rptDate={rpt_date};rptType={rpt_type};tradeDate={rpt_date}"
    )
    if result.ErrorCode != 0:
        print(f'error code: {result}')
        return None
    
    data = {}
    for i, field in enumerate(result.Fields):
        value = result.Data[i][0]
        if is_none_or_nan(value):
            value = None
        data[field] = value
    
    if all(v is None for v in data.values()):
        logger.warning(f"全空数据告警: {TickerSymbol} {rpt_date} 所有指标值均为空")
        return None
    return data


def insert_metrics_data(cursor,  yoy_data, date_obj, symbol, company_dict):
    invalid_rows = []
    yoy_valid_data = []
    
    for code in yoy_data.keys():
        
        raw_yoy = yoy_data.get(code)
        yoy_dec, is_yoy_valid = safe_convert_decimal(raw_yoy, (18, 6))
        
        if not all([ is_yoy_valid]):
            invalid_rows.append({
                "wind_code": code, 
                "yoy_value": yoy_dec,
                "reason": f"Value valid: YoY valid: {is_yoy_valid}"
            })
         
        # 在插入数据时使用 company_dict 获取 CompanyId
        yoy_valid_data.append((
           
            code, 
            date_obj,
            yoy_dec, 
            symbol 
        ))
    
    if invalid_rows:
        logger.warning("发现 %d 条无效数据，样例：", len(invalid_rows))
        for row in invalid_rows[:3]:
            logger.warning("WindCode=%s |   YoYValue=%s | 原因=%s",
                           row['wind_code'], row['yoy_value'], row['reason'])
    
    if yoy_valid_data:
        # 修改为更新操作
        for data in yoy_valid_data:
            if data[2] is None:
                continue
            update_query = "UPDATE WindMetrics SET YoYValue = ?,UpdateDate=GetDate() WHERE WindCode = ? AND Date = ? AND TickerSymbol = ? "
            cursor.execute(update_query, (data[2], data[0], data[1], data[3]))
        logger.info("成功更新 %d 条 YoYValue 有效数据", len(yoy_valid_data))
    else:
        logger.warning("没有有效 YoYValue 数据需要插入")


def safe_convert_decimal(value, precision=(18, 6)):
    if value is None:
        return None, True
    
    try:
        dec_value = Decimal(str(value))
    except (TypeError, InvalidOperation):
        return None, False
    return dec_value, True


def convert_large_number(value):
    if value is None:
        return None
    try:
        if isinstance(value, str) and 'e' in value.lower():
            d = Decimal(value.lower().replace('e', 'E'))
            return format(d, 'f').rstrip('0').rstrip('.')
        elif isinstance(value, (float, int)):
            return format(Decimal(str(value)), 'f').rstrip('0').rstrip('.')
        else:
            return str(value)
    except:
        return str(value)[:500]


if __name__ == "__main__":
    update_yoy_data()