from ..data_access import execute_query

class ReportTimeRepository:
    @staticmethod
    def get_report_times():
        """获取定期报告公布时间数据"""
        query = """
            SELECT 
                _compnay.CompanyName,
                _metrics.date,
                _metrics.OriValue,
                _compnay.TickerSymbol 
            FROM WindMetrics _metrics 
            LEFT JOIN CompanyInfo _compnay ON _compnay.TickerSymbol = _metrics.TickerSymbol  
            WHERE _metrics.WindCode = 'stm_issuingdate' 
            ORDER BY _compnay.CompanyName, _metrics.date DESC
        """
        return execute_query(query)
    
    @staticmethod
    def get_latest_l2metrics_report_date():
        """获取L2Metrics表中最新的报告日期"""
        query = """
            SELECT TOP 1 
                ReportDate,
                YEAR(ReportDate) as Year,
                DATEPART(QUARTER, ReportDate) as Quarter
            FROM L2Metrics 
            WHERE ReportDate IS NOT NULL
            ORDER BY ReportDate DESC
        """
        result = execute_query(query)
        if result:
            return result[0]
        return None 