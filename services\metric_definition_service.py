from flask import current_app
from ..repositories.metric_repository import MetricRepository

class MetricDefinitionService:
    @staticmethod
    def get_definitions(table_name, report_date=None):
        """获取指标定义"""
        try:
            metrics_list = MetricRepository.get_active_metrics()
            return [{
                "name": m["name"],
                "code": m["code"],
                "description": m["description"],
                "formula": m["formula"],
                "formula_desc": m["formula_desc"],
                "translated_formula": m.get("translated_formula", ""),
                "value_type": m["value_type"],
                "unit": m["unit"],
            } for m in metrics_list]
        except Exception as e:
            current_app.logger.error(f"Error getting metric definitions: {e}")
            return {"error": str(e)}
