@echo off
chcp 65001 >nul
cd /d "D:\Codes\Fin\windgather"

echo ========================================
echo 整合数据处理程序 - 定时任务执行
echo 执行时间: %date% %time%
echo ========================================

REM 记录开始时间
echo 任务开始执行: %date% %time% >> task_log.txt

REM 执行程序
integrated_processor.exe

REM 记录结束时间
echo 任务执行完成: %date% %time% >> task_log.txt
echo. >> task_log.txt

REM 如果程序执行失败，记录错误
if errorlevel 1 (
    echo 任务执行失败，错误代码: %errorlevel% >> task_log.txt
    echo. >> task_log.txt
)
