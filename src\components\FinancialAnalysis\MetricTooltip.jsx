import React from 'react';
import { Tooltip } from 'antd';
import { formatMetricValue } from './utils';

/**
 * 指标提示组件
 *
 * @param {Object} props
 * @param {Object} props.metric - 指标对象
 * @param {Object} props.metricData - 指标数据
 * @param {string} props.dataUnit - 数据单位
 * @param {number} props.convertedValue - 转换后的值
 * @param {string} props.children - 子元素
 * @param {Object} props.style - 样式对象
 * @returns {JSX.Element}
 */
const MetricTooltip = ({
  metric,
  metricData,
  dataUnit = "元",
  convertedValue,
  children,
  style = {}
}) => {
  const value = metricData ? metricData.value : undefined;
  const evaluationResult = metricData ? metricData.evaluation_result : undefined;
  const translatedFormula = metricData ? metricData.translated_formula : undefined;

  // 如果metric为undefined，返回简单的显示
  if (!metric) {
    return <span style={style}>{children}</span>;
  }

  // 根据数据单位显示不同的值
  const displayOriginalValue = formatMetricValue(value, metric.value_type, dataUnit, metric);
  const displayConvertedValue = formatMetricValue(convertedValue, metric.value_type, dataUnit, metric);

  // 获取单位显示文本
  const getUnitText = () => {
    if (metric.value_type && metric.value_type.trim() === "%") {
      return "";
    }
    return dataUnit;
  };

  return (
    <Tooltip
      title={
        <div style={{ maxWidth: 500 }}>
          <div>
            <strong>指标名称:</strong> {metric.name}
          </div>
          <div>
            <strong>原始值:</strong>{" "}
            {displayOriginalValue} 
          </div>
          <div>
            <strong>显示值:</strong>{" "}
            {displayConvertedValue} {getUnitText()}
          </div>
          <div>
            <strong>计算公式:</strong>{" "}
            {translatedFormula || "无计算公式"}
          </div>
          <div>
            <strong>评估结果:</strong> {evaluationResult || "无评估"}
          </div>
        </div>
      }
      styles={{ body: { maxWidth: 500 } }}
    >
      {children}
    </Tooltip>
  );
};

/**
 * 指标标题提示组件
 *
 * @param {Object} props
 * @param {Object} props.metric - 指标对象
 * @returns {JSX.Element}
 */
export const MetricHeaderTooltip = ({ metric }) => {
  // 如果metric为undefined，返回简单的显示
  if (!metric) {
    return <span>未知指标</span>;
  }

  return (
    <Tooltip
      title={
        <div style={{ maxWidth: 500 }}>
          <div>
            <strong>指标名称:</strong> {metric.name || "未知"}
          </div>
          <div>
            <strong>公式说明:</strong> {metric.formula_desc || "无说明"}
          </div>
          <div>
            <strong>公式:</strong> {metric.formula || "无公式"}
          </div>
          <div>
            <strong>指标解释:</strong> {metric.description || "无描述"}
          </div>
        </div>
      }
      styles={{ body: { maxWidth: 500 } }}
    >
      <span style={{ cursor: "help" }}>{metric.name || "未知指标"}</span>
    </Tooltip>
  );
};

export default MetricTooltip;
