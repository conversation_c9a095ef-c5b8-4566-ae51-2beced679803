import React from 'react';

/**
 * props: data: Array<{
 *   company: string,
 *   value: number, // 比重
 *   yoyValue: number, // 同比
 *   displayValue?: string // 格式化比重
 * }>
 */
const TwoFundIncomePanel = ({ data = [], title = '两金占营业收入比重及同比变动', rowHeight = 40 }) => (
  <div>
    <div style={{ fontWeight: 'bold',
     marginTop: 50,
     minHeight: 40,
     fontSize: 14,
     padding: 18,
     whiteSpace: 'nowrap',
     overflow: 'hidden',
     textOverflow: 'ellipsis',
     }}>{title}</div>
    {data.map(item => (
      <div
        key={item.company}
        style={{
          display: 'flex',
          alignItems: 'center',
          height: rowHeight,
          padding: '0 16px',
          marginBottom: 22, // 行间距
          borderRadius: 6, // 可选：圆角
          background: '#fafbfc', // 可选：淡背景
        }}
      >
        <span
          style={{
            width: 80, // 稍微宽一点
            textAlign: 'right',
            color: '#222',
            fontWeight: 600,
            fontSize: 16,
          }}
        >
          {item.displayValue || ''}
        </span>
        <span
          style={{
            width: 60, // 稍微宽一点
            color: item.yoyValue > 0 ? 'red' : item.yoyValue < 0 ? 'green' : '#666',
            fontWeight: 600,
            marginLeft: 16, // 间距更大
            fontSize: 16,
            display: 'flex',
            alignItems: 'center',
          }}
        >
          {item.yoyValue > 0 && '▲'}
          {item.yoyValue < 0 && '▼'}
          {item.yoyValue !== undefined ? Math.abs(item.yoyValue).toFixed(1) + '%' : ''}
        </span>
      </div>
    ))}
  </div>
);

export default TwoFundIncomePanel; 