-- 创建L2BenchmarkIndicators表（如果不存在）
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'L2BenchmarkIndicators')
BEGIN
    CREATE TABLE L2BenchmarkIndicators (
        ID INT IDENTITY(1,1) PRIMARY KEY,                -- 主键ID
        IndicatorNameEN NVARCHAR(100) NOT NULL,          -- 指标英文名称
        IndicatorNameCN NVARCHAR(100) NOT NULL,          -- 指标中文名称
        IndicatorDesc NVARCHAR(MAX),                     -- 公式解释
        Formula NVARCHAR(MAX),                           -- 公式中文
        FormulaDesc NVARCHAR(MAX),                       -- 公式
        LowerBound DECIMAL(38, 6),                       -- 下限值
        UpperBound DECIMAL(38, 6),                       -- 上限值
        RangeValue NVARCHAR(100),                        -- 范围值描述
        Category NVARCHAR(50),                           -- 指标类别（盈利能力/偿债能力/运营能力/成长能力/现金流量）
        IsActive BIT DEFAULT 1,                          -- 是否启用
        CreateTime DATETIME DEFAULT GETDATE(),           -- 创建时间
        UpdateTime DATETIME DEFAULT GETDATE(),           -- 更新时间
        CONSTRAINT UK_L2BenchmarkIndicators UNIQUE (IndicatorNameEN, IndicatorNameCN)  -- 唯一约束
    );
    
    PRINT 'L2BenchmarkIndicators表已创建';
END
ELSE
BEGIN
    PRINT 'L2BenchmarkIndicators表已存在';
END

-- 修改L2Metrics表，添加IndicatorDesc和Formula字段（如果不存在）
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('L2Metrics') AND name = 'IndicatorDesc')
BEGIN
    ALTER TABLE L2Metrics ADD IndicatorDesc NVARCHAR(MAX);
    PRINT 'L2Metrics表已添加IndicatorDesc字段';
END
ELSE
BEGIN
    PRINT 'L2Metrics表的IndicatorDesc字段已存在';
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('L2Metrics') AND name = 'Formula')
BEGIN
    ALTER TABLE L2Metrics ADD Formula NVARCHAR(MAX);
    PRINT 'L2Metrics表已添加Formula字段';
END
ELSE
BEGIN
    PRINT 'L2Metrics表的Formula字段已存在';
END

-- 修改L2MetricsErrorLog表，添加IndicatorDesc和Formula字段（如果不存在）
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'L2MetricsErrorLog')
BEGIN
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('L2MetricsErrorLog') AND name = 'IndicatorDesc')
    BEGIN
        ALTER TABLE L2MetricsErrorLog ADD IndicatorDesc NVARCHAR(MAX);
        PRINT 'L2MetricsErrorLog表已添加IndicatorDesc字段';
    END
    ELSE
    BEGIN
        PRINT 'L2MetricsErrorLog表的IndicatorDesc字段已存在';
    END

    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('L2MetricsErrorLog') AND name = 'Formula')
    BEGIN
        ALTER TABLE L2MetricsErrorLog ADD Formula NVARCHAR(MAX);
        PRINT 'L2MetricsErrorLog表已添加Formula字段';
    END
    ELSE
    BEGIN
        PRINT 'L2MetricsErrorLog表的Formula字段已存在';
    END
END

-- 插入示例数据
IF (SELECT COUNT(*) FROM L2BenchmarkIndicators) = 0
BEGIN
    -- 盈利能力指标
    INSERT INTO L2BenchmarkIndicators (IndicatorNameEN, IndicatorNameCN, IndicatorDesc, Formula, FormulaDesc, LowerBound, UpperBound, RangeValue, Category)
    VALUES 
    ('gross_margin', '毛利率（%）', '反映企业产品的盈利能力', '=(oper_rev - oper_cost) / oper_rev× 100%', '=(营业收入 - 营业成本) / 营业收入× 100%', 0.05, 0.30, '5%-30%', '盈利能力'),
    ('net_profit_margin', '净利率（%）', '反映企业的整体盈利能力', '=net_profit_is / oper_rev × 100%', '=净利润 / 营业收入 × 100%', -0.10, 0.15, '-10%-15%', '盈利能力'),
    ('roe', '净资产收益率（%）', '反映股东权益的收益水平', 'np_belongto_parcomsh/[(eqy_belongto_parcomsh[上期末]+eqy_belongto_parcomsh)/2]× 100', '归属于母公司股东的净利润/[(期初归属于母公司股东的权益+期末归属于母公司股东的权益)/2]× 100', -0.05, 0.15, '-5%-15%', '盈利能力'),
    
    -- 偿债能力指标
    ('debt_to_assets_ratio', '资产负债率（%）', '反映企业的长期偿债能力', '=tot_liab/tot_assets× 100%', '=负债总额/资产总额× 100%', 0.51, 0.86, '51%-86%', '偿债能力'),
    ('current_ratio', '流动比率', '反映企业的短期偿债能力', '= tot_cur_assets / tot_cur_liab', '= 流动资产合计 / 流动负债合计', 1.00, 2.00, '1.0-2.0', '偿债能力'),
    ('quick_ratio', '速动比率', '反映企业的即时偿债能力', '= (tot_cur_assets - inventories) / tot_cur_liab', '= (流动资产合计 - 存货) / 流动负债合计', 0.50, 1.50, '0.5-1.5', '偿债能力'),
    
    -- 运营能力指标
    ('inventory_turnover', '存货周转率', '反映企业存货周转速度', 'oper_cost/[(inventories[上期末]+inventories)/2]', '营业成本/[(期初存货+期末存货)/2]', 0.10, 20.00, '0.1-20.0', '运营能力'),
    ('receivables_turnover', '应收账款周转率', '反映企业应收账款周转速度', 'oper_rev/[(acct_rcv[上期末]+acct_rcv)/2]', '营业收入/[(期初应收账款+期末应收账款)/2]', 1.50, 12.50, '1.5-12.5', '运营能力'),
    ('total_asset_turnover', '总资产周转率', '反映企业总资产周转速度', 'oper_rev/[(tot_assets[上期末]+tot_assets)/2]× 100', '营业收入/[(期初资产总额+期末资产总额)/2]× 100', -0.10, 1.50, '-0.1-1.5', '运营能力'),
    
    -- 成长能力指标
    ('oper_rev_growth_rate', '营业收入增长率（%）', '反映企业营业收入的增长速度', '=(oper_rev - oper_rev[上期]) / oper_rev[上期]×  100%', '=(本期营业收入 - 上期营业收入) / 上期营业收入×  100%', -0.05, 0.15, '-5%-15%', '成长能力'),
    ('tot_profit_growth_rate', '利润总额增长率（%）', '反映企业利润总额的增长速度', '=(tot_profit - tot_profit[上期]) / abs(tot_profit[上期])×  100%', '=(本期利润总额 - 上期利润总额) / 上期利润总额绝对值×  100%', -0.10, 0.10, '-10%-10%', '成长能力'),
    
    -- 现金流量指标
    ('cash_earnings_coverage', '盈余现金保障倍数', '反映企业经营活动产生的现金流量与净利润的比率', '=net_cash_flows_oper_act/net_profit_is', '=经营活动产生的现金流量净额/净利润', -3.00, 3.00, '-3.0-3.0', '现金流量'),
    ('operating_cash_ratio', '营业现金比率（%）', '反映企业经营活动产生的现金流量与营业收入的比率', '= net_cash_flows_oper_act / oper_rev × 100%', '= 经营活动产生的现金流量净额 / 营业收入 × 100%', -0.15, 0.20, '-15%-20%', '现金流量'),
    ('cash_to_current_liabilities_ratio', '现金流动负债比率（％）', '反映企业经营活动产生的现金流量与流动负债的比率', '=net_cash_flows_oper_act／tot_cur_liabx100％', '=经营活动产生的现金流量净额／流动负债合计x100％', -0.30, 0.25, '-30%-25%', '现金流量'),
    
    -- 研发指标
    ('rd_intensity', '研发投入强度（%）', '反映企业研发投入占营业收入的比例', '= rd_exp / oper_rev × 100%', '= 研发费用 / 营业收入 × 100%', 0.015, 0.07, '1.5%-7%', '研发能力'),
    ('two_fund_to_current_assets_ratio', '两金净额占流动资产比率（%）', '反映企业两金占用情况', '=two_fund_net / tot_cur_assets × 100%', '=两金净额 / 流动资产合计 × 100%', 0.07, 0.55, '7%-55%', '运营能力');
    
    PRINT '已插入示例数据到L2BenchmarkIndicators表';
END
ELSE
BEGIN
    PRINT 'L2BenchmarkIndicators表已有数据，跳过插入示例数据';
END

PRINT '所有操作完成';
