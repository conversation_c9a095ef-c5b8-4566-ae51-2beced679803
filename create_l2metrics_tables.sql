-- 创建L2Metrics表（如果不存在）
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'L2Metrics')
BEGIN
    CREATE TABLE L2Metrics (
        ID INT IDENTITY(1,1) PRIMARY KEY,                -- 主键ID
        TickerSymbol NVARCHAR(20) NOT NULL,              -- 股票代码
        CompanyName NVARCHAR(100) NOT NULL,              -- 公司名称
        ReportDate DATE NOT NULL,                        -- 报告日期
        IndicatorNameCN NVARCHAR(100) NOT NULL,          -- 指标中文名称
        IndicatorNameEN NVARCHAR(100) NOT NULL,          -- 指标英文名称
        FormulaDesc NVARCHAR(MAX),                       -- 公式中文描述
        FormulaEN NVARCHAR(MAX),                         -- 公式英文描述
        CalculatedValue DECIMAL(38, 6),                  -- 计算结果
        LowerBound DECIMAL(38, 6),                       -- 下限值
        UpperBound DECIMAL(38, 6),                       -- 上限值
        RangeValue NVARCHAR(100),                        -- 范围值描述
        EvaluationResult NVARCHAR(50),                   -- 评估结果（优秀/良好/一般/较差）
        CalculationTime DATETIME DEFAULT GETDATE(),      -- 计算时间
        Remarks NVARCHAR(MAX),                           -- 备注
        CreateTime DATETIME DEFAULT GETDATE(),           -- 创建时间
        CONSTRAINT UK_L2Metrics UNIQUE (TickerSymbol, ReportDate, IndicatorNameEN)  -- 唯一约束
    );
    
    PRINT 'L2Metrics表已创建';
END
ELSE
BEGIN
    PRINT 'L2Metrics表已存在';
END

-- 创建L2MetricsErrorLog表（如果不存在）
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'L2MetricsErrorLog')
BEGIN
    CREATE TABLE L2MetricsErrorLog (
        ID INT IDENTITY(1,1) PRIMARY KEY,                -- 主键ID
        TickerSymbol NVARCHAR(20) NOT NULL,              -- 股票代码
        ReportDate DATE NOT NULL,                        -- 报告日期
        IndicatorNameCN NVARCHAR(100) NOT NULL,          -- 指标中文名称
        IndicatorNameEN NVARCHAR(100) NOT NULL,          -- 指标英文名称
        FormulaDesc NVARCHAR(MAX),                       -- 公式中文描述
        FormulaEN NVARCHAR(MAX),                         -- 公式英文描述
        ErrorMessage NVARCHAR(MAX),                      -- 错误信息
        ProcessedFormula NVARCHAR(MAX),                  -- 处理后的公式
        ErrorTime DATETIME DEFAULT GETDATE()             -- 错误时间
    );
    
    PRINT 'L2MetricsErrorLog表已创建';
END
ELSE
BEGIN
    PRINT 'L2MetricsErrorLog表已存在';
END

-- 创建L2MetricsCalculationLog表（如果不存在）
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'L2MetricsCalculationLog')
BEGIN
    CREATE TABLE L2MetricsCalculationLog (
        ID INT IDENTITY(1,1) PRIMARY KEY,                -- 主键ID
        TickerSymbol NVARCHAR(20) NOT NULL,              -- 股票代码
        ReportDate DATE NOT NULL,                        -- 报告日期
        CalculationTime DATETIME DEFAULT GETDATE(),      -- 计算时间
        SuccessCount INT,                                -- 成功计算的指标数量
        FailureCount INT,                                -- 计算失败的指标数量
        ExecutionTime DECIMAL(10, 3),                    -- 执行时间（秒）
        LogMessage NVARCHAR(MAX)                         -- 日志信息
    );
    
    PRINT 'L2MetricsCalculationLog表已创建';
END
ELSE
BEGIN
    PRINT 'L2MetricsCalculationLog表已存在';
END

-- 创建视图，用于查看最新的指标计算结果
IF NOT EXISTS (SELECT * FROM sys.views WHERE name = 'vw_LatestL2Metrics')
BEGIN
    EXEC('
    CREATE VIEW vw_LatestL2Metrics
    AS
    SELECT r.TickerSymbol, 
           r.CompanyName, 
           r.ReportDate, 
           r.IndicatorNameCN, 
           r.IndicatorNameEN, 
           r.CalculatedValue, 
           r.EvaluationResult,
           r.LowerBound,
           r.UpperBound,
           r.RangeValue,
           r.FormulaDesc,
           r.FormulaEN,
           r.CalculationTime,
           r.Remarks
    FROM L2Metrics r
    WHERE r.ReportDate = (
        SELECT MAX(ReportDate)
        FROM L2Metrics
        WHERE TickerSymbol = r.TickerSymbol
    );
    ');
    
    PRINT 'vw_LatestL2Metrics视图已创建';
END
ELSE
BEGIN
    PRINT 'vw_LatestL2Metrics视图已存在';
END

-- 创建函数，用于获取指标的历史趋势
IF NOT EXISTS (SELECT * FROM sys.objects WHERE type = 'IF' AND name = 'fn_GetL2MetricTrend')
BEGIN
    EXEC('
    CREATE FUNCTION fn_GetL2MetricTrend
    (
        @TickerSymbol NVARCHAR(20),
        @IndicatorNameEN NVARCHAR(100),
        @PeriodCount INT = 8
    )
    RETURNS TABLE
    AS
    RETURN
    (
        SELECT TOP (@PeriodCount)
               TickerSymbol,
               CompanyName,
               ReportDate,
               IndicatorNameEN,
               IndicatorNameCN,
               CalculatedValue,
               EvaluationResult,
               LowerBound,
               UpperBound,
               RangeValue
        FROM L2Metrics
        WHERE TickerSymbol = @TickerSymbol
          AND IndicatorNameEN = @IndicatorNameEN
        ORDER BY ReportDate DESC
    );
    ');
    
    PRINT 'fn_GetL2MetricTrend函数已创建';
END
ELSE
BEGIN
    PRINT 'fn_GetL2MetricTrend函数已存在';
END

-- 创建函数，用于获取公司的所有最新指标
IF NOT EXISTS (SELECT * FROM sys.objects WHERE type = 'IF' AND name = 'fn_GetCompanyLatestL2Metrics')
BEGIN
    EXEC('
    CREATE FUNCTION fn_GetCompanyLatestL2Metrics
    (
        @TickerSymbol NVARCHAR(20)
    )
    RETURNS TABLE
    AS
    RETURN
    (
        SELECT TickerSymbol,
               CompanyName,
               ReportDate,
               IndicatorNameCN,
               IndicatorNameEN,
               CalculatedValue,
               EvaluationResult,
               LowerBound,
               UpperBound,
               RangeValue,
               FormulaDesc,
               FormulaEN
        FROM L2Metrics
        WHERE TickerSymbol = @TickerSymbol
          AND ReportDate = (
              SELECT MAX(ReportDate)
              FROM L2Metrics
              WHERE TickerSymbol = @TickerSymbol
          )
    );
    ');
    
    PRINT 'fn_GetCompanyLatestL2Metrics函数已创建';
END
ELSE
BEGIN
    PRINT 'fn_GetCompanyLatestL2Metrics函数已存在';
END

PRINT '所有表和视图创建完成';
