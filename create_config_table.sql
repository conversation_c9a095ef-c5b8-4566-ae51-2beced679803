-- 创建配置数据表
CREATE TABLE IF NOT EXISTS FinancialReportConfig (
    id INT PRIMARY KEY AUTO_INCREMENT,
    sort INT NOT NULL COMMENT '排序号(原key*100)',
    item VARCHAR(255) NOT NULL COMMENT '项目名称',
    level INT NOT NULL DEFAULT 0 COMMENT '层级',
    rowType VARCHAR(50) NOT NULL COMMENT '行类型(header/sub/subtotal/total)',
    rptName VARCHAR(100) NOT NULL COMMENT '报表名称',
    windCode VARCHAR(100) COMMENT 'Wind代码',
    currentAmount DECIMAL(20,2) DEFAULT NULL COMMENT '本期金额',
    previousAmount DECIMAL(20,2) DEFAULT NULL COMMENT '上期金额',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_sort (sort),
    INDEX idx_rptName (rptName),
    INDEX idx_windCode (windCode)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='财务报表配置数据表';