import React, { useState } from 'react';
import { SwapOutlined } from '@ant-design/icons';
import CustomLine from './CustomLine';
import EnlargeableChartWrapper from './EnlargeableChartWrapper';

const TurnoverToggleChart = ({ 
  totalAssetTurnoverData, 
  equityTurnoverData, 
  height, 
  fontSize, 
  isFullscreen 
}) => {
  const [currentMode, setCurrentMode] = useState('totalAsset'); // 'totalAsset' 或 'equity'

  const handleToggle = () => {
    setCurrentMode(currentMode === 'totalAsset' ? 'equity' : 'totalAsset');
  };

  const getCurrentData = () => {
    return currentMode === 'totalAsset' ? totalAssetTurnoverData : equityTurnoverData;
  };

  const getCurrentTitle = () => {
    return currentMode === 'totalAsset' ? '总资产周转率' : '净资产周转率';
  };

  return (
    <div style={{ position: 'relative', height: '100%' }}>
      {/* 切换图标按钮 - 放在右上角，避免与放大按钮重叠 */}
      <button
        onClick={handleToggle}
        style={{
          position: 'absolute',
          top: '18px',
          right: '40px', // 向右偏移，避免与放大按钮重叠
          zIndex: 999,
          width: 28,
          height: 28,
          border: '1px solid #ccc',
          borderRadius: '50%',
          background: 'rgba(255,255,255,0.8)',
          cursor: 'pointer',
          transition: 'all 0.3s ease',
          boxShadow: '0 2px 8px rgba(0,0,0,0.2)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          fontSize: '16px',
          color: '#1783ff'
        }}
        onMouseOver={(e) => {
          e.target.style.background = 'rgba(255,255,255,0.9)';
          e.target.style.transform = 'scale(1.05)';
        }}
        onMouseOut={(e) => {
          e.target.style.background = 'rgba(255,255,255,0.8)';
          e.target.style.transform = 'scale(1)';
        }}
        title="切换总资产周转率/净资产周转率"
      >
        <SwapOutlined style={{ fontSize: 16, color: '#1783ff' }} />
      </button>
      
      <EnlargeableChartWrapper 
        title={getCurrentTitle()}
        style={{ height: '100%' }}
      >
        <CustomLine 
          data={getCurrentData()} 
          title={getCurrentTitle()} 
          height={height} 
          fontSize={fontSize} 
          isFullscreen={isFullscreen} 
        />
      </EnlargeableChartWrapper>
    </div>
  );
};

export default TurnoverToggleChart; 