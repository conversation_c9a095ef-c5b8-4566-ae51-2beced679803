from ..data_access import execute_query

class MetricRepository:
    @staticmethod
    def get_companies():
        """获取所有公司列表，包含内部公司标识"""
        return execute_query("SELECT TickerSymbol AS id, CompanyName AS name, IsInternal FROM CompanyInfo WHERE IsActive = 1 ORDER BY IsInternal DESC, CompanyName")

    @staticmethod
    def get_active_metrics():
        """获取所有活跃指标定义"""
        return execute_query("""
            SELECT
                IndicatorNameCN AS name,
                IndicatorNameEN AS code,
                IndicatorDesc AS description,
                Formula AS formula,
                FormulaDesc AS formula_desc,
                ValueType AS value_type,
                Unit AS unit

            FROM L2BenchmarkIndicators where Enabled=1 and isshow=1 order by Sort
        """)

    @staticmethod
    # def get_metric_values(table_name, year=None, metrics=None, companies=None):
    def get_metric_values(table_name, report_date=None, metrics=None, companies=None):
        """获取指标值数据，支持按报告日期、指标和公司筛选"""
        import logging
        query = f"""
            SELECT
                TickerSymbol AS company_id,
                IndicatorNameEN AS metric_name,
                CalculatedValue AS value,
               YEAR(ReportDate) AS year,
                ReportDate AS report_date_full,EvaluationResult,TranslatedFormula
            FROM {table_name}
            WHERE 1=1  
        """
        params = []
        logging.debug(f"初始SQL: {query}")
        # if year:
        #     query += " AND YEAR(ReportDate) = %s"
        #     params.append(year)
        #     logging.debug(f"添加年份筛选: {year}")
        if report_date:
            query += " AND ReportDate = ?"
            params.append(report_date)
            logging.debug(f"添加报告日期筛选: {report_date}")
        if metrics:
            placeholders = ', '.join(['?'] * len(metrics))
            query += f" AND IndicatorNameEN IN ({placeholders})"
            params.extend(metrics)
            logging.debug(f"添加指标筛选: {metrics}")
        if companies:
            placeholders = ', '.join(['?'] * len(companies))
            query += f" AND TickerSymbol IN ({placeholders})"
            params.extend(companies)
            logging.debug(f"添加公司筛选: {companies}")
        try:
            result = execute_query(query, params if params else None)
            logging.debug(f"查询结果: {result}")
            if not result:
                # logging.warning(f"未查询到数据，参数: year={year}, metrics={metrics}, companies={companies}")
                logging.warning(f"未查询到数据，参数: report_date={report_date}, metrics={metrics}, companies={companies}")
            return result
        except Exception as e:
            logging.error(f"执行SQL时异常: {e}, SQL: {query}, 参数: {params}")
            raise
