/**
 * 环境变量测试脚本
 * 
 * 用于测试环境变量是否正确加载
 * 使用方法: node test-env.js [环境文件名]
 */

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

// 获取命令行参数中的环境文件名
const envFile = process.argv[2] || '.env';
const envFilePath = path.resolve(__dirname, envFile);

// 检查环境文件是否存在
if (!fs.existsSync(envFilePath)) {
  console.error(`错误: 环境文件 "${envFile}" 不存在`);
  console.log('可用的环境文件:');
  
  // 列出所有可用的环境文件
  fs.readdirSync(__dirname)
    .filter(file => file.startsWith('.env'))
    .forEach(file => console.log(`- ${file}`));
  
  process.exit(1);
}

console.log(`使用环境文件: ${envFile}`);

// 读取环境文件内容
const envContent = fs.readFileSync(envFilePath, 'utf-8');
console.log('\n环境文件内容:');
console.log(envContent);

// 解析环境变量
const env = {};
envContent.split('\n').forEach(line => {
  if (line.trim() && !line.startsWith('#')) {
    const [key, ...valueParts] = line.split('=');
    if (key && valueParts.length > 0) {
      env[key.trim()] = valueParts.join('=').trim();
    }
  }
});

console.log('\n解析后的环境变量:');
console.log(env);

// 创建一个临时的 HTML 文件，用于测试环境变量
const tempHtmlPath = path.resolve(__dirname, 'temp-env-test.html');
const tempHtmlContent = `
<!DOCTYPE html>
<html>
<head>
  <title>环境变量测试</title>
</head>
<body>
  <h1>环境变量测试</h1>
  <div id="output"></div>
  <script>
    // 在浏览器中测试环境变量
    const output = document.getElementById('output');
    
    // 测试 process.env
    output.innerHTML += '<h2>process.env:</h2>';
    output.innerHTML += '<pre>' + JSON.stringify(process.env, null, 2) + '</pre>';
    
    // 测试 import.meta.env
    output.innerHTML += '<h2>import.meta.env:</h2>';
    output.innerHTML += '<pre>' + JSON.stringify(import.meta.env, null, 2) + '</pre>';
  </script>
</body>
</html>
`;

fs.writeFileSync(tempHtmlPath, tempHtmlContent);
console.log(`\n创建临时测试文件: ${tempHtmlPath}`);

// 启动 Vite 开发服务器
console.log('\n启动 Vite 开发服务器进行测试...');
const env2 = { ...process.env, ENV_FILE: envFile };

// 确定要运行的命令
const isWindows = process.platform === 'win32';
const npxCmd = isWindows ? 'npx.cmd' : 'npx';

// 启动 Vite 开发服务器
console.log(`启动命令: ${npxCmd} vite --config vite.config.js`);
const child = spawn(npxCmd, ['vite', '--config', 'vite.config.js'], {
  env: env2,
  stdio: 'inherit',
  shell: true
});

child.on('error', (error) => {
  console.error(`启动失败: ${error.message}`);
  // 清理临时文件
  if (fs.existsSync(tempHtmlPath)) {
    fs.unlinkSync(tempHtmlPath);
  }
  process.exit(1);
});

child.on('close', (code) => {
  // 清理临时文件
  if (fs.existsSync(tempHtmlPath)) {
    fs.unlinkSync(tempHtmlPath);
  }
  
  if (code !== 0) {
    console.error(`进程退出，退出码: ${code}`);
  }
});
