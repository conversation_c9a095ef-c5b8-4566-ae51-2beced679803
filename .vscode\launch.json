{"version": "0.2.0", "configurations": [{"name": "Python Debugger: Current File", "type": "debugpy", "request": "launch", "program": "${file}", "console": "integratedTerminal"}, {"name": "主程序", "type": "debugpy", "request": "launch", "program": "main.py", "console": "integratedTerminal"}, {"name": "Wind 导入", "type": "debugpy", "request": "launch", "program": "import_wind_data.py", "console": "integratedTerminal"}, {"name": "Wind 附注补充", "type": "debugpy", "request": "launch", "program": "import_wind_data_supplement.py", "console": "integratedTerminal"}, {"name": "Wind 补充同比数据", "type": "debugpy", "request": "launch", "program": "import_wind_data_yoyvalue.py", "console": "integratedTerminal"}, {"name": "Wind 指标校验", "type": "debugpy", "request": "launch", "program": "check_wind_indicators.py", "console": "integratedTerminal"}, {"name": "L2 指标计算", "type": "debugpy", "request": "launch", "program": "metrics_calculator.py", "console": "integratedTerminal"}]}