import sqlite3
import pyodbc
from ..config.database_config import get_db_connection

class CompanyRepository:
    def __init__(self):
        self.connection = get_db_connection()

    def get_all_companies(self):
        cursor = self.connection.cursor()
        # 从 CompanyInfo 表获取激活的公司列表
        query = '''SELECT TickerSymbol, CompanyName,IsInternal FROM CompanyInfo WHERE IsActive = 1 ORDER BY TickerSymbol'''
        cursor.execute(query)
        result = cursor.fetchall()

        return result
