#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
财务配置项API路由
提供配置项管理的RESTful接口
"""

import os
import json
from datetime import datetime
from flask import Blueprint, request, jsonify, current_app
from werkzeug.utils import secure_filename
from ..services.financial_config_service import FinancialConfigService

# 创建蓝图
financial_config_bp = Blueprint('financial_config', __name__, url_prefix='/api/config')

# 初始化服务
config_service = FinancialConfigService()

# 允许的文件扩展名
ALLOWED_EXTENSIONS = {'xlsx', 'xls'}

def allowed_file(filename):
    """检查文件扩展名是否允许"""
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

@financial_config_bp.route('/init', methods=['POST'])
def initialize_config_tables():
    """
    初始化配置表
    
    Returns:
        JSON: 初始化结果
    """
    try:
        success = config_service.initialize_tables()
        
        if success:
            return jsonify({
                'success': True,
                'message': '配置表初始化成功',
                'timestamp': datetime.now().isoformat()
            })
        else:
            return jsonify({
                'success': False,
                'message': '配置表初始化失败',
                'timestamp': datetime.now().isoformat()
            }), 500
            
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'初始化失败: {str(e)}',
            'timestamp': datetime.now().isoformat()
        }), 500

@financial_config_bp.route('/upload', methods=['POST'])
def upload_excel_config():
    """
    上传Excel文件生成配置
    
    Form Data:
        file: Excel文件
        config_name: 配置名称
        description: 配置描述（可选）
        created_by: 创建者（可选）
    
    Returns:
        JSON: 生成的配置ID和摘要
    """
    try:
        # 检查文件
        if 'file' not in request.files:
            return jsonify({
                'success': False,
                'message': '未找到上传文件'
            }), 400
        
        file = request.files['file']
        if file.filename == '':
            return jsonify({
                'success': False,
                'message': '未选择文件'
            }), 400
        
        if not allowed_file(file.filename):
            return jsonify({
                'success': False,
                'message': '文件格式不支持，请上传Excel文件(.xlsx或.xls)'
            }), 400
        
        # 获取参数
        config_name = request.form.get('config_name')
        if not config_name:
            return jsonify({
                'success': False,
                'message': '配置名称不能为空'
            }), 400
        
        description = request.form.get('description', '')
        created_by = request.form.get('created_by', 'system')
        
        # 保存文件
        filename = secure_filename(file.filename)
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"{timestamp}_{filename}"
        
        upload_dir = os.path.join(os.getcwd(), 'uploads')
        os.makedirs(upload_dir, exist_ok=True)
        
        file_path = os.path.join(upload_dir, filename)
        file.save(file_path)
        
        try:
            # 生成配置
            config_id = config_service.generate_config_from_excel(
                excel_path=file_path,
                config_name=config_name,
                description=description,
                created_by=created_by
            )
            
            # 获取配置摘要
            summary = config_service.get_config_summary(config_id)
            
            return jsonify({
                'success': True,
                'message': '配置生成成功',
                'configId': config_id,
                'summary': summary,
                'timestamp': datetime.now().isoformat()
            })
            
        finally:
            # 清理临时文件
            if os.path.exists(file_path):
                os.remove(file_path)
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'配置生成失败: {str(e)}',
            'timestamp': datetime.now().isoformat()
        }), 500

@financial_config_bp.route('/list', methods=['GET'])
def list_configs():
    """
    获取配置列表
    
    Query Parameters:
        page: 页码（默认1）
        page_size: 每页大小（默认20）
    
    Returns:
        JSON: 配置列表和分页信息
    """
    try:
        page = int(request.args.get('page', 1))
        page_size = int(request.args.get('page_size', 20))
        
        result = config_service.list_configs(page, page_size)
        
        return jsonify({
            'success': True,
            'data': result,
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取配置列表失败: {str(e)}',
            'timestamp': datetime.now().isoformat()
        }), 500

@financial_config_bp.route('/<int:config_id>', methods=['GET'])
def get_config_summary(config_id):
    """
    获取配置摘要
    
    Args:
        config_id: 配置ID
    
    Returns:
        JSON: 配置摘要信息
    """
    try:
        summary = config_service.get_config_summary(config_id)
        
        return jsonify({
            'success': True,
            'data': summary,
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取配置摘要失败: {str(e)}',
            'timestamp': datetime.now().isoformat()
        }), 500

@financial_config_bp.route('/<int:config_id>/three-reports', methods=['GET'])
def get_three_reports_config(config_id):
    """
    获取三表配置数据
    
    Args:
        config_id: 配置ID
    
    Returns:
        JSON: 三表配置数据
    """
    try:
        config_data = config_service.get_three_reports_config(config_id)
        
        return jsonify({
            'success': True,
            'data': config_data,
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取三表配置失败: {str(e)}',
            'timestamp': datetime.now().isoformat()
        }), 500

@financial_config_bp.route('/<int:config_id>/reports/<report_type>', methods=['GET'])
def get_report_config(config_id, report_type):
    """
    获取单个报表配置
    
    Args:
        config_id: 配置ID
        report_type: 报表类型
    
    Query Parameters:
        company_id: 公司ID（可选）
        report_date: 报告日期（可选）
        with_values: 是否包含数值（默认false）
    
    Returns:
        JSON: 报表配置数据
    """
    try:
        company_id = request.args.get('company_id')
        report_date = request.args.get('report_date')
        with_values = request.args.get('with_values', 'false').lower() == 'true'
        
        if with_values and company_id:
            # 获取包含数值的配置
            config_data = config_service.get_report_config_with_values(
                config_id=config_id,
                report_type=report_type,
                company_id=company_id,
                report_date=report_date
            )
        else:
            # 只获取配置结构
            three_reports = config_service.get_three_reports_config(config_id)
            if report_type not in three_reports['reports']:
                return jsonify({
                    'success': False,
                    'message': f'报表类型不存在: {report_type}'
                }), 404
            
            config_data = {
                'configId': config_id,
                'reportType': report_type,
                'companyId': company_id,
                'reportDate': report_date,
                **three_reports['reports'][report_type]
            }
        
        return jsonify({
            'success': True,
            'data': config_data,
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取报表配置失败: {str(e)}',
            'timestamp': datetime.now().isoformat()
        }), 500

@financial_config_bp.route('/<int:config_id>/reports/<report_type>/data', methods=['GET'])
def get_report_data_with_values(config_id, report_type):
    """
    获取报表数据（包含数值）
    
    Args:
        config_id: 配置ID
        report_type: 报表类型
    
    Query Parameters:
        company_id: 公司ID（必需）
        report_date: 报告日期（可选，默认最新）
    
    Returns:
        JSON: 包含数值的报表数据
    """
    try:
        company_id = request.args.get('company_id')
        if not company_id:
            return jsonify({
                'success': False,
                'message': '公司ID不能为空'
            }), 400
        
        report_date = request.args.get('report_date')
        
        config_data = config_service.get_report_config_with_values(
            config_id=config_id,
            report_type=report_type,
            company_id=company_id,
            report_date=report_date
        )
        
        return jsonify({
            'success': True,
            'data': config_data,
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取报表数据失败: {str(e)}',
            'timestamp': datetime.now().isoformat()
        }), 500

@financial_config_bp.route('/<int:config_id>', methods=['DELETE'])
def delete_config(config_id):
    """
    删除配置
    
    Args:
        config_id: 配置ID
    
    Returns:
        JSON: 删除结果
    """
    try:
        success = config_service.delete_config(config_id)
        
        if success:
            return jsonify({
                'success': True,
                'message': '配置删除成功',
                'timestamp': datetime.now().isoformat()
            })
        else:
            return jsonify({
                'success': False,
                'message': '配置删除失败',
                'timestamp': datetime.now().isoformat()
            }), 500
            
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'删除配置失败: {str(e)}',
            'timestamp': datetime.now().isoformat()
        }), 500

@financial_config_bp.route('/health', methods=['GET'])
def health_check():
    """
    健康检查
    
    Returns:
        JSON: 服务状态
    """
    try:
        # 测试数据库连接
        configs = config_service.list_configs(1, 1)
        
        return jsonify({
            'success': True,
            'message': '配置服务运行正常',
            'database': 'connected',
            'configCount': configs.get('total', 0),
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': '配置服务异常',
            'database': 'disconnected',
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

# 错误处理
@financial_config_bp.errorhandler(404)
def not_found(error):
    return jsonify({
        'success': False,
        'message': '接口不存在',
        'timestamp': datetime.now().isoformat()
    }), 404

@financial_config_bp.errorhandler(405)
def method_not_allowed(error):
    return jsonify({
        'success': False,
        'message': '请求方法不允许',
        'timestamp': datetime.now().isoformat()
    }), 405

@financial_config_bp.errorhandler(500)
def internal_error(error):
    return jsonify({
        'success': False,
        'message': '服务器内部错误',
        'timestamp': datetime.now().isoformat()
    }), 500