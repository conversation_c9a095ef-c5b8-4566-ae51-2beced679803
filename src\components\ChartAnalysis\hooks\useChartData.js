import { useState, useEffect } from 'react';
import { generateReportDate } from '../utils/generateReportDate';
import { generateChartData } from '../utils/generateChartData';

export function useChartData(selectedYear, selectedQuarter, selectedCompanies, quarters, companies, chartIndicators) {
  const [loading, setLoading] = useState(false);
  const [barData, setBarData] = useState([]);
  const [radarData, setRadarData] = useState([]);
  const [radarIndicators, setRadarIndicators] = useState([]);
  
  // 所有图表数据状态
  const [assetStackData, setAssetStackData] = useState([]);
  const [debtStackData, setDebtStackData] = useState([]);
  const [debtToAssetsData, setDebtToAssetsData] = useState([]);
  const [totalAssetTurnoverData, setTotalAssetTurnoverData] = useState([]);
  const [equityTurnoverData, setEquityTurnoverData] = useState([]);
  const [grossMarginData, setGrossMarginData] = useState([]);
  const [threeFeeRatioData, setThreeFeeRatioData] = useState([]);
  const [netProfitMarginData, setNetProfitMarginData] = useState([]);
  const [roeBasicData, setRoeBasicData] = useState([]);
  const [twoFundNetData, setTwoFundNetData] = useState([]);
  const [twoFundIncomeData, setTwoFundIncomeData] = useState([]);
  const [inventoryTurnoverData, setInventoryTurnoverData] = useState([]);
  const [receivablesTurnoverData, setReceivablesTurnoverData] = useState([]);
  const [cashFlowData, setCashFlowData] = useState([]);
  const [netCashFlowsOperActData, setNetCashFlowsOperActData] = useState([]);
  const [revenuePerRdPersonnelData, setRevenuePerRdPersonnelData] = useState([]);
  const [cashCollectionRatioData, setCashCollectionRatioData] = useState([]);
  const [cashCycleData, setCashCycleData] = useState([]);
  const [rdInvestData, setRdInvestData] = useState([]);
  const [rdExpData, setRdExpData] = useState([]);
  const [revenuePerPersonData, setRevenuePerPersonData] = useState([]);
  const [divAualCashDividendData, setDivAualCashDividendData] = useState([]);
  const [employeeData, setEmployeeData] = useState([]);
  const [nonRecurringProfitLossData, setNonRecurringProfitLossData] = useState([]);

  useEffect(() => {
    if (!selectedYear || !selectedQuarter || !selectedCompanies.length) return;
    const reportDate = generateReportDate(selectedYear, selectedQuarter, quarters);
    setLoading(true);
    
    // 使用新的图表配置接口
    fetch("./api/chart-data/dashboard", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({
        report_date: reportDate,
        company_ids: selectedCompanies,
      }),
    })
      .then(res => res.json())
      .then((response) => {
        if (!response.success) {
          console.error('API Error:', response.error);
          return;
        }
        
        const data = response.data;
        
        // 统一处理所有数据
        const processedData = processAllData(data, selectedCompanies, companies, chartIndicators);
         
        // 设置所有图表数据
        setBarData(processedData.barData);
        setRadarData(processedData.radarData);
        setRadarIndicators(processedData.radarIndicators);
        setAssetStackData(processedData.assetStackData);
        setDebtStackData(processedData.debtStackData);
        setDebtToAssetsData(processedData.debtToAssetsData);
        setTotalAssetTurnoverData(processedData.totalAssetTurnoverData);
        setEquityTurnoverData(processedData.equityTurnoverData);
        setGrossMarginData(processedData.grossMarginData);
        setThreeFeeRatioData(processedData.threeFeeRatioData);
        setNetProfitMarginData(processedData.netProfitMarginData);
        setRoeBasicData(processedData.roeBasicData);
        setTwoFundNetData(processedData.twoFundNetData);
        setTwoFundIncomeData(processedData.twoFundIncomeData);
        setInventoryTurnoverData(processedData.inventoryTurnoverData);
        setReceivablesTurnoverData(processedData.receivablesTurnoverData);
        setCashFlowData(processedData.cashFlowData);
        setNetCashFlowsOperActData(processedData.netCashFlowsOperActData);
        setRevenuePerRdPersonnelData(processedData.revenuePerRdPersonnelData);
        setCashCollectionRatioData(processedData.cashCollectionRatioData);
        setCashCycleData(processedData.cashCycleData);
        setRdInvestData(processedData.rdInvestData);
        setRdExpData(processedData.rdExpData);
        setRevenuePerPersonData(processedData.revenuePerPersonData);
        setDivAualCashDividendData(processedData.divAualCashDividendData);
        setEmployeeData(processedData.employeeData);
        setNonRecurringProfitLossData(processedData.nonRecurringProfitLossData);
      })
      .catch(error => {
        console.error('Fetch Error:', error);
      })
      .finally(() => setLoading(false));
  }, [selectedYear, selectedQuarter, selectedCompanies, quarters, chartIndicators]);

  // 统一处理所有数据的函数
  const processAllData = (data, selectedCompanies, companies, chartIndicators) => {
    const result = {
      barData: [],
      radarData: [],
      radarIndicators: [],
      assetStackData: [],
      debtStackData: [],
      debtToAssetsData: [],
      totalAssetTurnoverData: [],
      grossMarginData: [],
      threeFeeRatioData: [],
      netProfitMarginData: [],
      roeBasicData: [],
      twoFundNetData: [],
      twoFundIncomeData: [],
      inventoryTurnoverData: [],
      receivablesTurnoverData: [],
      cashFlowData: [],
      netCashFlowsOperActData: [],
      revenuePerRdPersonnelData: [],
      cashCycleData: [],
      rdInvestData: [],
      rdExpData: [],
      revenuePerPersonData: [],
      divAualCashDividendData: [],
      employeeData: [],
      nonRecurringProfitLossData: []
    };

    // 简化的数据处理函数
    const processChartDataByType = (dataKey, configKey) => {
      if (!data[dataKey] || !data[dataKey].indicators) {
        console.log(`No data for ${dataKey}:`, data[dataKey]);
        return;
      }
      
      const indicators = data[dataKey].indicators ??[];
      
      // 如果是雷达图，需要特殊处理
      if (configKey === 'radarIndicators') {
        const radarIndicators = chartIndicators[configKey].map(item => item.name);
        result.radarIndicators = radarIndicators;
        
        // 雷达图需要平铺格式的数据
        const radarData = indicators
          .filter(item => selectedCompanies.includes(item.TickerSymbol))
          .map(item => ({
            company: item.CompanyName || item.TickerSymbol,
            item: item.name,
            itemKey: item.key,
            dbitem: item.name,
            value: item.value || 0,
            displayValue: formatDisplayValue(item.value, item.unit),
            year: item.Year || selectedYear,
            source: item
          }));
        result.radarData = radarData;
        return;
      }
      
        // 标准图表处理：按指标分组处理数据
        if (configKey === 'barIndicators') {
          // 对于barIndicators，需要嵌套格式
          const chartsData = chartIndicators[configKey].map(ind => {
            const filteredData = indicators.filter(item => item.key === ind.key && selectedCompanies.includes(item.TickerSymbol));
          
            
            return {
              title: ind.name,
              data: filteredData.map(item => {
                let extraData = [];
                if (item.extra) {
                  extraData = item.extra.map(extra => ({
                    ...extra,
                    displayValue: formatDisplayValue(extra.value, extra.unit)
                  }));
                }
                return {
                  company: item.CompanyName || item.TickerSymbol,
                  category: item.CompanyName || item.TickerSymbol,
                  value: item.value || 0,
                  displayValue: formatDisplayValue(item.value, item.unit),
                  year: item.Year || selectedYear,
                  source: item,
                                     extra: extraData
                 };
               })
             };
           });
           result.barData = chartsData;
        } else {
          // 其他图表，返回平铺格式
          const filteredIndicators = indicators.filter(item => selectedCompanies.includes(item.TickerSymbol));
           
          
       
          const flatData = filteredIndicators.map(item => {
              let extraData = [];
              if (item.extra) {
                extraData = item.extra.map(extra => ({
                  ...extra,
                  displayValue: formatDisplayValue(extra.value, extra.unit)
                }));
              }
              return {
                company: item.CompanyName || item.TickerSymbol,
                category: item.CompanyName || item.TickerSymbol,
                title: item.name,
                type: item.name,
                value: item.value || 0,
                displayValue: formatDisplayValue(item.value, item.unit),
                year: item.Year || selectedYear,
                source: item,
                extra: extraData
              };
            });

          // 根据dataKey设置对应的结果
          const keyMap = {
            'assetStackKeys': 'assetStackData',
            'debtStackKeys': 'debtStackData',
            'debtToAssetsKeys': 'debtToAssetsData',
            'totalAssetTurnoverKeys': 'totalAssetTurnoverData',
            'equityTurnoverKeys': 'equityTurnoverData',
            'grossMarginKeys': 'grossMarginData',
            'threeFeeRatioKeys': 'threeFeeRatioData',
            'netProfitMarginKeys': 'netProfitMarginData',
            'roeBasicKeys': 'roeBasicData',
            'twoFundNetKeys': 'twoFundNetData',
            'twoFundIncomeKeys': 'twoFundIncomeData',
            'inventoryTurnoverKeys': 'inventoryTurnoverData',
            'receivablesTurnoverKeys': 'receivablesTurnoverData',
            'cashFlowIndicators': 'cashFlowData',
            'netCashFlowsOperActKeys': 'netCashFlowsOperActData',
            'revenuePerRdPersonnelKeys': 'revenuePerRdPersonnelData',
            'cashCollectionRatioKeys': 'cashCollectionRatioData',
            'cashCycleKeys': 'cashCycleData',
            'rdInvestKeys': 'rdInvestData',
            'rdExpKeys': 'rdExpData',
            'revenuePerPersonKeys': 'revenuePerPersonData',
            'divAualCashDividendKeys': 'divAualCashDividendData',
            'employeeKeys': 'employeeData',
            'nonRecurringProfitLossKeys': 'nonRecurringProfitLossData'
          };

         
          
          result[keyMap[dataKey]] = flatData;
        }
      
    };

    // 处理所有图表数据
    processChartDataByType('barIndicators', 'barIndicators');
    processChartDataByType('radarIndicators', 'radarIndicators');
    processChartDataByType('assetStackKeys', 'assetStackKeys');
    processChartDataByType('debtStackKeys', 'debtStackKeys');
    processChartDataByType('debtToAssetsKeys', 'debtToAssetsKeys');
    processChartDataByType('totalAssetTurnoverKeys', 'totalAssetTurnoverKeys');
    processChartDataByType('equityTurnoverKeys', 'equityTurnoverKeys');
    processChartDataByType('grossMarginKeys', 'grossMarginKeys');
    processChartDataByType('threeFeeRatioKeys', 'threeFeeRatioKeys');
    processChartDataByType('netProfitMarginKeys', 'netProfitMarginKeys');
    processChartDataByType('roeBasicKeys', 'roeBasicKeys');
    processChartDataByType('twoFundNetKeys', 'twoFundNetKeys');
    processChartDataByType('twoFundIncomeKeys', 'twoFundIncomeKeys');
    processChartDataByType('inventoryTurnoverKeys', 'inventoryTurnoverKeys');
    processChartDataByType('receivablesTurnoverKeys', 'receivablesTurnoverKeys');
    processChartDataByType('cashFlowIndicators', 'cashFlowIndicators');
    processChartDataByType('netCashFlowsOperActKeys', 'netCashFlowsOperActKeys');
    processChartDataByType('revenuePerRdPersonnelKeys', 'revenuePerRdPersonnelKeys');
    processChartDataByType('cashCollectionRatioKeys', 'cashCollectionRatioKeys');
    processChartDataByType('cashCycleKeys', 'cashCycleKeys');
    processChartDataByType('rdInvestKeys', 'rdInvestKeys');
    processChartDataByType('rdExpKeys', 'rdExpKeys');
    processChartDataByType('revenuePerPersonKeys', 'revenuePerPersonKeys');
    processChartDataByType('divAualCashDividendKeys', 'divAualCashDividendKeys');
    processChartDataByType('employeeKeys', 'employeeKeys');
    processChartDataByType('nonRecurringProfitLossKeys', 'nonRecurringProfitLossKeys');

    // --- 新增：合并两金占比和同比数据 ---
    if (result.twoFundIncomeData && Array.isArray(result.twoFundIncomeData) && result.twoFundIncomeData.length > 0) {
      // 原始数据格式：每家公司有两条（比重/同比）
      const merged = {};
      result.twoFundIncomeData.forEach(item => {
        if (!merged[item.company]) merged[item.company] = { company: item.company };
        if (item.title === '两金占营业收入比重') {
          merged[item.company].value = item.value;
          merged[item.company].displayValue = item.displayValue;
        }
        if (item.title === '两金占营业收入比重同比') {
          merged[item.company].yoyValue = item.value;
        }
      });
      result.twoFundIncomeData = Object.values(merged);
    }
    // --- END ---

    return result;
  };



  // 格式化显示值的函数
  const formatDisplayValue = (value, unit) => {
    if (value === null || value === undefined) return '';
    
    if (unit === '%') {
      return value.toFixed(2) + '%';
    } else if (unit === '万元') {
      return value.toFixed(2) + '万';
    } else if (unit === '亿元') {
      return value.toFixed(2) + '亿';
    } else if (unit === '次') {
      return value.toFixed(0) + '次';
    } else if (unit === '天') {
      return value.toFixed(0) + '天';
    } else if (unit === '人') {
      return value.toFixed(0) + '人';
    } else {
      return value.toFixed(2);
    }
  };

  return {
    loading,
    barData, 
    radarData, 
    radarIndicators,
    assetStackData, 
    debtStackData,
    debtToAssetsData,
    totalAssetTurnoverData,
    equityTurnoverData,
    grossMarginData,
    threeFeeRatioData,
    netProfitMarginData,
    roeBasicData,
    twoFundNetData,
    twoFundIncomeData,
    inventoryTurnoverData,
    receivablesTurnoverData,
    cashFlowData,
    netCashFlowsOperActData,
    revenuePerRdPersonnelData,
    cashCollectionRatioData,
    cashCycleData,
    rdInvestData,
    rdExpData,
    revenuePerPersonData,
    divAualCashDividendData,
    employeeData,
    nonRecurringProfitLossData
  };
} 