import pandas as pd
import re
from database.db_handler import DatabaseHandler
from datetime import datetime

class EnhancedFormulaExecutor:
    """
    增强版公式执行器，支持处理上期和上期末数据
    """

    @staticmethod
    def execute_formula(formula, current_values, historical_values):
        """
        执行财务指标公式

        参数:
            formula (str): 从数据库读取的公式字符串
            current_values (dict): 当期值字典
            historical_values (dict): 历史值字典，包含上期和上期末的数据

        返回:
            float: 计算结果
        """
        # 如果公式为空或者只是一个变量名，直接返回对应的值
        if not formula or formula.strip() == '':
            return 0

        # 将所有键转换为大写，以便不区分大小写
        current_values_upper = {k.upper(): v for k, v in current_values.items()}

        # 检查公式是否只是一个简单的变量名
        formula_upper = formula.strip().upper()
        if formula_upper in current_values_upper:
            return current_values_upper[formula_upper]

        # 创建一个本地变量字典，用于eval执行
        local_vars = {}

        # 将当前值添加到本地变量字典（不区分大小写）
        for var, value in current_values_upper.items():
            local_vars[var.lower()] = value

        # 替换所有中文特殊字符
        replacements = {
            "（": "(", "）": ")",
            "×": "*", "÷": "/",
            "％": "%", "／": "/",
            " ": "",  # 移除空格
        }
        for ch, rep in replacements.items():
            formula = formula.replace(ch, rep)

        # 处理公式中的特殊标记
        # 替换[上期]和[上期末]为对应的历史数据
        pattern = r'([a-zA-Z0-9_]+)\[(上期|上期末)\]'
        matches = re.findall(pattern, formula)

        for var_name, period in matches:
            period_key = 'previous' if period == '上期' else 'previous_end'
            var_name_upper = var_name.upper()
            if period_key in historical_values and var_name_upper in {k.upper(): k for k in historical_values[period_key].keys()}:
                orig_key = {k.upper(): k for k in historical_values[period_key].keys()}[var_name_upper]
                value = historical_values[period_key].get(orig_key, 0)
                formula = formula.replace(f"{var_name}[{period}]", str(value))
                # 同时添加到本地变量字典
                local_vars[f"{var_name.lower()}_{period}"] = value
            else:
                # 如果找不到历史数据，则替换为0
                formula = formula.replace(f"{var_name}[{period}]", "0")
                local_vars[f"{var_name.lower()}_{period}"] = 0

        # 处理公式中的特殊情况
        # 例如，处理"total_invested_capital"这样的复合计算
        if "total_invested_capital" in formula.lower():
            # 计算total_invested_capital
            total_invested_capital = (
                current_values_upper.get('EQY_BELONGTO_PARCOMSH', 0) +
                current_values_upper.get('ST_BORROW', 0) +
                current_values_upper.get('WGSD_LIABS_TRADING', 0) +
                current_values_upper.get('NON_CUR_LIAB_DUE_WITHIN_1Y', 0) +
                current_values_upper.get('BONDS_PAYABLE', 0) +
                current_values_upper.get('LT_BORROW', 0) +
                current_values_upper.get('LEASE_OBLIGATION', 0)
            )
            formula = re.sub(r'total_invested_capital', str(total_invested_capital), formula, flags=re.IGNORECASE)
            local_vars['total_invested_capital'] = total_invested_capital

            # 处理上期末的total_invested_capital
            if 'total_invested_capital[上期末]' in formula.lower():
                prev_end = historical_values.get('previous_end', {})
                prev_end_upper = {k.upper(): v for k, v in prev_end.items()}
                prev_total_invested_capital = (
                    prev_end_upper.get('EQY_BELONGTO_PARCOMSH', 0) +
                    prev_end_upper.get('ST_BORROW', 0) +
                    prev_end_upper.get('WGSD_LIABS_TRADING', 0) +
                    prev_end_upper.get('NON_CUR_LIAB_DUE_WITHIN_1Y', 0) +
                    prev_end_upper.get('BONDS_PAYABLE', 0) +
                    prev_end_upper.get('LT_BORROW', 0) +
                    prev_end_upper.get('LEASE_OBLIGATION', 0)
                )
                formula = re.sub(r'total_invested_capital\[上期末\]', str(prev_total_invested_capital), formula, flags=re.IGNORECASE)
                local_vars['total_invested_capital_上期末'] = prev_total_invested_capital

        # 处理EBITDA计算
        if "EBITDA" in formula.upper():
            ebitda = (
                current_values_upper.get('TOT_PROFIT', 0) +
                current_values_upper.get('FIN_INT_EXP', 0) +
                current_values_upper.get('DEPR_FA_COGA_DPBA', 0) +
                current_values_upper.get('DEPRE_PROP_RIGHT_USE', 0) +
                current_values_upper.get('AMORT_INTANG_ASSETS', 0) +
                current_values_upper.get('AMORT_LT_DEFERRED_EXP', 0)
            )
            formula = re.sub(r'EBITDA', str(ebitda), formula, flags=re.IGNORECASE)
            local_vars['ebitda'] = ebitda

        # 处理两金净额计算
        if "two_fund_net" in formula.lower():
            two_fund_net = current_values_upper.get('ACCT_RCV', 0) + current_values_upper.get('INVENTORIES', 0)
            formula = re.sub(r'two_fund_net', str(two_fund_net), formula, flags=re.IGNORECASE)
            local_vars['two_fund_net'] = two_fund_net

        # 处理人事费用计算
        if "personnel_cost_total" in formula.lower():
            personnel_cost_total = (
                current_values_upper.get('STMNOTE_OTHERS_7626', 0) +  # 销售费用-工资薪酬
                current_values_upper.get('STMNOTE_OTHERS_7627', 0) +  # 管理费用-工资薪酬
                current_values_upper.get('STMNOTE_RDSALARY', 0)       # 研发费用-工资薪酬
            )
            formula = re.sub(r'personnel_cost_total', str(personnel_cost_total), formula, flags=re.IGNORECASE)
            local_vars['personnel_cost_total'] = personnel_cost_total

        # 处理研发人员数量计算
        if "stmnote_RDemployee_prev" in formula.lower() or "stmnote_RDemployee_curr" in formula.lower():
            rd_employee_curr = current_values_upper.get('STMNOTE_RDEMPLOYEE', 0)

            prev_end = historical_values.get('previous_end', {})
            prev_end_upper = {k.upper(): v for k, v in prev_end.items()}
            rd_employee_prev = prev_end_upper.get('STMNOTE_RDEMPLOYEE', 0)

            formula = re.sub(r'stmnote_RDemployee_curr', str(rd_employee_curr), formula, flags=re.IGNORECASE)
            formula = re.sub(r'stmnote_RDemployee_prev', str(rd_employee_prev), formula, flags=re.IGNORECASE)

            local_vars['stmnote_rdemployee_curr'] = rd_employee_curr
            local_vars['stmnote_rdemployee_prev'] = rd_employee_prev

        # 清理公式中的换行符和其他不必要的字符
        formula = formula.replace('\n', ' ')

        # 处理可能的语法错误
        formula = formula.replace('x100%', '*100')
        formula = formula.replace('x100％', '*100')
        formula = formula.replace('%', '/100')

        # 处理公式中的等号
        if formula.startswith('='):
            formula = formula[1:]

        # 处理公式中的乘法
        formula = formula.replace('*100/100', '')

        # 处理公式中的除法
        formula = formula.replace('/100*100', '')

        # 处理公式中的特殊情况
        formula = formula.replace('x100', '*100')

        # 处理公式中的逗号
        formula = formula.replace('，', ',')

        # 处理公式中的分号
        formula = formula.replace('；', ';')

        # 处理公式中的冒号
        formula = formula.replace('：', ':')

        # 处理公式中的括号
        formula = formula.replace('（', '(')
        formula = formula.replace('）', ')')

        # 处理公式中的空格
        formula = formula.replace(' ', '')

        # 处理公式中的注释
        if ';' in formula:
            formula = formula.split(';')[0]

        # 处理公式中的注释
        if ',' in formula:
            formula = formula.split(',')[0]

        # 处理公式中的注释
        if '其中' in formula:
            formula = formula.split('其中')[0]

        # 处理公式中的注释
        if '1)' in formula:
            formula = formula.split('1)')[0]

        # 处理公式中的注释
        if '2)' in formula:
            formula = formula.split('2)')[0]

        # 处理公式中的注释
        if '1）' in formula:
            formula = formula.split('1）')[0]

        # 处理公式中的注释
        if '2）' in formula:
            formula = formula.split('2）')[0]

        # 处理公式中的2inventories这样的表达式
        formula = re.sub(r'(\d+)([a-zA-Z_]+)', r'\1*\2', formula)

        # 将公式中的变量名转换为小写，以匹配本地变量字典中的键
        for var in re.findall(r'[a-zA-Z_]+', formula):
            if var.lower() in local_vars and var != var.lower():
                formula = re.sub(r'\b' + var + r'\b', var.lower(), formula)

        # 打印处理后的公式，用于调试
        print(f"处理后的公式: {formula}")
        print(f"本地变量: {local_vars}")

        # 计算表达式
        try:
            # 使用本地变量字典执行公式
            result = eval(formula, {"__builtins__": {}}, local_vars)
            print(f"计算结果: {result}")
            return result
        except Exception as e:
            print(f"公式计算错误: {formula}")
            print(f"错误信息: {str(e)}")
            # 返回一个默认值，而不是抛出异常
            return 0

class EnhancedMetricsCalculator:
    """
    增强版财务指标计算器，支持处理上期和上期末数据
    """

    def __init__(self):
        self.db = DatabaseHandler()
        self.executor = EnhancedFormulaExecutor()
        self.metrics_mapping = self._load_metrics_mapping()

    def _load_metrics_mapping(self):
        """
        从指标名称映射文件加载指标映射
        """
        try:
            df = pd.read_csv('docs/指标名称映射.csv', encoding='utf-8')
            mapping = {}
            for _, row in df.iterrows():
                if pd.notna(row['中文指标名称']) and pd.notna(row['英文指标名称']):
                    mapping[row['中文指标名称']] = {
                        'wind_code': row['英文指标名称'],
                        'formula': row['英文公式'] if pd.notna(row['英文公式']) else None,
                        'lower_bound': row['LowerBound'] if pd.notna(row['LowerBound']) else None,
                        'upper_bound': row['UpperBound'] if pd.notna(row['UpperBound']) else None,
                        'range_value': row['RangeValue'] if pd.notna(row['RangeValue']) else None
                    }
            return mapping
        except Exception as e:
            print(f"加载指标映射失败: {str(e)}")
            return {}

    def _get_company_name(self, stock_code):
        """根据股票代码获取公司名称"""
        query = """
        SELECT CompanyName
        FROM CompanyInfo
        WHERE TickerSymbol=?
        """
        with self.db as db:
            cursor = db.connection.cursor()
            cursor.execute(query, (stock_code,))
            row = cursor.fetchone()

        return row[0] if row else stock_code

    def _get_previous_period(self, date_obj):
        """
        获取上期日期
        对于年报（12-31），上期为去年同期
        对于季报（03-31, 06-30, 09-30），上期为去年同期
        """
        year = date_obj.year
        month = date_obj.month
        day = date_obj.day

        # 上期日期（去年同期）
        previous_date = datetime(year - 1, month, day)

        return previous_date

    def _get_previous_end_period(self, date_obj):
        """
        获取上期末日期
        对于年报（12-31），上期末为去年年末
        对于一季报（03-31），上期末为去年年末
        对于中报（06-30），上期末为去年年末
        对于三季报（09-30），上期末为去年年末
        """
        year = date_obj.year

        # 上期末日期（去年年末）
        previous_end_date = datetime(year - 1, 12, 31)

        return previous_end_date

    def _get_metrics_data(self, stock_code, date_obj):
        """
        从数据库获取指标数据，包括当期、上期和上期末的数据

        参数:
            stock_code (str): 股票代码
            date_obj (datetime): 当期日期

        返回:
            tuple: (当期数据, 历史数据)
        """
        # 获取上期和上期末日期
        previous_date = self._get_previous_period(date_obj)
        previous_end_date = self._get_previous_end_period(date_obj)

        # 格式化日期为字符串
        date_str = date_obj.strftime('%Y%m%d')
        previous_date_str = previous_date.strftime('%Y%m%d')
        previous_end_date_str = previous_end_date.strftime('%Y%m%d')

        # 查询当期数据
        current_query = """
        SELECT WindCode, Value
        FROM WindMetrics
        WHERE TickerSymbol=? AND Date=?
        """

        # 查询上期数据
        previous_query = """
        SELECT WindCode, Value
        FROM WindMetrics
        WHERE TickerSymbol=? AND Date=?
        """

        # 查询上期末数据
        previous_end_query = """
        SELECT WindCode, Value
        FROM WindMetrics
        WHERE TickerSymbol=? AND Date=?
        """

        current_values = {}
        historical_values = {'previous': {}, 'previous_end': {}}

        with self.db as db:
            cursor = db.connection.cursor()

            # 获取当期数据
            cursor.execute(current_query, (stock_code, date_str))
            for row in cursor.fetchall():
                current_values[row[0]] = row[1]

            # 获取上期数据
            cursor.execute(previous_query, (stock_code, previous_date_str))
            for row in cursor.fetchall():
                historical_values['previous'][row[0]] = row[1]

            # 获取上期末数据
            cursor.execute(previous_end_query, (stock_code, previous_end_date_str))
            for row in cursor.fetchall():
                historical_values['previous_end'][row[0]] = row[1]

        return current_values, historical_values

    def _get_metric_formulas_from_db(self):
        """从L2BenchmarkIndicators表获取指标公式"""
        query = """
        SELECT IndicatorNameCN, FormulaDesc, LowerBound, UpperBound, RangeValue
        FROM L2BenchmarkIndicators
        """
        with self.db as db:
            cursor = db.connection.cursor()
            cursor.execute(query)
            rows = cursor.fetchall()

        return {
            row[0]: {
                'formula': row[1],
                'lower_bound': row[2],
                'upper_bound': row[3],
                'range_value': row[4],
                'explanation': ''  # 暂时没有解释字段
            } for row in rows
        }

    def calculate_metrics(self, stock_code, date_str):
        """
        根据股票代码和日期计算财务指标

        参数:
            stock_code (str): 股票代码
            date_str (str): 日期，格式为YYYYMMDD

        返回:
            dict: 包含计算结果的字典
        """
        date_obj = datetime.strptime(date_str, '%Y%m%d')

        # 从数据库获取基础数据
        current_data, historical_data = self._get_metrics_data(stock_code, date_obj)
        if not current_data:
            print(f"未找到股票 {stock_code} 在 {date_str} 的数据")
            return None

        # 从数据库获取指标公式
        formulas = self._get_metric_formulas_from_db()
        if not formulas:
            print("未找到指标公式定义")
            # 如果没有从数据库获取到指标公式，则尝试从指标名称映射文件中获取
            formulas = {}
            for metric_name, mapping in self.metrics_mapping.items():
                formulas[metric_name] = {
                    'formula': mapping.get('formula', ''),
                    'lower_bound': mapping.get('lower_bound'),
                    'upper_bound': mapping.get('upper_bound'),
                    'range_value': mapping.get('range_value'),
                    'explanation': ''
                }

        # 获取公司名称
        company_name = self._get_company_name(stock_code)

        # 计算结果
        results = {
            'stock_code': stock_code,
            'company_name': company_name,
            'metrics': {}
        }

        # 打印当前数据和历史数据，用于调试
        print(f"当前数据: {current_data}")
        print(f"历史数据: {historical_data}")

        for metric_name, formula_info in formulas.items():
            try:
                # 检查是否有对应的Wind指标映射
                if metric_name in self.metrics_mapping:
                    wind_formula = self.metrics_mapping[metric_name]['formula']
                    if wind_formula:
                        # 使用Wind指标公式计算
                        value = self.executor.execute_formula(
                            wind_formula,
                            current_data,
                            historical_data
                        )
                    else:
                        # 如果没有Wind公式，则使用原始公式
                        value = self.executor.execute_formula(
                            formula_info['formula'],
                            current_data,
                            historical_data
                        )
                else:
                    # 使用原始公式
                    value = self.executor.execute_formula(
                        formula_info['formula'],
                        current_data,
                        historical_data
                    )

                results['metrics'][metric_name] = {
                    'value': value,
                    'formula': formula_info['formula'],
                    'wind_formula': self.metrics_mapping.get(metric_name, {}).get('formula'),
                    'lower_bound': formula_info.get('lower_bound'),
                    'upper_bound': formula_info.get('upper_bound'),
                    'range_value': formula_info.get('range_value'),
                    'explanation': formula_info.get('explanation', '')
                }
            except Exception as e:
                print(f"计算指标 {metric_name} 时出错: {str(e)}")
                continue

        return results

# 使用示例
if __name__ == "__main__":
    calculator = EnhancedMetricsCalculator()
    stock_code = "600850.SH"
    date_str = "20231231"

    results = calculator.calculate_metrics(stock_code, date_str)
    if results:
        print(f"股票代码: {results['stock_code']}")
        print(f"公司名称: {results['company_name']}")
        print("指标结果:")
        for metric, data in results['metrics'].items():
            value = data.get('value')
            if value is not None:
                try:
                    print(f"{metric}: {value:.4f}")
                except (TypeError, ValueError):
                    print(f"{metric}: {value}")
            else:
                print(f"{metric}: N/A")

            print(f"公式: {data.get('formula', 'N/A')}")
            if data.get('wind_formula'):
                print(f"Wind公式: {data['wind_formula']}")
            print(f"解释: {data.get('explanation', '')}")
            print("-" * 50)
    else:
        print("计算指标失败")
