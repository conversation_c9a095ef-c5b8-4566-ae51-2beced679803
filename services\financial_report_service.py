#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
财务报表服务类
处理财务报表相关的业务逻辑
"""

import json
import pandas as pd
from typing import List, Dict, Any, Optional
from ..config.database_config import get_db_connection  

class FinancialReportService:
    def __init__(self):
        self.config_cache = {}
        self.windcode_cache = set()
    
    def get_all_windcodes(self) -> List[str]:
        """
        获取所有可用的WindCode列表
        
        Returns:
            WindCode列表
        """
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            query = """
                SELECT DISTINCT WindCode 
                FROM FinancialReportConfig 
                WHERE WindCode IS NOT NULL 
                AND WindCode != '' 
                ORDER BY WindCode
            """
            
            cursor.execute(query)
            windcodes = [row[0] for row in cursor.fetchall()]
            
            conn.close()
            
            # 更新缓存
            self.windcode_cache = set(windcodes)
            
            return windcodes
            
        except Exception as e:
            raise Exception(f"获取WindCode列表失败: {e}")
    
    def get_metrics_by_windcodes(self, windcodes: List[str], 
                                report_date: Optional[str] = None,
                                company_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        根据WindCode列表批量查询指标数据
        
        Args:
            windcodes: WindCode列表
            report_date: 报告日期
            company_id: 公司ID
            
        Returns:
            指标数据列表
        """
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            # 构建查询条件
            windcode_placeholders = ','.join(['?' for _ in windcodes])
            
            query = f"""
                SELECT 
                    _config.WindCode,
                    _config.item,
                    _config.Level,
                    _config.sort,
                    _config.RptName,
                    _wind.Value,
                    _wind.YoYValue,
                    _wind.TickerSymbol AS company_id,
                    _wind.Date AS report_date
                FROM FinancialReportConfig _config 
                LEFT JOIN WindMetrics _wind ON _wind.WindCode = _config.WindCode
                WHERE _config.WindCode IN ({windcode_placeholders})
            """
            
            params = windcodes.copy()
            
            # 添加报告日期条件
            if report_date:
                query += " AND _wind.Date = ?"
                params.append(report_date)
            
            # 添加公司ID条件
            if company_id:
                query += " AND _wind.TickerSymbol = ?"
                params.append(company_id)
            
            query += " ORDER BY _config.RptName, _config.sort"
            
            cursor.execute(query, params)
            columns = [column[0] for column in cursor.description]
            rows = cursor.fetchall()
            
            results = [dict(zip(columns, row)) for row in rows]
            
            conn.close()
            
            # 格式化金额
            for result in results:
                if result.get('value') is not None:
                    try:
                        value = float(result['value'])
                        result['formatted_value'] = f"{value:,.2f}"
                    except (ValueError, TypeError):
                        result['formatted_value'] = str(result['value'])
                else:
                    result['formatted_value'] = ""
            
            return results
            
        except Exception as e:
            raise Exception(f"批量查询指标数据失败: {e}")
    
    def get_report_structure(self, report_type: str) -> Dict[str, Any]:
        """
        获取指定报表类型的结构配置
        
        Args:
            report_type: 报表类型
            
        Returns:
            报表结构配置
        """
        # 这里可以从配置文件或数据库加载报表结构
        # 暂时返回基本结构
        structures = {
            "资产负债表": {
                "sections": [
                    {
                        "title": "流动资产",
                        "type": "header",
                        "items": [
                            "货币资金", "交易性金融资产", "应收票据", 
                            "应收账款", "预付款项", "其他应收款", 
                            "存货", "一年内到期的非流动资产", "其他流动资产"
                        ]
                    },
                    {
                        "title": "非流动资产",
                        "type": "header",
                        "items": [
                            "可供出售金融资产", "持有至到期投资", "长期应收款",
                            "长期股权投资", "投资性房地产", "固定资产",
                            "在建工程", "无形资产", "商誉", "递延所得税资产", "其他非流动资产"
                        ]
                    }
                ]
            },
            "利润表": {
                "sections": [
                    {
                        "title": "营业收入",
                        "type": "header",
                        "items": ["营业收入"]
                    },
                    {
                        "title": "营业成本及费用",
                        "type": "header",
                        "items": [
                            "营业成本", "营业税金及附加", "销售费用",
                            "管理费用", "财务费用", "资产减值损失"
                        ]
                    }
                ]
            },
            "现金流量表": {
                "sections": [
                    {
                        "title": "经营活动产生的现金流量",
                        "type": "header",
                        "items": [
                            "销售商品、提供劳务收到的现金",
                            "收到的税费返还",
                            "收到其他与经营活动有关的现金"
                        ]
                    }
                ]
            }
        }
        
        return structures.get(report_type, {})
    
    def get_complete_report_data(self, report_type: str, 
                               report_date: Optional[str] = None,
                               company_id: Optional[str] = None,
                               format_amounts: bool = True) -> Dict[str, Any]:
        """
        获取指定报表类型的完整数据
        
        Args:
            report_type: 报表类型
            report_date: 报告日期
            company_id: 公司ID
            format_amounts: 是否格式化金额
            
        Returns:
            完整的报表数据
        """
        try:
            print(f"[DEBUG] Starting get_complete_report_data for {report_type}")
            
            # 直接查询FinancialReportConfig和WindMetrics
            from ..config.database_config import get_db_connection
            conn = get_db_connection()
            cursor = conn.cursor()
            
            query = """
                SELECT 
                    _config.WindCode,
                    _config.item,
                    _config.Level,
                    _config.sort,
                    _config.RptName,
                    _wind.Value,
                    _wind.YoYValue
                FROM FinancialReportConfig _config 
                LEFT JOIN WindMetrics _wind ON _wind.WindCode = _config.WindCode
                 and  _config.RptName = ?
            """
            
            params = [report_type]
            
            # 添加报告日期条件
            if report_date:
                query += " AND _wind.Date = ?"
                params.append(report_date)
            
            # 添加公司ID条件
            if company_id:
                query += " AND _wind.TickerSymbol = ?"
                params.append(company_id)
            query+= "  WHERE _config.RptName = ?"
            params.append(report_type)
            query += " ORDER BY _config.sort"
            
            print(f"[DEBUG] Executing query: {query}")
            print(f"[DEBUG] With params: {params}")
            
            cursor.execute(query, params)
            columns = [column[0] for column in cursor.description]
            rows = cursor.fetchall()
            
            results = []
            for row in rows:
                row_dict = dict(zip(columns, row))
                
                # 格式化数据以匹配前端期望
                formatted_item = {
                    'WindCode': row_dict.get('WindCode'),
                    'item': row_dict.get('item'),
                    'Level': row_dict.get('Level'),
                    'sort': row_dict.get('sort'),
                    'RptName': row_dict.get('RptName'),
                    'Value': row_dict.get('Value'),
                    'YoYValue': row_dict.get('YoYValue')
                }
                
                # 格式化金额
                if format_amounts:
                    if formatted_item['Value'] is not None:
                        try:
                            formatted_item['Value'] = float(formatted_item['Value'])
                        except (ValueError, TypeError):
                            pass
                    
                    if formatted_item['YoYValue'] is not None:
                        try:
                            formatted_item['YoYValue'] = float(formatted_item['YoYValue'])
                        except (ValueError, TypeError):
                            pass
                
                results.append(formatted_item)
            
            conn.close()
            
            print(f"[DEBUG] Retrieved {len(results)} records for {report_type}")
            return results
            
        except Exception as e:
            print(f"[ERROR] Error in get_complete_report_data: {e}")
            raise Exception(f"获取完整报表数据失败: {e}")
    
    def _get_windcodes_by_report_type(self, report_type: str) -> List[str]:
        """
        根据报表类型获取相关的WindCode列表
        
        Args:
            report_type: 报表类型
            
        Returns:
            WindCode列表
        """
        # 这里可以从配置文件或数据库获取
        # 暂时返回所有WindCode
        return self.get_all_windcodes()
    
    def _organize_report_data(self, structure: Dict[str, Any], 
                            metrics_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        根据报表结构组织数据
        
        Args:
            structure: 报表结构
            metrics_data: 指标数据
            
        Returns:
            组织后的数据
        """
        organized_data = []
        
        # 创建指标数据的快速查找字典
        metrics_dict = {}
        for metric in metrics_data:
            key = f"{metric.get('metric_name', '')}"
            if key not in metrics_dict:
                metrics_dict[key] = []
            metrics_dict[key].append(metric)
        
        # 根据结构组织数据
        for section in structure.get('sections', []):
            # 添加节标题
            organized_data.append({
                "type": "header",
                "title": section['title'],
                "level": 0,
                "data": None
            })
            
            # 添加节项目
            for item_name in section.get('items', []):
                item_data = {
                    "type": "item",
                    "title": item_name,
                    "level": 1,
                    "data": metrics_dict.get(item_name, [])
                }
                organized_data.append(item_data)
        
        return organized_data
    
    def generate_config_from_excel(self, excel_path: str) -> Dict[str, Any]:
        """
        从Excel文件生成报表配置
        
        Args:
            excel_path: Excel文件路径
            
        Returns:
            生成的配置
        """
        try:
            generator = FinancialReportConfigGenerator()
            config = generator.generate_config_from_excel(excel_path)
            
            # 缓存配置
            self.config_cache = config
            
            return config
            
        except Exception as e:
            raise Exception(f"从Excel生成配置失败: {e}")
    
    def format_amount(self, amount: Any) -> str:
        """
        格式化金额为千分位两位小数
        
        Args:
            amount: 金额值
            
        Returns:
            格式化后的金额字符串
        """
        if amount is None or amount == "":
            return ""
        
        try:
            value = float(str(amount).replace(",", ""))
            return f"{value:,.2f}"
        except (ValueError, TypeError):
            return str(amount)

    def get_company_metrics_for_dashboard(self, report_date: str, company_ids: list = []) -> Dict[str, Any]:
        """
        获取指定日期、公司ID列表和WindCode列表的公司指标数据（含公司名），同时返回L2Metrics表数据
        返回结构：{"wind_metrics": [...], "l2_metrics": [...]} 不再返回merged
        """
        from ..data_access import execute_query
        
        # 查询WindMetrics
        sql = '''
            SELECT
                _company.CompanyId,
                _company.CompanyName,
                _metri.WindCode,
                _metri.value,
                _metri.OriValue,
                _metri.TickerSymbol
            FROM
                WindMetrics _metri
                LEFT JOIN CompanyInfo _company ON _company.CompanyID = _metri.CompanyId
            WHERE
                _metri.Date = ?
        '''
        params = [report_date]
        if company_ids and isinstance(company_ids, list) and len(company_ids) > 0:
            placeholders = ','.join(['?' for _ in company_ids])
            sql += f" AND _metri.TickerSymbol IN ({placeholders})"
            params.extend(company_ids)
        
        
        windcodes = [
            'oper_rev', 'tot_profit', 'deductedprofit', 'two_fund_net', 'rd_exp', 
            'revenue_per_rd_personnel', 'employee', 'div_aualcashdividend', 'non_recurring_profit_loss',
            'debt_to_assets_ratio', 'total_asset_turnover', 'gross_margin', 'three_fee_ratio', 
            'net_profit_margin', 'roe_basic', 'cash_cycle', 'inventory_turnover', 'receivables_turnover',
            'oper_rev_growth_rate', 'operating_cash_ratio',
            'fix_assets', 'cont_assets', 'monetary_cap', 'oth_cur_assets', 'oth_non_cur_assets', 'intang_assets',
            'st_borrow', 'bonds_payable', 'acct_payable', 'oth_cur_liab', 'oth_non_cur_liab',
            'stmnote_rdexp']
        windcode_placeholders = ','.join(['?' for _ in windcodes])
        sql += f" AND WindCode IN ({windcode_placeholders})"
        params.extend(windcodes)
        wind_metrics = execute_query(sql, params)

        # 查询L2Metrics
        l2_sql = '''
                SELECT _l2.[ID]
                ,_l2.[TickerSymbol]
                ,_l2.[CompanyName]
                ,_l2.[ReportDate]
                ,_l2.[IndicatorNameCN]
                ,_l2.[IndicatorNameEN]
                ,_l2.[FormulaDesc]
                ,_l2.[FormulaEN]
                ,_l2.[CalculatedValue]
                ,_l2.[LowerBound]
                ,_l2.[UpperBound]
                ,_l2.[RangeValue]
                ,_l2.[EvaluationResult]
                ,_l2.[CalculationTime]
                ,_l2.[Remarks]
                ,_l2.[CreateTime]
                ,_l2.[IndicatorDesc]
                ,_l2.[Formula]
                ,_l2.[TranslatedFormula]
                ,_com.CompanyID
            FROM [FinancialReport].[dbo].[L2Metrics] _l2
            left join  CompanyInfo _com on _l2.TickerSymbol=_com.TickerSymbol
                        WHERE [ReportDate] = ?
        '''
        l2_params = [report_date]
        if company_ids and isinstance(company_ids, list) and len(company_ids) > 0:
            placeholders = ','.join(['?' for _ in company_ids])
            l2_sql += f" AND _com.TickerSymbol IN ({placeholders})"
            l2_params.extend(company_ids)
       
        l2_metrics = execute_query(l2_sql, l2_params)

        return {
            'wind_metrics': wind_metrics,
            'l2_metrics': l2_metrics
        }