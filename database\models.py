from dataclasses import dataclass
from datetime import date


@dataclass
class Company:
    company_id: int = None
    name: str = None
    short_name: str = None
    stock_code: str = None
    is_active: bool = True


@dataclass
class FinancialReport:
    report_id: int = None
    company_id: int = None
    report_date: date = None
    date_value: str = None
    report_type: str = None
    raw_data: str = None
