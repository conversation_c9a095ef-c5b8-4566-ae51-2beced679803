import React from 'react';

const FilterPanel = ({
  years = [],
  quarters = [],
  companies = [],
  onYearChange,
  onQuarterChange,
  onCompanyChange,
  onSearch
}) => {
  // 生成年份选项：2022 ~ 当前年
  const currentYear = new Date().getFullYear();
  const yearOptions = [];
  for (let y = 2022; y <= currentYear; y++) {
    yearOptions.push(y);
  }

  return (
    <div style={{
      display: 'flex',
      alignItems: 'center',
      gap: 16,
      marginBottom: 24
    }}>
      <label style={{ display: 'flex', alignItems: 'center', gap: 4 }}>
        年:
        <select multiple value={years} onChange={onYearChange} style={{ minWidth: 80, height: 32 }}>
          {yearOptions.map(y => (
            <option key={y} value={y}>{y}</option>
          ))}
        </select>
      </label>
      <label style={{ display: 'flex', alignItems: 'center', gap: 4 }}>
        季度:
        <select multiple value={quarters} onChange={onQuarterChange} style={{ minWidth: 80, height: 32 }}>
          <option value="Q1">Q1</option>
          <option value="Q2">Q2</option>
          <option value="Q3">Q3</option>
          <option value="Q4">Q4</option>
        </select>
      </label>
      <label style={{ display: 'flex', alignItems: 'center', gap: 4 }}>
        公司:
        <select multiple value={companies} onChange={onCompanyChange} style={{ minWidth: 120, height: 32 }}>
          <option value="companyA">公司 A</option>
          <option value="companyB">公司 B</option>
          <option value="companyC">公司 C</option>
        </select>
      </label>
      <button
        onClick={onSearch}
        style={{
          height: 36,
          padding: '0 20px',
          background: '#1783FF',
          color: '#fff',
          border: 'none',
          borderRadius: 4,
          cursor: 'pointer',
          fontSize: 16
        }}
      >
        检索
      </button>
    </div>
  );
};

export default FilterPanel;
