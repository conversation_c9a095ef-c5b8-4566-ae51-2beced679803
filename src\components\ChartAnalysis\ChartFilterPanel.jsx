import React from 'react';
import { Select } from 'antd';

const { Option } = Select;

/**
 * 图表分析筛选面板
 * @param {Object} props
 * @param {Array} props.years - 年份列表
 * @param {number|string} props.selectedYear - 当前选中年份
 * @param {Array} props.quarters - 季度列表
 * @param {string|number} props.selectedQuarter - 当前选中季度
 * @param {Array} props.companies - 公司列表（{id, name}）
 * @param {Array} props.selectedCompanies - 选中公司id数组
 * @param {Function} props.onYearChange
 * @param {Function} props.onQuarterChange
 * @param {Function} props.onCompanyChange
 */
const ChartFilterPanel = ({
  years = [],
  selectedYear,
  quarters = [],
  selectedQuarter,
  companies = [],
  selectedCompanies = [],
  onYearChange,
  onQuarterChange,
  onCompanyChange,
  style = {}, // 新增，允许外部传 style
  children, // 新增，支持插槽
}) => {
  // 限制最多选择3个公司
  const handleCompanyChange = (value) => {
    if (value.length > 3) {
      window?.antd?.message?.warning?.('最多只能选择3家公司');
      return;
    }
    onCompanyChange(value);
  };

  return (
    <div style={{ display: 'flex', gap: 5, marginBottom: 0, alignItems: 'center', flex: 1, ...style }}>
      <Select
        value={selectedYear}
        onChange={onYearChange}
        style={{ width: 100 }}
        placeholder="年份"
      >
        {years.map((year) => (
          <Option key={year} value={year}>{year}年</Option>
        ))}
      </Select>
      <Select
        value={selectedQuarter}
        onChange={onQuarterChange}
        style={{ width: 120 }}
        placeholder="季度"
      >
        {quarters.map((q) => (
          <Option key={q.value} value={q.value}>{q.label}</Option>
        ))}
      </Select>
      <Select
        mode="multiple"
        value={selectedCompanies}
        onChange={handleCompanyChange}
        placeholder="可比公司(多选)"
        allowClear
        showSearch
        optionFilterProp="children"
        style={{ minWidth: 200, maxWidth: 320, flex: 1.5 }}
        maxTagCount={3}
      >
        {companies.map((c) => (
          <Option key={c.id} value={c.id}>{c.name}</Option>
        ))}
      </Select>
      {children}
    </div>
  );
};

export default ChartFilterPanel; 