from flask import current_app
from ..repositories.report_time_repository import ReportTimeRepository

class ReportTimeService:
    @staticmethod
    def get_report_times():
        """获取定期报告公布时间数据"""
        try:
            return ReportTimeRepository.get_report_times()
        except Exception as e:
            current_app.logger.error(f"Error getting report times: {e}")
            return {"error": str(e)}
    
    @staticmethod
    def get_latest_l2metrics_report_date():
        """获取L2Metrics表中最新的报告日期"""
        try:
            return ReportTimeRepository.get_latest_l2metrics_report_date()
        except Exception as e:
            current_app.logger.error(f"Error getting latest L2Metrics report date: {e}")
            return {"error": str(e)} 