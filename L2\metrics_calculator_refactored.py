# L2/metrics_calculator_refactored.py
import logging
from datetime import datetime
from .db_structure_manager import DatabaseStructureManager
from .metric_definition_loader import MetricDefinitionLoader
from .metric_data_loader import MetricDataLoader
from .metric_result_saver import MetricResultSaver
from .formula_executor import FormulaExecutor # Assuming formula_executor is already in L2

class MetricsCalculatorRefactored:
    """
    重构后的财务指标计算器，协调各个组件完成计算。
    """

    def __init__(self, logger=None):
        # 初始化日志记录器
        self.logger = logger or self._setup_logger()
        
        # 初始化各个组件
        self.db_structure_manager = DatabaseStructureManager()
        self.definition_loader = MetricDefinitionLoader()
        self.data_loader = MetricDataLoader()
        self.result_saver = MetricResultSaver()

        # 加载指标映射 (可以在初始化时加载，或在计算时按需加载)
        self.metrics_mapping = self.definition_loader.load_metrics_mapping()
        self.metrics_definitions = self.definition_loader.load_metrics_definitions()
        # 使用 L2 下的 FormulaExecutor，并传入指标定义
        self.executor = FormulaExecutor(self.definition_loader.indicator_definitions,  self.metrics_definitions)

        # 确保数据库结构 (可以在初始化时执行一次，或者根据需要调用)
        # self.db_structure_manager.ensure_database_structure()
        
        self.logger.info("MetricsCalculatorRefactored 初始化完成")

    def check_data_already_processed(self, stock_code, date_str):
        """
        检查指定股票和日期的数据是否已经处理过
        
        参数:
            stock_code (str): 股票代码
            date_str (str): 日期字符串，格式为YYYYMMDD
            
        返回:
            bool: True表示已处理过，False表示未处理过
        """
        try:
            date_obj = datetime.strptime(date_str, '%Y%m%d')
            report_date = date_obj.strftime('%Y-%m-%d')
            
            with self.data_loader.db as db:
                cursor = db.connection.cursor()
                
                # 检查L2Metrics表中是否已有该股票该日期的数据
                cursor.execute("""
                    SELECT COUNT(*) FROM L2Metrics 
                    WHERE TickerSymbol = ? AND ReportDate = ?
                """, (stock_code, report_date))
                
                count = cursor.fetchone()[0]
                
                if count > 0:
                    self.logger.info(f"股票 {stock_code} 在 {date_str} 的数据已处理过，共 {count} 条记录")
                    return True
                else:
                    self.logger.info(f"股票 {stock_code} 在 {date_str} 的数据未处理过")
                    return False
                    
        except Exception as e:
            self.logger.error(f"检查数据是否已处理时出错: {e}")
            # 如果检查失败，默认返回False，允许处理
            return False

    def get_unprocessed_data(self, stock_codes, dates):
        """
        获取未处理的数据列表
        
        参数:
            stock_codes (list): 股票代码列表
            dates (list): 日期列表，格式为YYYYMMDD
            
        返回:
            list: 未处理的数据列表，每个元素为 (stock_code, date_str)
        """
        unprocessed = []
        
        for stock_code in stock_codes:
            for date_str in dates:
                if not self.check_data_already_processed(stock_code, date_str):
                    unprocessed.append((stock_code, date_str))
        
        self.logger.info(f"找到 {len(unprocessed)} 个未处理的数据项")
        return unprocessed

    def _setup_logger(self):
        """设置日志记录器"""
        logger = logging.getLogger('MetricsCalculator')
        if not logger.handlers:
            logger.setLevel(logging.INFO)
            
            # 创建控制台处理器
            console_handler = logging.StreamHandler()
            console_handler.setLevel(logging.INFO)
            console_formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            console_handler.setFormatter(console_formatter)
            logger.addHandler(console_handler)
            
            # 创建文件处理器
            file_handler = logging.FileHandler(
                f'metrics_calculation_{datetime.now().strftime("%Y%m%d")}.log',
                encoding='utf-8'
            )
            file_handler.setLevel(logging.DEBUG)
            file_formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
            )
            file_handler.setFormatter(file_formatter)
            logger.addHandler(file_handler)
        
        return logger


    def calculate_metrics(self, stock_code, date_str, save_to_db=True, force_recalculate=False):
        """
        根据股票代码和日期计算财务指标

        参数:
            stock_code (str): 股票代码
            date_str (str): 日期，格式为YYYYMMDD
            save_to_db (bool): 是否保存结果到数据库，默认为True
            force_recalculate (bool): 是否强制重新计算，即使数据已存在

        返回:
            dict: 包含计算结果的字典, 或者在无法获取数据时返回 None
        """
        self.logger.info(f"开始计算股票 {stock_code} 在 {date_str} 的财务指标")
        
        try:
            date_obj = datetime.strptime(date_str, '%Y%m%d')
        except ValueError:
            error_msg = f"错误: 日期格式无效 '{date_str}'。请使用 YYYYMMDD 格式。"
            self.logger.error(error_msg)
            print(error_msg)
            return None

        # 检查数据是否已处理过
        if not force_recalculate and self.check_data_already_processed(stock_code, date_str):
            self.logger.info(f"股票 {stock_code} 在 {date_str} 的数据已处理过，跳过计算")
            return {
                'stock_code': stock_code,
                'company_name': self.data_loader.get_company_name(stock_code),
                'report_date': date_obj.strftime('%Y-%m-%d'),
                'metrics': {},
                'statistics': {
                    'success_count': 0,
                    'failure_count': 0,
                    'execution_time': 0,
                    'error_logs': [],
                    'skipped': True,
                    'skip_reason': '数据已处理过'
                }
            }

        start_time = datetime.now()

        # 1. 确保数据库结构 (如果不在初始化时执行)
        # self.db_structure_manager.ensure_database_structure()

        # 2. 加载基础数据
        self.logger.info(f"正在加载股票 {stock_code} 在 {date_str} 的基础数据...")
        current_data, previous_data, previous_end_data = self.data_loader.get_metrics_data(stock_code, date_obj)
        if current_data is None:
            error_msg = f"无法获取 {stock_code} 在 {date_str} 的基础数据，计算中止。"
            self.logger.error(error_msg)
            print(error_msg)
            # 可以在这里记录一个总体错误日志
            if save_to_db:
                 self.result_saver.log_calculation(
                     stock_code=stock_code,
                     report_date=date_obj.strftime('%Y-%m-%d'),
                     success_count=0,
                     failure_count=0, # 或者标记为特殊失败状态
                     execution_time=0,
                     log_message=f"计算中止：未找到股票 {stock_code} 在 {date_str} 的数据"
                 )
            return None # 返回 None 表示计算未执行
        
        self.logger.info(f"成功加载基础数据：当期数据 {len(current_data)} 项，上期数据 {len(previous_data)} 项，上期末数据 {len(previous_end_data)} 项")

        # 3. 加载指标公式 (优先从DB获取完整定义)
        self.logger.info("正在加载指标公式定义...")
        formulas = self.definition_loader.get_metric_formulas_from_db()
        if not formulas:
            warning_msg = "警告: 未从数据库找到指标公式定义，将尝试使用指标映射文件中的公式"
            self.logger.warning(warning_msg)
            print(warning_msg)
            # 从 metrics_mapping 构建一个简化的 formulas 字典
            formulas = {}
            for metric_name, mapping_info in self.metrics_mapping.items():
                 formulas[metric_name] = {
                     'formula': mapping_info.get('formula'),
                     'formula_desc': mapping_info.get('formula_desc'), # 可能没有
                     'indicator_name_en': mapping_info.get('wind_code'),
                     'lower_bound': mapping_info.get('lower_bound'),
                     'upper_bound': mapping_info.get('upper_bound'),
                     'range_value': mapping_info.get('range_value')
                     # 'indicator_desc' 等字段可能缺失
                 }
            if not formulas:
                 error_msg = "错误: 无法从数据库或映射文件加载任何指标公式，计算中止。"
                 self.logger.error(error_msg)
                 print(error_msg)
                 return None
        
        self.logger.info(f"成功加载 {len(formulas)} 个指标公式定义")


        # 4. 获取公司名称
        company_name = self.data_loader.get_company_name(stock_code)
        self.logger.info(f"公司名称: {company_name}")

        # 5. 获取所有指标的详细定义信息 (用于保存结果)
        benchmark_indicators = self.definition_loader.get_all_benchmark_indicators()
        self.logger.info(f"已获取 {len(benchmark_indicators)} 个指标的定义信息 (按名称索引)")
        print(f"已获取 {len(benchmark_indicators)} 个指标的定义信息 (按名称索引)")


        # 6. 执行计算并保存结果
        results = {
            'stock_code': stock_code,
            'company_name': company_name,
            'report_date': date_obj.strftime('%Y-%m-%d'),
            'metrics': {}
        }
        success_count = 0
        failure_count = 0
        error_logs = [] # 用于返回结果字典

        self.logger.info(f"开始计算 {len(formulas)} 个指标...")
        
        # 迭代从数据库获取的公式定义
        for i, (metric_name_en, formula_info) in enumerate(formulas.items(), 1):
            self.logger.debug(f"计算进度: {i}/{len(formulas)} - {metric_name_en}")
            metric_name_cn=formula_info.get('indicator_name_cn')
            calculated_value = None
            error_message = None
            processed_formula_on_error = None
            indicator_name_en = formula_info.get('indicator_name_en', '') # 从公式定义获取EN名称
            # 如果公式定义中没有，尝试从 mapping 中获取
            if not indicator_name_en and indicator_name_en in self.metrics_mapping:
                 indicator_name_en = self.metrics_mapping[indicator_name_en].get('wind_code', '')

            # 获取对应的 benchmark 信息，优先用 EN 名称查找，其次用 CN 名称
            benchmark_info = benchmark_indicators.get(indicator_name_en)

            # 确定要使用的公式 (优先使用 L2BenchmarkIndicators.Formula)
            formula_to_execute = formula_info.get('formula')
            formula_en_desc = self.metrics_mapping.get(indicator_name_en, {}).get('formula') # Wind公式描述

            if not formula_to_execute:
                 # 如果 L2BenchmarkIndicators.Formula 为空，尝试使用映射文件中的 Wind 公式
                 formula_to_execute = formula_en_desc
                 if not formula_to_execute:
                     error_message = "指标缺少有效的计算公式"
                     self.logger.warning(f"指标 {metric_name_en} ({indicator_name_en}) 缺少计算公式，跳过计算。")
                     print(f"指标 {metric_name_en} ({indicator_name_en}) 缺少计算公式，跳过计算。")


            if not error_message:
                try:
                    # 打印公式和数据，用于调试
                    self.logger.debug(f"执行公式: {formula_to_execute}")
                    self.logger.debug(f"当期数据键: {list(current_data.keys())[:10]}...")
                    self.logger.debug(f"上期数据键: {list(previous_data.keys())[:10]}...")
                    self.logger.debug(f"上期末数据键: {list(previous_end_data.keys())[:10]}...")

                    # 执行计算
                    calculated_value = self.executor.execute_formula(
                        formula_to_execute,
                        current_data,
                        previous_data,
                        previous_end_data
                    )
                    success_count += 1
                    self.logger.debug(f"指标 {metric_name_en} 计算成功: {calculated_value.get('result') if calculated_value else 'None'}")
                except Exception as e:
                    error_message = str(e)
                    processed_formula_on_error = getattr(e, 'processed_formula', formula_to_execute) # 记录出错时的公式
                    self.logger.error(f"计算指标 {metric_name_en} ({indicator_name_en}) 时出错: {error_message}")
                    print(f"计算指标 {metric_name_en} ({indicator_name_en}) 时出错: {error_message}")
                    failure_count += 1
                    error_logs.append({
                        'indicator_name_cn': metric_name_cn,
                        'indicator_name_en': indicator_name_en,
                        'formula': formula_to_execute, # 记录实际执行的公式
                        'error_message': error_message,
                        'processed_formula': processed_formula_on_error
                    })
            
            # 准备结果字典条目
            results['metrics'][metric_name_en] = {
                'value': calculated_value.get('result') if calculated_value else None,
                'translated_formula': calculated_value.get('translated_formula') if calculated_value else None,
                'formula': formula_info.get('formula'), # L2BenchmarkIndicators.Formula
                'formula_desc': formula_info.get('formula_desc'), # L2BenchmarkIndicators.FormulaDesc
                'wind_formula': formula_en_desc, # 映射文件中的 Wind 公式
                'indicator_name_en': indicator_name_en,
                    'indicator_name_cn': metric_name_cn,
                'lower_bound': formula_info.get('lower_bound'),
                'upper_bound': formula_info.get('upper_bound'),
                'range_value': formula_info.get('range_value'),
                'lower_bound_label': formula_info.get('lower_bound_label'),
                'upper_bound_label': formula_info.get('upper_bound_label'),
                'error': error_message # 添加错误信息到结果
            }

            # 保存结果或错误到数据库
            if save_to_db:
                if error_message is None: # 如果计算成功
                    self.result_saver.save_metric_to_db(
                        stock_code=stock_code,
                        company_name=company_name,
                        report_date=date_obj.strftime('%Y-%m-%d'),
                        indicator_name_cn=metric_name_cn,
                        indicator_name_en=indicator_name_en,
                        formula_desc=formula_info.get('formula_desc'), # 传递 FormulaDesc
                        formula_en=formula_en_desc, # 传递 Wind 公式
                        calculated_value=calculated_value['result'],
                        lower_bound=formula_info.get('lower_bound'),
                        upper_bound=formula_info.get('upper_bound'),
                        range_value=formula_info.get('range_value'),
                        benchmark_info=benchmark_info, # 传递完整的 benchmark 信息
                        translated_formula=calculated_value['translated_formula'] # 传递翻译后的公式
                    )
                else: # 如果计算失败
                     self.result_saver.log_calculation_error(
                         stock_code=stock_code,
                         report_date=date_obj.strftime('%Y-%m-%d'),
                         indicator_name_cn=metric_name_cn,
                         indicator_name_en=indicator_name_en,
                         formula_desc=formula_info.get('formula_desc'), # 传递 FormulaDesc
                         formula_en=formula_en_desc, # 传递 Wind 公式
                         error_message=error_message,
                         processed_formula=processed_formula_on_error,
                         benchmark_info=benchmark_info # 传递完整的 benchmark 信息
                     )

        # 7. 计算执行时间并记录总体日志
        end_time = datetime.now()
        execution_time = (end_time - start_time).total_seconds()

        log_msg = f"计算完成: 成功 {success_count}, 失败 {failure_count}, 耗时 {execution_time:.3f} 秒"
        self.logger.info(log_msg)
        print(log_msg)
        if save_to_db:
            self.result_saver.log_calculation(
                stock_code=stock_code,
                report_date=date_obj.strftime('%Y-%m-%d'),
                success_count=success_count,
                failure_count=failure_count,
                execution_time=execution_time,
                log_message=log_msg
            )

        # 8. 添加统计信息到返回结果
        results['statistics'] = {
            'success_count': success_count,
            'failure_count': failure_count,
            'execution_time': execution_time,
            'error_logs': error_logs, # 返回详细错误日志列表
            'skipped': False
        }

        self.logger.info(f"股票 {stock_code} 在 {date_str} 的指标计算完成")
        return results
