# 1. 使用官方 Ubuntu 22.04 (Jammy) 镜像
FROM harbor.shecc.com/fms/financialcrossanalyzerapi:1260

# 设置非交互式安装，避免时区等问题
ENV DEBIAN_FRONTEND=noninteractive
# 3. 配置 OpenSSL 安全等级
USER root
RUN if [ -f /etc/ssl/openssl.cnf ]; then \
        sed -i 's/DEFAULT:@SECLEVEL=2/DEFAULT:@SECLEVEL=0/g' /etc/ssl/openssl.cnf; \
    fi
RUN if [ -f /etc/ssl/openssl.cnf ]; then \
        sed -i 's/DEFAULT:@SECLEVEL=2/DEFAULT:@SECLEVEL=0/g' /etc/ssl/openssl.cnf && \
        echo "=== 修改后的配置 ===" && \
        grep -n "SECLEVEL" /etc/ssl/openssl.cnf || echo "未找到SECLEVEL配置"; \
    else \
        echo "OpenSSL配置文件不存在"; \
    fi
USER 1001

# 3. 设置工作目录
WORKDIR /app

# 4. 复制并安装 Python 依赖（使用 poetry）
COPY pyproject.toml ./api/pyproject.toml
COPY poetry.lock ./api/poetry.lock
RUN pip install --no-cache-dir poetry && cd api && poetry config virtualenvs.create false && poetry install --no-root

# 5. 复制项目文件
COPY . ./api/

# 6. 暴露端口
EXPOSE 5000

# 7. 启动应用
CMD ["gunicorn", "--bind", "0.0.0.0:5000", "api:app"]