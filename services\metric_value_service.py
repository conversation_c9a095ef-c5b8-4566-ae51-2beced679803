from flask import current_app
from ..repositories.metric_repository import MetricRepository

class MetricValueService:
    @staticmethod
    def get_values(table_name, report_date=None, metrics=None, companies=None):
        """获取指标值数据"""
        try:
            return MetricRepository.get_metric_values(table_name, report_date, metrics, companies)
        except Exception as e:
            current_app.logger.error(f"Error getting metric values: {e}")
            return {"error": str(e)}
