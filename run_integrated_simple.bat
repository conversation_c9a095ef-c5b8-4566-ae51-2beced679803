@echo off
chcp 65001 >nul
echo ============================================================
echo 整合Wind数据导入和财务指标计算
echo ============================================================
echo.

echo 🔍 检查Python环境...
python --version
if %errorlevel% neq 0 (
    echo ❌ Python未安装或未添加到PATH
    pause
    exit /b 1
)

echo.
echo 📋 使用说明:
echo   1. 完整流程（导入+计算）: 直接运行此脚本
echo   2. 仅计算指标: 添加 --skip-import 参数
echo   3. 强制重新计算: 添加 --force-recalculate 参数
echo   4. 指定股票: 添加 --stock-code 股票代码 参数
echo   5. 仅检查数据: 添加 --check-only 参数
echo.

echo 🚀 开始处理...
echo.

REM 使用智能默认值（无需指定参数）
python integrated_processor.py

if %errorlevel% equ 0 (
    echo.
    echo 🎉 处理完成！
) else (
    echo.
    echo ❌ 处理失败，请检查错误信息
)

echo.
pause 