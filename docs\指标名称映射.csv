序号,中文指标名称,英文指标名称,指标解释,公式,英文公式,LowerBound,UpperBound,RangeValue,FormulaConversion
1,营业收入,oper_rev,公司在一定期间内通过销售商品或提供劳务所获得的总收入。,营业收入,oper_rev,,,
2,营业收入增长率（%）,oper_rev_growth_rate,反映公司营业收入的增长情况，衡量公司业务扩展能力。,=(本期营业收入 - 上期营业收入) / 上期营业收入×  100%,=(oper_rev - oper_rev[上期]) / oper_rev[上期]×  100%,-0.0500,0.1500,"<-5%,较差\n>15%,优秀"
3,利润总额,tot_profit,公司在一定期间内实现的全部利润，包括营业利润和非营业利润。,利润总额,tot_profit,,,
4,利润总额增长率（%）,tot_profit_growth_rate,公司净利润中归属于上市公司股东的部分。,=（本期利润总额-上期利润总额）/abs（上期利润总额）×  100%,=(tot_profit - tot_profit[上期]) / abs(tot_profit[上期])×  100%,-0.1000,0.1000,"<-10%,较差\n>10%,优秀"
5,净利润,net_profit_is,公司在一定期间内实现的税后利润，反映公司最终盈利水平。,净利润,net_profit_is,,,
6,归属于上市公司股东的净利润,np_belongto_parcomsh,公司净利润中归属于上市公司股东的部分。,归属于上市公司股东的净利润,np_belongto_parcomsh,,,
7,归属于上市公司股东的除非经常性损益后的净利润,deductedprofit,反映公司持续经营能力。,归属于上市公司股东的除非经常性损益后的净利润,deductedprofit,,,
8,毛利率（%）,gross_margin,反映公司销售商品的盈利能力，毛利率越高，盈利能力越强。,=（营业收入 - 营业成本） / 营业收入× 100%,=(oper_rev - oper_cost) / oper_rev× 100%,,,
9,三费费率（%）,three_fee_ratio,衡量公司费用控制能力。,=（销售费用 + 管理费用 + 研发费用） / 营业收入 × 100%,=(selling_dist_exp + gerl_admin_exp + rd_exp) / oper_rev × 100%,,,
10,人事费用占比,personnel_cost_ratio,衡量公司人力资源成本的控制能力和运营效率。,=销售费用、管理费用、研发费用附注的人事费用合计/（销售费用 + 管理费用 + 研发费用）,=personnel_cost_total/(selling_dist_exp + gerl_admin_exp + rd_exp),,,
11,人事费用增长率（%）,personnel_cost_growth_rate,衡量公司人力资源成本的变化趋势。,=本期人事费用/上期人事费用-1,=personnel_cost/personnel_cost[上期]-1,,,,
12,净利率（%）,net_profit_margin,反映公司净利润占营业收入的比例，衡量公司盈利能力。,=净利润 / 营业收入 × 100%,=net_profit_is / oper_rev × 100%,-0.1000,0.1500,"<-10%,较差\n>15%,优秀"
13,净资产收益率（%）,roe,反映公司净资产的使用效率，衡量公司为股东创造价值的能力。,本期归属于母公司净利润/[(上期末归属母公司股东权益+本期末归属母公司股东权益)/2]× 100,np_belongto_parcomsh/[(eqy_belongto_parcomsh[上期末]+eqy_belongto_parcomsh)/2]× 100,-0.0500,0.1500,"<-5%,较差\n>15%,优秀"
14,归属于母公司所有者权益合计,eqy_belongto_parcomsh,归属于母公司所有者权益 （或股东权益）合计,归属于母公司所有者权益 （或股东权益）合计,eqy_belongto_parcomsh,,,
15,股东权益合计,tot_equity,衡量公司净资产规模，体现公司财务实力和股东权益的保障程度。,所有者权益（或股东权益）合计,tot_equity,,,
16,资本回报率（%）,roic,"反映公司全部投入资本的回报情况，衡量公司资本使用效率。","本期归属于母公司净利润*2/(上期末全部投入资本+本期末全部投入资本）× 100，\n其中全部投入资本：归属于母公司股东权益合计+短期借款+交易性金融负债+一年内到期的非流动负债+应付债券+长期借款+租赁负债","np_belongto_parcomsh*2/(total_invested_capital[上期末]+total_invested_capital）× 100，\n其中全部投入资本：eqy_belongto_parcomsh+st_borrow+wgsd_liabs_trading+non_cur_liab_due_within_1y+bonds_payable+lt_borrow+lease_obligation",-0.0500,0.1000,"<-5%,较差\n>10%,优秀"
17,资产负债率（%）,debt_to_assets_ratio,反映公司负债水平，衡量公司财务风险。,=负债总额/资产总额× 100%,=tot_liab/tot_assets× 100%,0.5100,0.8600,"<51%,优秀\n>86%,较差"
18,利息保障倍数,interest_coverage_ratio,"反映公司支付利息的能力，倍数越高，支付利息的能力越强。","=EBITDA/利息费用\n1）EBITDA为利润总额+利息费用（不含资本化利息支出）+折旧与摊销（①固定资产折旧、油气资产折耗、生产性生物资产折旧 ②利息费用 ③使用权资产摊销 ④无形资产摊销 ⑤长期待摊费用摊销）\n2）利息费用包括费用化（年报：利息费用）及资本化利息支出（年报：其中：本期利息资本化金额）；","=EBITDA/fin_int_exp\n1）EBITDA为tot_profit+fin_int_exp（不含资本化利息支出）+depr_fa_coga_dpba+depre_prop_right_use+amort_intang_assets+amort_lt_deferred_exp\n2）利息费用包括费用化（fin_int_exp）及资本化利息支出（stmnote_finexp_13）；",,,
19,流动比率,current_ratio,反映公司短期偿债能力，比率越高，短期偿债能力越强。,= 流动资产 / 流动负债,= tot_cur_assets / tot_cur_liab,,,
20,速动比率,quick_ratio,反映公司短期偿债能力，比率越高，短期偿债能力越强。,= （流动资产-存货） / 流动负债,= (tot_cur_assets - inventories) / tot_cur_liab,0.5000,1.5000,"<0.5,较差\n>1.5,优秀"
21,两金净额,two_fund_net,反映公司应收账款和存货的总额，衡量公司资产状况和流动性风险。,=应收账款 + 存货,= acct_rcv + inventories,,,
22,两金净额占营业收入比率（%）,two_fund_to_revenue_ratio,衡量公司资产周转效率和营运资金占用情况。,=两金净额 / 营业收入 × 100%,=two_fund_net / oper_rev × 100%,,,
23,两金净额占流动资产比率（%）,two_fund_to_current_assets_ratio,反映公衡量公司流动资产的构成及其流动性风险。,=两金净额 / 流动资产 × 100%,=two_fund_net / tot_cur_assets × 100%,0.0700,0.5500,"<7%,优秀\n>55%,较差"
24,存货周转率,inventory_turnover,反映公司存货的周转速度，衡量公司存货管理效率。,本期营业成本/[(上期末存货净额+2本期末存货净额)/2],oper_cost/[(inventories[上期末]+2inventories)/2],0.1000,20.0000,"<0.1,较差\n>20,优秀"
25,应收账款周转率,receivables_turnover,反映公司应收账款的回收速度，衡量公司应收账款管理效率。,本期营业收入/[(上期末应收账款+本期末应收账款)/2],oper_rev/[(acct_rcv[上期末]+acct_rcv)/2],1.5000,12.5000,"<1.5,较差\n>12.5,优秀"
26,总资产周转率,total_asset_turnover,反映公司全部资产的使用效率，衡量公司资产运营能力。,本期营业收入/[(上期末资产总计+本期末资产总计)/2]× 100,oper_rev/[(tot_assets[上期末]+tot_assets)/2]× 100,-0.1000,1.5000,"<-0.1,较差\n>1.5,优秀"
27,净资产周转率,equity_turnover,反映公司净资产的使用效率，衡量公司净资产运营能力。,本期营业收入/[(上期末归属于上市公司股东的净资产+本期末净归属于上市公司股东的净资产)/2]× 100,oper_rev/[(eqy_belongto_parcomsh[上期末]+eqy_belongto_parcomsh)/2]× 100,,,,
28,经营活动产生的现金流量净额,net_cash_flows_oper_act,衡量公司经营活动现金流状况。,年报现金流量表：经营活动产生的现金流量净额,net_cash_flows_oper_act,,,
29,盈余现金保障倍数,cash_earnings_coverage,衡量公司偿还债务能力。,=经营活动产生的现金流量净额/净利润,=net_cash_flows_oper_act/net_profit_is,-3.0000,3.0000,"<-3,较差\n>3,优秀"
30,营业现金比率（%）,operating_cash_ratio,衡量公司经营活动现金流质量。,= 经营活动产生的现金流量净额 / 营业收入 × 100%,= net_cash_flows_oper_act / oper_rev × 100%,-0.1500,0.2000,"<-15%,较差\n>20%,优秀"
31,营业收现率（%）,cash_collection_ratio,衡量公司销售回款能力。,= 销售商品、提供劳务收到的现金（现金流量表中经营活动现金净流量的细项）/营业收入,= cash_recp_sg_and_rs / oper_rev,,,
32,净利润现金比率（%）,net_profit_cash_ratio,衡量公司利润质量。,= 经营活动产生的现金流量净额 / 净利润 × 100%,= net_cash_flows_oper_act / net_profit_is × 100%,,,
33,现金流动负债比率（％）,cash_to_current_liabilities_ratio,衡量公司短期偿债能力。,=经营活动产生的现金流量净额／年末流动负债x100％,=net_cash_flows_oper_act／tot_cur_liabx100％,-0.3000,0.2500,"<-30%,较差\n>25%,优秀"
34,研发支出合计,rd_exp,公司在一定期间内用于研发活动的总支出。,研发支出合计,rd_exp,,,
35,研发投入强度（%）,rd_intensity,衡量公司研发投入力度。,= 研发支出合计 / 营业收入 × 100%,= rd_exp / oper_rev × 100%,0.0150,0.0700,"<1.5%,较差\n>7%,优秀"
36,研发人员数量占比（%）,rd_personnel_ratio,衡量公司研发人力资源投入。,= 研发人员数量 / 总员工数量× 100%,= stmnote_RDemployee / employee× 100%,,,
37,每万名研发人员主营业务收入(亿元),revenue_per_rd_personnel,衡量公司研发人员效率。,本期主营业务收入/[(上期末研发人员数量+本期末研发人员数量)/20000],oper_rev/[(stmnote_RDemployee_prev+stmnote_RDemployee_curr)/20000],,,
38,研发投入资本化率（%）,rd_capitalization_rate,衡量公司研发支出资本化程度。,=研发资本化支出 / 研发支出合计 × 100%,=rd_capitalization / rd_exp × 100%,,,
39,研发费用占三费比率（%）,rd_to_three_fee_ratio,衡量公司研发费用占比。,= 研发费用 / （销售费用 + 管理费用 + 研发费用） × 100%,= rd_exp / （selling_dist_exp + gerl_admin_exp + rd_exp） × 100%,,,
40,分红金额,wgsd_stmnote_aualaccmdiv,公司在一定期间内向股东分配的利润金额。,本期分红金额,wgsd_stmnote_aualaccmdiv,,,
41,现金分红率,cash_dividend_ratio,衡量公司分红力度。,本期现金派息/上期归属于母公司股东净利润,=wgsd_stmnote_aualaccmdiv / np_belongto_parcomsh[上期],,,,
42,关联业务收入占比,related_party_revenue_ratio,衡量公司关联交易规模。,=关联交易产生的收入/主营业务收入 × 100,=stmnote_associated_1 / oper_rev × 100,,,
43,关联业务成本占比,related_party_cost_ratio,衡量公司关联交易成本占比。,=关联交易产生的成本/主营业务成本 × 100,=stmnote_associated_2 / oper_cost × 100,,,
