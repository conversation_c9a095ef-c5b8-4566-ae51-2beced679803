import React from 'react';
import ReactDOM from 'react-dom';
import { Select, Table, Tag, Card, Tooltip } from 'antd';
import 'antd/dist/reset.css'; // Import Ant Design styles

const { Option } = Select;

class FinancialAnalysis extends React.PureComponent {
  state = {
    loading: true,
    metrics: [],
    companies: [],
    selectedMetrics: [],
    selectedCompanies: [],
    tableData: [],
    years: [2022, 2023, 2024], // 示例年份，可以动态生成或从API获取
    selectedYear: 2024, // 默认当前年份
    quarters: [
      { label: '一季度', value: 'Q1', month: '03', day: '31' },
      { label: '二季度', value: 'Q2', month: '06', day: '30' },
      { label: '三季度', value: 'Q3', month: '09', day: '30' },
      { label: '年度报告', value: 'ANNUAL', month: '12', day: '31' },
    ],
    selectedQuarter: 'ANNUAL', // 默认年度报告
    reportDate: `2024-12-31`, // 初始化 reportDate
    tableType: "quantitative" // "quantitative" or "custom"
  };

  // Function to display errors in the error-container
  displayError(errorMessage) {
    const errorContainer = document.getElementById('error-container');
    if (errorContainer) {
      errorContainer.textContent = '发生错误: ' + errorMessage;
      errorContainer.style.display = 'block';
    }
    console.error('发生错误:', errorMessage); // Keep console log for debugging
  }

  async componentDidMount() {
    try {
      const [definitionsRes, companiesRes, valuesRes] = await Promise.all([
        fetch(`${process.env.REACT_APP_API_BASE_URL}/metrics/definitions?table=L2Metrics&reportDate=${this.state.reportDate}`),
        fetch(`${process.env.REACT_APP_API_BASE_URL}/companies?table=L2Metrics&reportDate=${this.state.reportDate}`),
        fetch(`${process.env.REACT_APP_API_BASE_URL}/metrics/values?table=L2Metrics&reportDate=${this.state.reportDate}`)
      ]);

      if (!definitionsRes.ok || !companiesRes.ok || !valuesRes.ok) {
        throw new Error(`HTTP error! status: ${definitionsRes.status}, ${companiesRes.status}, ${valuesRes.status}`);
      }

      const [definitions, companies, values] = await Promise.all([
        definitionsRes.json(),
        companiesRes.json(),
        valuesRes.json()
      ]);

      this.setState({
        loading: false,
        metrics: definitions,
        companies: companies,
        selectedMetrics: definitions.map(m => m.code),
        selectedCompanies: companies.map(c => c.id),
        tableData: this.processTableData({ metrics_values: values, metrics_definitions: definitions, companies })
      });
    } catch (error) {
      this.displayError(error.message || '数据加载失败');
      this.setState({ loading: false });
    }
  }

  processTableData = (data) => {
    const companiesMap = new Map(); // 使用 Map 按 company_id 分组

    data.metrics_values.forEach(item => {
      // item: { metric_name, company_id, value }
      // data.companies: [{ id, name, ... }]

      if (!companiesMap.has(item.company_id)) {
        const companyInfo = data.companies.find(c => c.id === item.company_id);
        companiesMap.set(item.company_id, {
          company_id: item.company_id,
          company_name: companyInfo ? companyInfo.name : item.company_id, // 公司名称
          key: item.company_id,         // 表格行的唯一键
          metric_values: {}           // 存储该公司各指标的值 { metric_name: value }
        });
      }
      // 将当前指标的值和评估结果存入对应公司的 metric_values 对象中
      companiesMap.get(item.company_id).metric_values[item.metric_name] = { value: item.value, evaluation_result: item.EvaluationResult, translated_formula: item.TranslatedFormula };
    });

    // 将 Map 转换为数组，作为表格的 dataSource
    return Array.from(companiesMap.values());
  };

  generateReportDate = (year, quarterValue) => {
    const quarterInfo = this.state.quarters.find(q => q.value === quarterValue);
    if (year && quarterInfo) {
      return `${year}-${quarterInfo.month}-${quarterInfo.day}`;
    }
    // 默认返回当前年份的年报日期或一个合理的默认值
    const currentYear = new Date().getFullYear();
    return `${currentYear}-12-31`;
  };

  handleMetricChange = (selectedMetrics) => {
    this.setState({ selectedMetrics });
  };

  handleCompanyChange = (selectedCompanies) => {
    this.setState({ selectedCompanies });
  };

  // handleYearChange = (selectedYear) => {
  //   this.setState({ loading: true, selectedYear }, async () => {
  //     try {
  //       const response = await fetch(`http://localhost:5000/api/data?table=L2Metrics&year=${selectedYear}`);
  //       if (!response.ok) {
  //         throw new Error(`HTTP error! status: ${response.status}`);
  //       }
  //       const data = await response.json();
  //       this.setState({
  //         metrics: data.metrics_definitions,
  //         companies: data.companies,
  //         selectedMetrics: data.metrics_definitions.map(m => m.code),
  //         selectedCompanies: data.companies.map(c => c.id),
  //         tableData: this.processTableData(data),
  //         loading: false
  //       });
  //     } catch (error) {
  //       this.displayError(error.message || '数据加载失败');
  //       this.setState({ loading: false });
  //     }
  //   });
  // };

  handleDateChange = (changedValue, type) => {
    let { selectedYear, selectedQuarter } = this.state;

    if (type === 'year') {
      selectedYear = changedValue;
    } else if (type === 'quarter') {
      selectedQuarter = changedValue;
    }

    const reportDate = this.generateReportDate(selectedYear, selectedQuarter);

    this.setState({ loading: true, selectedYear, selectedQuarter, reportDate }, async () => {
      try {
        const [definitionsRes, companiesRes, valuesRes] = await Promise.all([
          fetch(`${process.env.REACT_APP_API_BASE_URL}/metrics/definitions?table=L2Metrics&reportDate=${reportDate}`),
          fetch(`${process.env.REACT_APP_API_BASE_URL}/companies?table=L2Metrics&reportDate=${reportDate}`),
          fetch(`${process.env.REACT_APP_API_BASE_URL}/metrics/values?table=L2Metrics&reportDate=${reportDate}`)
        ]);

        if (!definitionsRes.ok || !companiesRes.ok || !valuesRes.ok) {
          throw new Error(`HTTP error! status: ${definitionsRes.status}, ${companiesRes.status}, ${valuesRes.status}`);
        }

        const [definitions, companies, values] = await Promise.all([
          definitionsRes.json(),
          companiesRes.json(),
          valuesRes.json()
        ]);

        this.setState({
          metrics: definitions,
          companies: companies,
          tableData: this.processTableData({ metrics_values: values, metrics_definitions: definitions, companies }),
          loading: false
        });
      } catch (error) {
        this.displayError(error.message || '数据加载失败');
        this.setState({ loading: false });
      }
    });
  };

  handleTableTypeChange = (tableType) => {
    this.setState({ tableType });
    // 切换表格类型时，如果切换到自定义，保留当前指标选择，否则重置为全部
    if (tableType === "custom") {
      // 不做处理，保留当前 selectedMetrics
    } else {
      // 定量评价时，自动选中全部指标
      this.setState((state) => ({ selectedMetrics: state.metrics.map(m => m.code) }));
    }
  };
  // 添加格式化数值的独立方法
  formatMetricValue = (value, valueType) => {
    if (value === undefined) return '-';

    const numValue = Number(value);
    const trimmedType = valueType ? valueType.trim() : '';

    if (trimmedType === '%') {
      return (numValue * 100).toFixed(2) + '%';
    } else if (trimmedType === '亿') {
      return (numValue / 100000000).toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      });
    } else {
      return numValue.toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      });
    }
  };
  render() {
    const { loading, metrics, companies, selectedMetrics, selectedCompanies, tableData, years, selectedYear, tableType, quarters, selectedQuarter } = this.state;

    // 根据选择的公司筛选数据源。tableData 中的每个 item 现在是一个公司对象。
    const filteredData = tableData.filter(item =>
      selectedCompanies.includes(item.company_id) // item.company_id 是公司代码
    );

    const columns = [
      {
        title: '公司名称',
        dataIndex: 'company_name', // 公司名称，来自 dataSource 中的每个对象
        key: 'company_name',
        fixed: 'left',
        width: 300,
        // render: (companyName) => companyName // company_name 应该已经是可读的了
        sorter: (a, b) => (a.company_name || '').localeCompare(b.company_name || ''),
        sortDirections: ['ascend', 'descend'],
      },
      // 根据选择的指标动态生成列
      ...metrics // metrics is data.metrics_definitions [{code, name}, ...]
        .filter(metric => selectedMetrics.includes(metric.code))
        .map(metric => ({
          title: (
            <Tooltip
              title={
                <div style={{ maxWidth: 500 }}>
                  <div><strong>指标名称:</strong> {metric.name}</div>
                  <div><strong>公式说明:</strong> {metric.formula_desc || '无说明'}</div>
                  <div><strong>公式:</strong> {metric.formula || '无公式'}</div>
                  <div><strong>指标解释:</strong> {metric.description || '无描述'}</div>
                </div>
              }
              overlayStyle={{ maxWidth: 500 }}
            >
              <span style={{ cursor: 'help' }}>{metric.name}</span>
            </Tooltip>
          ),
          dataIndex: 'metric_values',
          key: metric.code,
          width: 150,
          align: 'right',
          sorter: (a, b) => {
            const va = a.metric_values[metric.code]?.value;
            const vb = b.metric_values[metric.code]?.value;
            if (va === undefined && vb === undefined) return 0;
            if (va === undefined) return 1;
            if (vb === undefined) return -1;
            return va - vb;
          },
          sortDirections: ['descend', 'ascend'],


          // 修改render方法
          render: (metricValues, record) => {
            const metricData = metricValues ? metricValues[metric.code] : undefined;
            const value = metricData ? metricData.value : undefined;
            const evaluationResult = metricData ? metricData.evaluation_result : undefined;
            const translated_formula = metricData ? metricData.translated_formula : undefined;
            let style = {};
            if (evaluationResult === '良好') {
              style = { backgroundColor: '#fff1f0', color: '#f5222d', padding: '8px', textAlign: 'right' }; // 更柔和的淡绿底，深绿字
            } else if (evaluationResult === '较差') {
              style = { backgroundColor: '#e8f5e9', color: '#2e7d32', padding: '8px', textAlign: 'right' }; // 淡红底，红字
            } else if (evaluationResult === '一般') {
              //   style = { backgroundColor: '#fffbe6', color: '#faad14', padding: '8px', textAlign: 'right' }; // 淡黄底，黄字
            } else {
              style = { padding: '8px', textAlign: 'right' }; // 默认样式
            }

            return (
              <Tooltip
                title={
                  <div style={{ maxWidth: 500 }}>
                    <div><strong>指标名称:</strong> {metric.name}</div>
                    <div><strong>值:</strong> {this.formatMetricValue(value, metric.value_type)}</div>
                    <div><strong>计算公式:</strong> {translated_formula || '无计算公式'}</div>
                    <div><strong>评估结果:</strong> {evaluationResult || '无评估'}</div>
                  </div>
                }
                overlayStyle={{ maxWidth: 500 }}
              >
                <div style={style}>
                  {this.formatMetricValue(value, metric.value_type)}
                </div>
              </Tooltip>
            );
          }
        }))
    ];

    return (
      <div className="container">
        <div className="card-flex">
          <Card title="财务指标对比" loading={loading} style={{ flex: 1, minHeight: 0, display: 'flex', flexDirection: 'column' }}>
            <div className="selectors">
              <Select
                value={selectedYear}
                onChange={(value) => this.handleDateChange(value, 'year')}
                style={{ width: 120, marginBottom: 16, marginRight: 16 }}
              >
                {years.map(year => (
                  <Option key={year} value={year}>{year}年</Option>
                ))}
              </Select>
              <Select
                value={selectedQuarter}
                onChange={(value) => this.handleDateChange(value, 'quarter')}
                style={{ width: 140, marginBottom: 16, marginRight: 16 }}
              >
                {quarters.map(q => (
                  <Option key={q.value} value={q.value}>{q.label}</Option>
                ))}
              </Select>
              <Select
                value={tableType}
                onChange={this.handleTableTypeChange}
                style={{ width: 140, marginBottom: 16, marginRight: 16 }}
              >
                <Option value="quantitative">定量评价</Option>
                <Option value="custom">自定义</Option>
              </Select>
              {tableType === "custom" && (
                <Select
                  mode="multiple"
                  placeholder="选择指标"
                  value={selectedMetrics}
                  onChange={this.handleMetricChange}
                  style={{ width: '100%', marginBottom: 16 }}
                  allowClear
                  showSearch
                  optionFilterProp="children"
                >
                  {metrics.map(metric => (
                    <Option key={metric.code} value={metric.code}>
                      {metric.name}
                    </Option>
                  ))}
                </Select>
              )}
              <Select
                mode="multiple"
                placeholder="选择公司"
                value={selectedCompanies}
                onChange={this.handleCompanyChange}
                style={{ width: '100%', marginBottom: 16 }}
                allowClear
                showSearch
                optionFilterProp="children"
              >
                {companies.map(company => (
                  <Option key={company.id} value={company.id}>
                    {company.name}
                  </Option>
                ))}
              </Select>
            </div>
            <Table
              columns={columns}
              dataSource={filteredData}
              bordered
              showSorterTooltip={false}
              size="middle"
              scroll={{ x: 'max-content', y: 'calc(100vh - 430px)' }}
              pagination={false}
              className="flex-table"
              style={{
                height: 'calc(100vh - 500px)',
                minHeight: '400px'
              }}
            />
          </Card>
        </div>
      </div>
    );
  }
}

const rootElement = document.getElementById('root');
if (rootElement) {
  const root = ReactDOM.createRoot(rootElement);
  root.render(<FinancialAnalysis />);
} else {
  console.error('Failed to find the root element');
}

// Add a global error handler to catch unhandled errors
window.onerror = function (message, source, lineno, colno, error) {
  const errorContainer = document.getElementById('error-container');
  if (errorContainer) {
    let displayMessage = `错误: ${message}`;
    if (source) {
      displayMessage += `\n来源: ${source}`;
    }
    if (lineno) {
      displayMessage += `\n行号: ${lineno}`;
    }
    if (colno) {
      displayMessage += ` 列号: ${colno}`;
    }
    errorContainer.textContent = displayMessage;
    errorContainer.style.display = 'block';
  }
  // Return true to prevent the browser's default error handling
  return true;
};

// Catch unhandled promise rejections
window.addEventListener('unhandledrejection', function (event) {
  const errorContainer = document.getElementById('error-container');
  if (errorContainer) {
    const reason = event.reason || '未处理的 Promise 拒绝';
    errorContainer.textContent = `未处理的 Promise 拒绝: ${reason.message || reason}`;
    errorContainer.style.display = 'block';
  }
});
