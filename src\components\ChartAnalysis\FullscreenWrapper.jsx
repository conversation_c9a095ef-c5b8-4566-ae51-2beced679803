import React, { useRef, useState, useEffect, useImperativeHandle, forwardRef } from 'react';

const FullscreenWrapper = forwardRef(({ children }, ref) => {
  const containerRef = useRef(null);
  const [isFullscreen, setIsFullscreen] = useState(false);

  // 进入全屏
  const enterFullscreen = () => {
    if (containerRef.current && !isFullscreen) {
      if (containerRef.current.requestFullscreen) {
        containerRef.current.requestFullscreen();
      } else if (containerRef.current.webkitRequestFullscreen) {
        containerRef.current.webkitRequestFullscreen();
      } else if (containerRef.current.mozRequestFullScreen) {
        containerRef.current.mozRequestFullScreen();
      } else if (containerRef.current.msRequestFullscreen) {
        containerRef.current.msRequestFullscreen();
      }
    }
  };

  // 退出全屏
  const exitFullscreen = () => {
    if (document.fullscreenElement) {
      document.exitFullscreen();
    } else if (document.webkitFullscreenElement) {
      document.webkitExitFullscreen();
    } else if (document.mozFullScreenElement) {
      document.mozCancelFullScreen();
    } else if (document.msFullscreenElement) {
      document.msExitFullscreen();
    }
  };

  // 切换全屏
  const toggleFullscreen = () => {
    if (isFullscreen) {
      exitFullscreen();
    } else {
      enterFullscreen();
    }
  };

  // 监听全屏变化
  useEffect(() => {
    const handleFullscreenChange = () => {
      const fs = document.fullscreenElement === containerRef.current ||
        document.webkitFullscreenElement === containerRef.current ||
        document.mozFullScreenElement === containerRef.current ||
        document.msFullscreenElement === containerRef.current;
      setIsFullscreen(fs);
    };
    document.addEventListener('fullscreenchange', handleFullscreenChange);
    document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
    document.addEventListener('mozfullscreenchange', handleFullscreenChange);
    document.addEventListener('MSFullscreenChange', handleFullscreenChange);
    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
      document.removeEventListener('webkitfullscreenchange', handleFullscreenChange);
      document.removeEventListener('mozfullscreenchange', handleFullscreenChange);
      document.removeEventListener('MSFullscreenChange', handleFullscreenChange);
    };
  }, []);

  // 暴露方法给父组件
  useImperativeHandle(ref, () => ({
    enterFullscreen,
    exitFullscreen,
    toggleFullscreen,
    isFullscreen,
  }));

  return (
    <div ref={containerRef} style={{
      background: isFullscreen ? '#f7f9fa' : undefined,
      minHeight: isFullscreen ? '100vh' : undefined,
      height: isFullscreen ? '100vh' : undefined,
      width: isFullscreen ? '100vw' : undefined,
      overflow: 'auto',
    }}>
      {children(isFullscreen)}
    </div>
  );
});

export default FullscreenWrapper; 