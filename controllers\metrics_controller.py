from flask import request, jsonify
from ..services.metrics_service import MetricsService

def create_metrics_controller(app):
    service = MetricsService()

    @app.route('/api/companies', methods=['GET'])
    def fetch_companies():
        companies = service.fetch_all_companies()
        return jsonify(companies)

    @app.route('/api/indicators', methods=['GET'])
    def fetch_indicators():
        indicators = service.fetch_all_indicators()
        return jsonify(indicators)
