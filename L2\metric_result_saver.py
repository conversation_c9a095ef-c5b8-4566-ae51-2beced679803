# L2/metric_result_saver.py
from database.db_handler import DatabaseHandler
from decimal import Decimal, InvalidOperation

class MetricResultSaver:
    """
    负责将指标计算结果、错误日志和计算日志保存到数据库。
    """
    def __init__(self):
        self.db = DatabaseHandler()

    def _evaluate_metric(self, calculated_value, lower_bound, upper_bound, lower_bound_label=None, upper_bound_label=None):
        """根据上下限评估指标结果"""
        if lower_bound is None or upper_bound is None or calculated_value is None:
            return '一般' # 无法评估时返回'一般'

        try:
            # 尝试将所有值转换为Decimal进行精确比较
            cv = Decimal(str(calculated_value))
            lb = Decimal(str(lower_bound))
            ub = Decimal(str(upper_bound))

            if cv < lb:
                return lower_bound_label if lower_bound_label else '较差'
            elif cv > ub:
                return upper_bound_label if upper_bound_label else '优秀'
            else:
                return '良好'
        except (InvalidOperation, ValueError, TypeError) as e:
            print(f"评估指标时出错 (值: {calculated_value}, 下限: {lower_bound}, 上限: {upper_bound}): {e}，返回 '一般'")
            return '一般'

    def save_metric_to_db(self, stock_code, company_name, report_date, indicator_name_cn,
                          indicator_name_en, formula_desc, formula_en, calculated_value,
                          lower_bound=None, upper_bound=None, range_value=None, benchmark_info=None,translated_formula=None):
        """
        保存指标计算结果到数据库 L2Metrics 表。

        参数:
            ... (参数同原方法)
            benchmark_info (dict): 从L2BenchmarkIndicators表获取的指标定义信息
        """
        try:
            # 从 benchmark_info 更新信息
            indicator_desc = None
            formula = formula_desc # 默认使用传入的 formula_desc
            lower_bound_label = None
            upper_bound_label = None
            if benchmark_info:
                indicator_name_en = benchmark_info.get('indicator_name_en') or indicator_name_en
                indicator_name_cn = benchmark_info.get('indicator_name_cn') or indicator_name_cn
                indicator_desc = benchmark_info.get('indicator_desc')
                formula = benchmark_info.get('formula') or formula # 优先使用 benchmark 的 formula
                formula_desc = benchmark_info.get('formula_desc') or formula_desc # 优先使用 benchmark 的 formula_desc
                lower_bound = benchmark_info.get('lower_bound') if benchmark_info.get('lower_bound') is not None else lower_bound
                upper_bound = benchmark_info.get('upper_bound') if benchmark_info.get('upper_bound') is not None else upper_bound
                range_value = benchmark_info.get('range_value') or range_value
                lower_bound_label = benchmark_info.get('lower_bound_label')
                upper_bound_label = benchmark_info.get('upper_bound_label')

            # 确定评估结果
            evaluation_result = self._evaluate_metric(calculated_value, lower_bound, upper_bound, lower_bound_label, upper_bound_label)

            with self.db as db:
                cursor = db.connection.cursor()

                # 先删除历史数据
                try:
                    cursor.execute("""
                    DELETE FROM L2Metrics
                    WHERE TickerSymbol = ? AND ReportDate = ? AND IndicatorNameEN = ?
                    """, (stock_code, report_date, indicator_name_en))
                    # print(f"已删除 {cursor.rowcount} 条指标 {indicator_name_cn} ({indicator_name_en}) 的历史数据")
                except Exception as e:
                    print(f"删除指标 {indicator_name_cn} ({indicator_name_en}) 的历史数据时出错: {str(e)}")
                    # 不中断执行，继续尝试插入

                # 插入新记录
                cursor.execute("""
                INSERT INTO L2Metrics (
                    TickerSymbol, CompanyName, ReportDate, IndicatorNameCN, IndicatorNameEN,
                    IndicatorDesc, FormulaDesc, Formula, FormulaEN, CalculatedValue,
                    LowerBound, UpperBound, RangeValue, EvaluationResult,TranslatedFormula
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?,?)
                """, (
                    stock_code, company_name, report_date, indicator_name_cn, indicator_name_en,
                    indicator_desc, formula_desc, formula, formula_en, calculated_value,
                    lower_bound, upper_bound, range_value, evaluation_result,translated_formula
                ))
                # print(f"已插入指标 {indicator_name_cn} ({indicator_name_en}) 的计算结果")

                # 同时删除相关的错误日志
                try:
                    cursor.execute("""
                    DELETE FROM L2MetricsErrorLog
                    WHERE TickerSymbol = ? AND ReportDate = ? AND IndicatorNameEN = ?
                    """, (stock_code, report_date, indicator_name_en))
                    # if cursor.rowcount > 0:
                    #     print(f"已删除 {cursor.rowcount} 条指标 {indicator_name_cn} ({indicator_name_en}) 的错误日志")
                except Exception as e:
                    # L2MetricsErrorLog 表可能不存在或删除出错，打印日志但不中断
                    print(f"删除指标 {indicator_name_cn} ({indicator_name_en}) 的错误日志时出错: {str(e)}")
                    pass

                db.connection.commit()
        except Exception as e:
            print(f"保存指标 {indicator_name_cn} ({indicator_name_en}) 结果到数据库时出错: {str(e)}")

    def log_calculation_error(self, stock_code, report_date, indicator_name_cn,
                              indicator_name_en, formula_desc, formula_en,
                              error_message, processed_formula=None, benchmark_info=None):
        """
        记录计算错误到数据库 L2MetricsErrorLog 表。

        参数:
            ... (参数同原方法)
            benchmark_info (dict): 从L2BenchmarkIndicators表获取的指标定义信息
        """
        try:
            # 从 benchmark_info 更新信息
            indicator_desc = None
            formula = formula_desc # 默认使用传入的 formula_desc
            if benchmark_info:
                indicator_name_en = benchmark_info.get('indicator_name_en') or indicator_name_en
                indicator_name_cn = benchmark_info.get('indicator_name_cn') or indicator_name_cn
                indicator_desc = benchmark_info.get('indicator_desc')
                formula = benchmark_info.get('formula') or formula # 优先使用 benchmark 的 formula
                formula_desc = benchmark_info.get('formula_desc') or formula_desc # 优先使用 benchmark 的 formula_desc

            with self.db as db:
                cursor = db.connection.cursor()

                # 尝试插入到L2MetricsErrorLog表
                try:
                    # 先删除历史错误日志
                    cursor.execute("""
                    DELETE FROM L2MetricsErrorLog
                    WHERE TickerSymbol = ? AND ReportDate = ? AND IndicatorNameEN = ?
                    """, (stock_code, report_date, indicator_name_en))
                    # if cursor.rowcount > 0:
                    #     print(f"已删除 {cursor.rowcount} 条指标 {indicator_name_cn} ({indicator_name_en}) 的历史错误日志")

                    # 插入新的错误日志
                    cursor.execute("""
                    INSERT INTO L2MetricsErrorLog (
                        TickerSymbol, ReportDate, IndicatorNameCN, IndicatorNameEN,
                        IndicatorDesc, FormulaDesc, Formula, FormulaEN, ErrorMessage, ProcessedFormula
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        stock_code, report_date, indicator_name_cn, indicator_name_en,
                        indicator_desc, formula_desc, formula, formula_en,
                        error_message, processed_formula
                    ))
                    # print(f"已记录指标 {indicator_name_cn} ({indicator_name_en}) 的计算错误")

                    db.connection.commit()

                except Exception as e:
                    # 如果 L2MetricsErrorLog 表操作失败，尝试记录到 L2Metrics 的 Remarks 字段
                    print(f"记录到 L2MetricsErrorLog 失败: {str(e)}。尝试记录到 L2Metrics 表的 Remarks 字段。")
                    try:
                        # 先删除 L2Metrics 表中的历史数据
                        cursor.execute("""
                        DELETE FROM L2Metrics
                        WHERE TickerSymbol = ? AND ReportDate = ? AND IndicatorNameEN = ?
                        """, (stock_code, report_date, indicator_name_en))

                        # 插入错误信息到 L2Metrics
                        cursor.execute("""
                        INSERT INTO L2Metrics (
                            TickerSymbol, CompanyName, ReportDate, IndicatorNameCN, IndicatorNameEN,
                            IndicatorDesc, FormulaDesc, Formula, FormulaEN, CalculatedValue,
                            LowerBound, UpperBound, RangeValue, EvaluationResult, Remarks
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                        """, (
                            stock_code, "未知", report_date, indicator_name_cn, indicator_name_en,
                            indicator_desc, formula_desc, formula, formula_en, None, # CalculatedValue 为空
                            benchmark_info.get('lower_bound') if benchmark_info else None,
                            benchmark_info.get('upper_bound') if benchmark_info else None,
                            benchmark_info.get('range_value') if benchmark_info else None,
                            '错误', # EvaluationResult 设为 '错误'
                            f"计算错误: {error_message}" # 错误信息记录在 Remarks
                        ))
                        print(f"已将指标 {indicator_name_cn} ({indicator_name_en}) 的计算错误记录到 L2Metrics 表的 Remarks 字段")
                        db.connection.commit()
                    except Exception as inner_e:
                        print(f"记录错误到 L2Metrics 表也失败: {str(inner_e)}")
                        # 此时无法保存错误信息到数据库，仅打印
                        print(f"!!! 无法记录错误日志到数据库: Ticker={stock_code}, Date={report_date}, Indicator={indicator_name_cn}, Error={error_message}")


        except Exception as e:
            print(f"记录计算错误 {indicator_name_cn} ({indicator_name_en}) 到数据库时发生意外错误: {str(e)}")

    def log_calculation(self, stock_code, report_date, success_count,
                        failure_count, execution_time, log_message=None):
        """
        记录计算日志到数据库 L2MetricsCalculationLog 表。

        参数:
            ... (参数同原方法)
        """
        try:
            with self.db as db:
                cursor = db.connection.cursor()

                # 尝试直接操作 L2MetricsCalculationLog 表
                try:
                    # 先删除历史计算日志
                    cursor.execute("""
                    DELETE FROM L2MetricsCalculationLog
                    WHERE TickerSymbol = ? AND ReportDate = ?
                    """, (stock_code, report_date))
                    # if cursor.rowcount > 0:
                    #     print(f"已删除 {cursor.rowcount} 条股票 {stock_code} 在 {report_date} 的历史计算日志")

                    # 插入新的计算日志
                    cursor.execute("""
                    INSERT INTO L2MetricsCalculationLog (
                        TickerSymbol, ReportDate, SuccessCount, FailureCount, ExecutionTime, LogMessage
                    ) VALUES (?, ?, ?, ?, ?, ?)
                    """, (
                        stock_code, report_date, success_count, failure_count,
                        execution_time, log_message
                    ))
                    # print(f"已记录计算日志: {stock_code} @ {report_date}")
                    db.connection.commit()

                except Exception as e:
                    # 如果表不存在或操作失败，仅打印日志
                    print(f"记录计算日志到 L2MetricsCalculationLog 表失败: {str(e)}")
                    log_entry = (f"计算日志: 股票代码={stock_code}, 报告日期={report_date}, "
                                 f"成功={success_count}, 失败={failure_count}, "
                                 f"执行时间={execution_time:.3f}秒")
                    if log_message:
                        log_entry += f", 信息: {log_message}"
                    print(log_entry)

        except Exception as e:
            print(f"记录计算日志到数据库时发生意外错误: {str(e)}")
