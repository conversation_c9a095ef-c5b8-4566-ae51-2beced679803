#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Wind数据导入工具 - 简化打包脚本
"""

import os
import sys
import subprocess
import shutil
from datetime import datetime

def check_dependencies():
    """检查依赖"""
    print("🔍 检查依赖...")
    
    required_packages = [
        'pyinstaller',
        'pandas', 
        'numpy',
        'pyodbc'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'pyinstaller':
                import PyInstaller
                print(f"✅ {package}: {PyInstaller.__version__}")
            elif package == 'pandas':
                import pandas
                print(f"✅ {package}: {pandas.__version__}")
            elif package == 'numpy':
                import numpy
                print(f"✅ {package}: {numpy.__version__}")
            elif package == 'pyodbc':
                import pyodbc
                print(f"✅ {package}: 已安装")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package}: 未安装")
    
    if missing_packages:
        print(f"\n⚠️  缺少依赖包: {', '.join(missing_packages)}")
        print("请先安装缺少的依赖包:")
        for package in missing_packages:
            print(f"  pip install {package}")
        return False
    
    print("✅ 所有依赖检查完成")
    return True

def clean_build_dirs():
    """清理构建目录"""
    dirs_to_clean = ['build', 'dist', '__pycache__']
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            print(f"🧹 清理目录: {dir_name}")
            shutil.rmtree(dir_name)
    
    # 清理spec文件
    spec_files = [f for f in os.listdir('.') if f.endswith('.spec')]
    for spec_file in spec_files:
        print(f"🧹 删除spec文件: {spec_file}")
        os.remove(spec_file)

def build_exe():
    """构建exe文件"""
    print("🔨 开始构建exe文件...")
    
    # 清理旧文件
    clean_build_dirs()
    
    # 构建命令
    cmd = [
        'pyinstaller',
        '--onefile',  # 打包成单个exe文件
        '--console',  # 控制台应用
        '--name=wind_import',  # 指定exe名称
        '--add-data=database;database',  # 添加数据库目录
        '--add-data=logging_config.py;.',  # 添加日志配置
        '--add-data=import_config.py;.',  # 添加配置文件
        '--add-data=import_config_examples.py;.',  # 添加配置示例
        '--hidden-import=pandas',
        '--hidden-import=numpy', 
        '--hidden-import=pyodbc',
        '--hidden-import=WindPy',
        '--hidden-import=decimal',
        '--hidden-import=concurrent.futures',
        '--hidden-import=threading',
        '--hidden-import=queue',
        '--hidden-import=datetime',
        '--hidden-import=uuid',
        '--hidden-import=logging',
        '--hidden-import=argparse',
        '--hidden-import=importlib.util',
        'import_wind_data.py'
    ]
    
    print("🚀 执行PyInstaller构建...")
    print(f"命令: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        print("✅ 构建成功!")
        
        # 检查生成的文件
        exe_path = os.path.join('dist', 'wind_import.exe')
        if os.path.exists(exe_path):
            file_size = os.path.getsize(exe_path) / (1024 * 1024)  # MB
            print(f"📦 生成文件: {exe_path}")
            print(f"📏 文件大小: {file_size:.2f} MB")
            
            # 创建使用说明
            create_usage_guide()
            
            return True
        else:
            print("❌ exe文件未生成")
            return False
            
    except subprocess.CalledProcessError as e:
        print("❌ 构建失败!")
        print("错误输出:")
        print(e.stderr)
        return False
    except Exception as e:
        print(f"❌ 构建过程中出现错误: {e}")
        return False

def create_usage_guide():
    """创建使用说明文件"""
    guide_content = f'''# Wind数据导入工具 - 使用说明

## 文件说明
- `wind_import.exe`: 主程序文件
- `import_config.py`: 配置文件（可选）
- `import_config_examples.py`: 配置示例文件

## 智能默认值逻辑
程序会根据当前时间自动选择要导入的数据：
- 当前2季度 → 查询1季度数据
- 当前1季度 → 查询上一年4季度数据  
- 当前3季度 → 查询2季度数据
- 当前4季度 → 查询3季度数据

## 使用方法

### 1. 智能默认模式（推荐）
直接双击运行 `wind_import.exe`，程序会自动选择合适的数据期

### 2. 命令行模式
在命令行中运行：

```bash
# 使用智能默认值
wind_import.exe

# 指定年份和季度
wind_import.exe --start-year 2024 --end-year 2024 --quarters 0331

# 导入多个季度
wind_import.exe --start-year 2024 --end-year 2025 --quarters 0331 0630 0930 1231

# 只导入年报数据
wind_import.exe --start-year 2024 --end-year 2024 --quarters 1231

# 使用配置文件
wind_import.exe --config-file my_config.py

# 查看帮助
wind_import.exe --help
```

### 3. 配置文件模式
创建 `my_config.py` 文件：
```python
START_YEAR = 2024
END_YEAR = 2024
QUARTERS = ['0331', '0630', '0930', '1231']
```

然后运行：
```bash
wind_import.exe --config-file my_config.py
```

## 注意事项
1. 确保Wind终端已登录
2. 确保数据库连接正常
3. 程序会自动创建日志文件
4. 支持断点续传，重复运行不会重复导入

## 故障排除
1. 如果程序无法启动，请检查是否安装了必要的依赖
2. 如果数据导入失败，请查看日志文件
3. 如果Wind连接失败，请检查Wind终端状态

生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
'''
    
    with open('dist/使用说明.txt', 'w', encoding='utf-8') as f:
        f.write(guide_content)
    
    print("📖 创建使用说明: dist/使用说明.txt")

def main():
    """主函数"""
    print("=" * 60)
    print("Wind数据导入工具 - 简化打包")
    print("=" * 60)
    
    # 检查当前目录
    if not os.path.exists('import_wind_data.py'):
        print("❌ 错误: 请在包含 import_wind_data.py 的目录中运行此脚本")
        return
    
    # 检查依赖
    if not check_dependencies():
        return
    
    # 构建exe
    if build_exe():
        print("\n🎉 打包完成!")
        print("📁 输出目录: dist/")
        print("📄 主程序: dist/wind_import.exe")
        print("📖 使用说明: dist/使用说明.txt")
        print("\n💡 提示: 可以直接运行 wind_import.exe 使用智能默认值")
    else:
        print("\n❌ 打包失败，请检查错误信息")

if __name__ == "__main__":
    main() 