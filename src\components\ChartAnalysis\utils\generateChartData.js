// 通用数据处理函数
export function generateChartData(keys, typeName, processedValues, comps, selectedCompanyIds) {
  return comps.filter(c => selectedCompanyIds.includes(c.ticker_symbol)).map(c => {
    const v = processedValues.find(val => val.TickerSymbol === c.ticker_symbol && keys.some(k => k.key === val.IndicatorNameEN));
    return {
      company: c.name || c.ticker_symbol,
      value: v ? v.value : null,
      displayValue: v ? v.displayValue : null,
      type: typeName,
    };
  });
} 