import os
import re
import pandas as pd
import pdfplumber
from functools import lru_cache
import glob
from datetime import datetime

# 定义基础目录路径
base_dir = r'.\SINA'
output_file = 'related_transactions_results.txt'

# 缓存正则表达式编译结果
RELATED_TRANSACTION_PATTERN = re.compile(r'[一二三四五六七八九十]、重大关联交易')
# 扩展匹配模式，包含关联交易类别、关联交易类型等变体
CATEGORY_PATTERN = re.compile(r'关联\s*交易\s*类[别型]')

@lru_cache(maxsize=128)
def _contains_category_column(header_tuple):
    """缓存表头检查结果，先清理换行符再匹配"""
    for col in header_tuple:
        if col:
            # 清理换行符和空白字符后再匹配
            cleaned_col = re.sub(r'\s+', '', str(col))
            if CATEGORY_PATTERN.search(cleaned_col):
                return True
    return False

# 提取包含关联交易类别的表格
def extract_related_transactions_with_category(pdf_path):
    with pdfplumber.open(pdf_path) as pdf:
        # 初始化变量
        found_section = False
        all_tables = []
        start_page = None
        
        # 第一步：快速定位重大关联交易章节
        for page_num, page in enumerate(pdf.pages):
            # 只提取部分文本进行快速检查
            text = page.extract_text(layout=False)  # 更快的文本提取
            if text and RELATED_TRANSACTION_PATTERN.search(text):
                found_section = True
                start_page = page_num
                print(f'找到重大关联交易章节，页码：{page.page_number}')
                break
        
        # 第二步：从找到的页面开始提取表格，限制搜索范围
        if found_section:
            # 只处理找到章节后的有限页面（通常关联交易表格在章节开始后几页内）
            end_page = min(start_page + 10, len(pdf.pages))  # 限制搜索范围，但不超过PDF总页数
            
            for page_num in range(start_page, end_page):
                page = pdf.pages[page_num]
                tables = page.extract_tables()
                
                if tables:
                    for table in tables:
                        # 快速检查表格有效性
                        if not table or len(table) < 2:
                            continue
                            
                        header = table[0]
                        if not header:
                            continue
                            
                        # 使用缓存的表头检查，先清理表头换行符
                        cleaned_header = []
                        for col in header:
                            if col:
                                # 清理换行符但保留基本结构用于显示
                                cleaned_col = str(col).replace('\n', '').replace('\r', '').strip()
                                cleaned_header.append(cleaned_col)
                            else:
                                cleaned_header.append('')
                        
                        header_tuple = tuple(cleaned_header)
                        if _contains_category_column(header_tuple):
                            all_tables.append(table)
                            print(f'提取到包含关联交易类别的表格，页码：{page.page_number}')
                            # 找到一个表格后继续查找，可能有多个相关表格
                            break
                
                # 如果已经找到表格且当前页没有更多表格，可以考虑提前结束
                # 这里保持原逻辑继续搜索以确保完整性
                
           
                  
        
        # 处理提取的表格数据
        if all_tables:
            # 为每个表格创建一个DataFrame并转换为列表
            processed_data = []
            for i, table in enumerate(all_tables):
                headers = table[0]
                data_rows = table[1:]
                
                # 创建DataFrame，先清理表头换行符
                try:
                    # 清理表头中的换行符
                    cleaned_headers = []
                    for header in headers:
                        if header:
                            cleaned_header = str(header).replace('\n', '').replace('\r', '').strip()
                            cleaned_headers.append(cleaned_header)
                        else:
                            cleaned_headers.append('')
                    
                    # 处理重复列名问题
                    unique_headers = []
                    header_counts = {}
                    for header in cleaned_headers:
                        if header in header_counts:
                            header_counts[header] += 1
                            unique_headers.append(f"{header}_{header_counts[header]}")
                        else:
                            header_counts[header] = 0
                            unique_headers.append(header)
                    
                    df = pd.DataFrame(data_rows, columns=unique_headers)
                    
                    # 处理合并单元格导致的None值
                    # 找到关联交易类别列的索引，使用相同的匹配逻辑
                    category_col = None
                    for col in df.columns:
                        if col:
                            # 清理换行符和空白字符后再匹配
                            cleaned_col = re.sub(r'\s+', '', str(col))
                            if CATEGORY_PATTERN.search(cleaned_col):
                                category_col = col
                                break
                    
                    if category_col is not None:
                        # 向前填充None值（合并单元格的处理）- 使用新的方法
                        df[category_col] = df[category_col].ffill()
                        
                        # 批量清理所有列中的换行符（更高效的方式）
                        df = df.astype(str).replace('\n', '', regex=True)
                        
                        # 转换为列表格式
                        data_list = df.to_dict('records')
                        processed_data.extend(data_list)
                    
                    print(f'成功处理表格 {i+1}，转换为 {len(data_list)} 条记录')
                except Exception as e:
                    print(f'处理表格 {i+1} 时出错: {str(e)}')
                    # 打印表格内容以便调试
                    print(f'表头: {headers}')
                    print(f'行数: {len(data_rows)}')
                    if data_rows:
                        print(f'第一行列数: {len(data_rows[0])}')
            
            if processed_data:
                return processed_data
        
        print('未找到包含关联交易类别的表格')
        return None

def format_table_data(data_list, stock_code):
    """格式化表格数据为文本格式"""
    if not data_list:
        return f"股票代码: {stock_code} - 未找到关联交易数据\n\n"
    
    result = f"股票代码: {stock_code}\n"
    result += "=" * 50 + "\n"
    
    # 获取所有列名
    if data_list:
        columns = list(data_list[0].keys())
        
        # 创建表格头
        header = "|"
        for col in columns:
            header += f" {col[:15]:15} |"
        result += header + "\n"
        
        # 创建分隔线
        separator = "|"
        for col in columns:
            separator += "-" * 17 + "|"
        result += separator + "\n"
        
        # 添加数据行
        for record in data_list:
            row = "|"
            for col in columns:
                value = str(record.get(col, '')).replace('\n', ' ')[:15]
                row += f" {value:15} |"
            result += row + "\n"
    
    result += "\n" + "=" * 50 + "\n\n"
    return result

def process_all_stocks():
    """处理所有股票代码目录下的2024年年度报告"""
    results = []
    processed_count = 0
    error_count = 0
    
    # 获取所有股票代码目录
    stock_dirs = [d for d in os.listdir(base_dir) if os.path.isdir(os.path.join(base_dir, d)) and d.isdigit()]
    stock_dirs.sort()
    
    print(f"找到 {len(stock_dirs)} 个股票代码目录")
    
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(f"关联交易数据提取结果\n")
        f.write(f"提取时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"总计处理股票数量: {len(stock_dirs)}\n")
        f.write("=" * 80 + "\n\n")
        
        for stock_code in stock_dirs:
            stock_dir = os.path.join(base_dir, stock_code)
            print(f"\n处理股票代码: {stock_code}")
            
            # 查找2024年年度报告PDF文件
            pdf_pattern = os.path.join(stock_dir, f"{stock_code}_*2024年年度报告*.pdf")
            pdf_files = glob.glob(pdf_pattern)
            
            if not pdf_files:
                print(f"  未找到2024年年度报告PDF文件")
                result_text = f"股票代码: {stock_code} - 未找到2024年年度报告PDF文件\n\n"
                f.write(result_text)
                error_count += 1
                continue
            
            # 处理找到的PDF文件（通常只有一个）
            pdf_file = pdf_files[0]
            print(f"  找到PDF文件: {os.path.basename(pdf_file)}")
            
            try:
                # 提取关联交易数据
                data_list = extract_related_transactions_with_category(pdf_file)
                
                if data_list:
                    print(f"  成功提取到 {len(data_list)} 条关联交易记录")
                    result_text = format_table_data(data_list, stock_code)
                    processed_count += 1
                else:
                    print(f"  未找到关联交易数据")
                    # 如果找不到关联交易数据，尝试提取前3个表格作为参考
                    try:
                        with pdfplumber.open(pdf_file) as pdf:
                            found_tables = []
                            table_count = 0
                            
                            for page in pdf.pages[:20]:  # 只检查前20页
                                tables = page.extract_tables()
                                if tables:
                                    for table in tables:
                                        if table and len(table) > 1:
                                            found_tables.append(table)
                                            table_count += 1
                                            if table_count >= 3:
                                                break
                                    if table_count >= 3:
                                        break
                            
                            if found_tables:
                                result_text = f"股票代码: {stock_code} - 未找到关联交易数据，以下是前3个表格:\n"
                                result_text += "=" * 50 + "\n"
                                for i, table in enumerate(found_tables[:3], 1):
                                    result_text += f"\n表格 {i}:\n"
                                    for row in table[:5]:  # 只显示前5行
                                        result_text += "| " + " | ".join([str(cell)[:10] if cell else "" for cell in row]) + " |\n"
                                    result_text += "\n"
                                result_text += "=" * 50 + "\n\n"
                            else:
                                result_text = f"股票代码: {stock_code} - 未找到任何表格数据\n\n"
                    except Exception as e:
                        result_text = f"股票代码: {stock_code} - 处理PDF时出错: {str(e)}\n\n"
                    
                    error_count += 1
                
                f.write(result_text)
                
            except Exception as e:
                print(f"  处理PDF文件时出错: {str(e)}")
                result_text = f"股票代码: {stock_code} - 处理PDF文件时出错: {str(e)}\n\n"
                f.write(result_text)
                error_count += 1
        
        # 写入统计信息
        f.write("\n" + "=" * 80 + "\n")
        f.write(f"处理完成统计:\n")
        f.write(f"总计股票数量: {len(stock_dirs)}\n")
        f.write(f"成功提取数据: {processed_count}\n")
        f.write(f"未找到数据或出错: {error_count}\n")
        f.write(f"完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
    
    print(f"\n处理完成！")
    print(f"总计处理: {len(stock_dirs)} 个股票")
    print(f"成功提取: {processed_count} 个")
    print(f"未找到数据或出错: {error_count} 个")
    print(f"结果已保存到: {output_file}")

# 执行批量提取
if __name__ == "__main__":
    process_all_stocks()