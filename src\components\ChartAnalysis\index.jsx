import React from 'react';
import FilterPanel from './FilterPanel';
import ChartsDisplay from './ChartsDisplay';
import { useState } from 'react';
import chartIndicators from './chartIndicators';
const ChartAnalysis = () => {
  const [years, setYears] = useState([]);
  const [quarters, setQuarters] = useState([]);
  const [companies, setCompanies] = useState([]);

  const handleYearChange = (event) => {
    setYears(event.target.value);
  };

  const handleQuarterChange = (event) => {
    setQuarters(event.target.value);
  };

  const handleCompanyChange = (event) => {
    setCompanies(event.target.value);
  };

  return (
    <div>
    
      <ChartsDisplay 
        years={years} 
        quarters={quarters} 
        companies={companies} 
        chartIndicators={chartIndicators}
      />
    </div>
  );
};

export default ChartAnalysis;
