import React, { useMemo, useCallback } from "react";
import { Table, Card } from "antd";
import MetricTooltip, { MetricHeaderTooltip } from "./MetricTooltip";
import { formatMetricValue } from "../../utils/formatters";
import "./QuantitativeTable.css";

/**
 * 上市公司定量评估表组件
 * 专门用于展示定量评估表格式的财务数据
 * 按照标准的定量评估表格式进行分组展示
 */
const QuantitativeTable = ({
  tableData,
  metrics,
  selectedMetrics,
  selectedCompanies,
  selectedMetricsObjects,
  dataUnit,
  loading,
}) => {
  // 直接使用父组件传递的预处理数据，保留排名行
  const filteredData = useMemo(() => {
    return tableData; // 不过滤排名行，保持完整数据
  }, [tableData]);

  // 如果父组件没有传递selectedMetricsObjects，则进行兜底处理
  const metricsObjects = selectedMetricsObjects || useMemo(() => {
    return selectedMetrics
      .map((metricCode) => metrics.find((m) => m.code === metricCode))
      .filter((metric) => metric);
  }, [selectedMetrics, metrics]);

  // 定量评估表的指标分组结构
  const metricGroups = useMemo(() => [
    {
      category: "财务质量",
      categoryKey: "financial_quality",
      metrics: [
        { name: "净资产收益率", key: "roe", unit: "(%)" },
        { name: "资本回报率", key: "roic", unit: "(%)" },
        { name: "总资产周转率", key: "total_asset_turnover", unit: "(%)" },
        { name: "净利润现金比率", key: "net_profit_cash_ratio", unit: "(%)" },
        { name: "资产负债率", key: "debt_to_assets_ratio", unit: "(%)" },
      //  { name: "利息保障倍数", key: "interest_coverage_ratio", unit: "" },
        { name: "营业收入增长率", key: "oper_rev_growth_rate", unit: "(%)" }
      ],
    },
    {
      category: "创新能力",
      categoryKey: "innovation_capability",
      metrics: [
        { name: "研发投入强度", key: "rd_intensity", unit: "(%)" },
        { name: "研发人员占比", key: "rd_personnel_ratio", unit: "(%)" },
        { name: "每万名研发人员主营业务收入", key: "revenue_per_rd_personnel", unit: "（亿元）" },
      ]
    },
    // {
    //   category: "公司治理",
    //   categoryKey: "corporate_governance",
    //   metrics: [
    //     { name: "关联业务收入占比", key: "related_party_revenue_ratio", unit: "(%)" },
    //     { name: "关联业务成本占比", key: "related_party_cost_ratio", unit: "(%)" }
    //   ],
    // },
    {
      category: "利益相关者关系管理",
      categoryKey: "stakeholder_relationship",
      metrics: [
        { name: "库存周转率", key: "inventory_turnover", unit: "" },
        { name: "应收账款周转率", key: "receivables_turnover", unit: "" }
      ],
    },
    {
      category: "投资者回报",
      categoryKey: "investor_returns",
      metrics: [
        { name: "分红金额", key: "div_aualcashdividend", unit: "(亿元)" },
        { name: "现金分红率", key: "cash_dividend_ratio", unit: "" }
      ],
    },
  ], []);

  // 使用 useMemo 优化指标映射
  const metricKeyToObjectMap = useMemo(() => {
    const map = new Map();
    metricsObjects.forEach((metric) => {
      if (metric.code) {
        map.set(metric.code, metric);
      }
      if (metric.name) {
        map.set(metric.name, metric);
      }
      if (metric.metric_name) {
        map.set(metric.metric_name, metric);
      }
    });
    
    // 为定量评估表的指标key建立映射关系
    metricGroups.forEach(group => {
      group.metrics.forEach(metricDef => {
        // 尝试通过各种可能的字段匹配找到对应的指标对象
        const matchedMetric = metricsObjects.find(metric => 
          metric.code === metricDef.key || 
          metric.name === metricDef.name ||
          metric.metric_name === metricDef.name
        );
        if (matchedMetric) {
          map.set(metricDef.key, matchedMetric);
        }
      });
    });
    
    return map;
  }, [metricsObjects, metricGroups]);

  // 使用 useMemo 优化公司数据映射
  const companyDataMap = useMemo(() => {
    const map = new Map();
    filteredData.forEach((company) => {
      const companyMetrics = new Map();
      Object.entries(company.metric_values || {}).forEach(([key, value]) => {
        companyMetrics.set(key, value);
      });
      map.set(company.company_id, { company, metrics: companyMetrics });
    });
    return map;
  }, [filteredData]);

  // 使用useMemo优化分组表格数据源构建
  const dataSource = useMemo(() => {
    const result = [];
    let key = 0;

    metricGroups.forEach((group) => {
      const groupSize = group.metrics.length;
      // 直接添加该分组下的指标，包含分类信息和rowSpan信息
      group.metrics.forEach((metric, index) => {
        result.push({
          key: `metric-${key++}`,
          metricCategory: group.category,
          metricName: metric.name,
          metricUnit: metric.unit,
          metricKey: metric.key,
          categoryKey: group.categoryKey,
          // 只有第一行显示分类，其他行rowSpan为0
          categoryRowSpan: index === 0 ? groupSize : 0,
        });
      });
    });

    return result;
  }, [metricGroups]);

  // 渲染单元格的回调函数
  const renderCell = useCallback(
    (companyId, record) => {
      // 如果是排名行，显示电科数字公司的排名
      if (record.isRankRow) {
        const metric = metricKeyToObjectMap.get(record.metricKey);
        if (!metric) return "-";
        
        // 从排名行的 metric_ranks 中获取电科数字公司的排名（这是基于选中公司的相对排名）
        const diankeCompany = data.find(item => item.company_name === "电科数字" && !item.isRankRow);
        let diankeRank = "-";
        
        if (diankeCompany && record.metric_ranks?.[diankeCompany.company_id]?.[metric.code]) {
          diankeRank = record.metric_ranks[diankeCompany.company_id][metric.code];
        }
        
        return (
          <div className="metric-value-cell rank-cell" style={{ 
            fontWeight: 'bold', 
            color: '#1890ff',
            textAlign: 'center'
          }}>
            {diankeRank}
          </div>
        );
      }

      const companyData = companyDataMap.get(companyId);
      if (!companyData) return "-";

      const { metrics: companyMetrics } = companyData;

      // 数据存储时使用的是metric_name，需要通过指标对象的name或code来匹配
      // 首先尝试通过metricKey在指标对象中查找对应的指标
      const metric = metricKeyToObjectMap.get(record.metricKey);
      
      // 如果找到指标对象，则使用其name或code来查找数据
      let metricData = null;
      if (metric) {
        metricData = companyMetrics.get(metric.name) || 
                    companyMetrics.get(metric.code) ||
                    companyMetrics.get(record.metricKey) ||
                    companyMetrics.get(record.metricName);
      } else {
        // 如果没找到指标对象，直接用key和name尝试匹配
        metricData = companyMetrics.get(record.metricKey) ||
                    companyMetrics.get(record.metricName);
      }

      if (!metricData || !metric) {
        return "-";
      }

      const formattedValue = formatMetricValue(
        metricData.value,
        metric.value_type,
        dataUnit,
        metric
      );

      return (
        <div className="metric-value-cell">
          <MetricTooltip
            metric={metric}
            metricData={metricData}
            dataUnit={dataUnit}
            style={{}}
          >
            {formattedValue}
          </MetricTooltip>
        </div>
      );
    },
    [companyDataMap, metricKeyToObjectMap, dataUnit]
  );

  // 使用useMemo优化表格列构建
  const columns = useMemo(() => {
    const baseColumns = [
      {
        title: "指标分类",
        dataIndex: "metricCategory",
        key: "metricCategory",
        width: 150,
        render: (text, record) => {
          if (record.categoryRowSpan === 0) {
            return {
              props: {
                rowSpan: 0,
              },
            };
          }
          return {
            children: <div className="category-cell">{text}</div>,
            props: {
              rowSpan: record.categoryRowSpan,
            },
          };
        },
      },
      {
        title: "指标名称",
        dataIndex: "metricName",
        key: "metricName",
        width: 200,
        render: (text, record) => {
          // 如果是排名行，显示"排名"
          if (record.isRankRow) {
            return (
              <div className="metric-name-cell rank-cell" style={{ 
                fontWeight: 'bold', 
                color: '#1890ff',
                textAlign: 'center'
              }}>
                排名
              </div>
            );
          }
          
          return (
            <div className="metric-name-cell">
              <div>{text} {record.metricUnit??""}</div>
            </div>
          );
        },
      },
    ];

    // 为每个选中的公司添加列
    const companyColumns = selectedCompanies
      .map((companyId) => {
        const companyData = companyDataMap.get(companyId);
        if (!companyData) return null;

        const { company } = companyData;

        return {
          title: (
            <div style={{ textAlign: 'center' }}>
              <div>{company.company_name}</div>
              <div style={{ fontSize: '12px', color: '#666' }}>
                {company.company_code}
              </div>
              <div style={{ fontSize: '12px', color: '#666' }}>
                {company.report_date}
              </div>
            </div>
          ),
          dataIndex: companyId,
          key: companyId,
          width: 120,
          align: "right",
          render: (text, record) => renderCell(companyId, record),
        };
      })
      .filter(Boolean);

    return [...baseColumns, ...companyColumns];
  }, [selectedCompanies, companyDataMap, renderCell]);

  return (
    
     
      <Table
        columns={columns}
        dataSource={dataSource}
        loading={loading}
        pagination={false}
        scroll={{ x: "max-content", y: 600 }}
        size="small"
        bordered
        className="quantitative-table"
      /> 
  );
};

export default QuantitativeTable;