# 错误处理机制说明

## 概述

本模块提供了一套统一的错误处理机制，确保：
- 后端记录详细的错误日志（包含堆栈信息）
- 前端只收到简洁的错误信息
- 统一的错误响应格式

## 核心组件

### 1. 错误类 (error_handler.py)

```python
from ..utils.error_handler import ValidationError, NotFoundError, DatabaseError, APIError

# 参数验证错误
raise ValidationError('参数不能为空')

# 资源未找到错误
raise NotFoundError('用户不存在')

# 数据库错误
raise DatabaseError('数据库连接失败')

# 自定义API错误
raise APIError('业务逻辑错误', 'BUSINESS_ERROR', 400)
```

### 2. 错误处理函数

```python
from ..utils.error_handler import handle_exception, log_error

# 处理异常并返回错误响应
error_response, status_code = handle_exception(e, "获取用户数据")
return jsonify(error_response), status_code

# 只记录错误日志，不抛出异常
log_error(e, "数据处理失败", {"user_id": user_id})
```

### 3. 装饰器 (decorators.py)

```python
from ..utils.decorators import api_error_handler, validate_required_params

@api_error_handler("获取图表数据")
@validate_required_params('report_date', 'company_ids')
def get_chart_data():
    # 业务逻辑
    pass
```

## 使用示例

### 路由层错误处理

```python
# 方式1：使用装饰器（推荐）
@api_error_handler("获取用户数据")
@validate_required_params('user_id')
def get_user():
    user_id = request.get_json()['user_id']
    user = user_service.get_user(user_id)
    return jsonify({"success": True, "data": user})

# 方式2：手动处理
def get_user():
    try:
        user_id = request.get_json().get('user_id')
        if not user_id:
            raise ValidationError('user_id参数是必需的')
        
        user = user_service.get_user(user_id)
        return jsonify({"success": True, "data": user})
    except Exception as e:
        error_response, status_code = handle_exception(e, "获取用户数据")
        return jsonify(error_response), status_code
```

### 服务层错误处理

```python
def get_user(user_id):
    try:
        # 数据库查询
        result = execute_query("SELECT * FROM users WHERE id = ?", [user_id])
        if not result:
            raise NotFoundError('用户不存在')
        return result[0]
    except Exception as e:
        log_error(e, "查询用户失败", {"user_id": user_id})
        raise DatabaseError('查询用户失败')
```

## 错误响应格式

### 成功响应
```json
{
    "success": true,
    "data": {...}
}
```

### 错误响应
```json
{
    "success": false,
    "error": "系统异常，请稍后重试",
    "error_code": "DATABASE_ERROR"  // 可选
}
```

## 日志记录

错误日志会记录到 `api_error.log` 文件，包含：
- 错误类型
- 错误消息
- 错误上下文
- 完整堆栈信息
- 额外调试信息

## 最佳实践

1. **路由层**：使用装饰器或 `handle_exception` 函数
2. **服务层**：使用 `log_error` 记录错误，抛出适当的异常
3. **数据层**：抛出 `DatabaseError` 异常
4. **参数验证**：使用 `ValidationError` 异常
5. **资源不存在**：使用 `NotFoundError` 异常

## 迁移指南

### 从旧错误处理迁移

```python
# 旧方式
try:
    # 业务逻辑
    pass
except Exception as e:
    current_app.logger.error(f"Error: {e}")
    return jsonify({"error": str(e)}), 500

# 新方式
@api_error_handler("业务操作")
def business_operation():
    # 业务逻辑
    pass
``` 