#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
整合程序EXE构建脚本
用于将 integrated_processor.py 打包成可执行文件
"""

import os
import sys
import subprocess
import shutil
from datetime import datetime

def check_dependencies():
    """检查必要的依赖包"""
    required_packages = [
        'pyinstaller', 'pandas', 'numpy', 'pyodbc', 'WindPy'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
            print(f"✓ {package} 已安装")
        except ImportError:
            missing_packages.append(package)
            print(f"✗ {package} 未安装")
    
    if missing_packages:
        print(f"\n需要安装以下包: {', '.join(missing_packages)}")
        for package in missing_packages:
            try:
                subprocess.run([sys.executable, '-m', 'pip', 'install', package], check=True)
                print(f"✓ {package} 安装成功")
            except subprocess.CalledProcessError:
                print(f"✗ {package} 安装失败")
                return False
    
    return True

def clean_build_dirs():
    """清理构建目录"""
    dirs_to_clean = ['build', 'dist', '__pycache__']
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"✓ 清理目录: {dir_name}")

def build_exe():
    """构建EXE文件"""
    print("\n开始构建EXE文件...")
    
    # PyInstaller命令
    cmd = [
        'pyinstaller',
        '--onefile',                    # 打包成单个文件
        '--console',                    # 控制台应用
        '--name=integrated_processor',  # EXE文件名
        '--add-data=database;database', # 添加数据库目录
        '--add-data=logging_config.py;.', # 添加日志配置文件
        '--add-data=import_wind_data.py;.', # 添加Wind数据导入脚本
        '--add-data=metrics_calculator.py;.', # 添加指标计算脚本
        '--add-data=config.py;.', # 添加配置文件
        '--hidden-import=import_wind_data',
        '--hidden-import=metrics_calculator',
        '--hidden-import=pandas',
        '--hidden-import=numpy',
        '--hidden-import=pyodbc',
        '--hidden-import=WindPy',
        '--hidden-import=decimal',
        '--hidden-import=argparse',
        '--hidden-import=logging',
        '--hidden-import=datetime',
        '--hidden-import=uuid',
        '--hidden-import=math',
        '--hidden-import=concurrent.futures',
        '--hidden-import=threading',
        '--hidden-import=queue',
        '--hidden-import=json',
        '--hidden-import=time',
        '--hidden-import=os',
        '--hidden-import=sys',
        '--hidden-import=shutil',
        '--hidden-import=subprocess',
        'integrated_processor.py'
    ]
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✓ EXE构建成功!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ EXE构建失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False

def create_usage_guide():
    """创建使用说明"""
    guide_content = """整合数据处理程序使用说明
=====================================

程序名称: integrated_processor.exe
功能: 整合Wind数据导入和财务指标计算

智能默认值说明:
- 当前2季度查询1季度数据
- 当前1季度查询上一年4季度数据
- 其他情况查询上一个季度数据

使用方法:
1. 使用智能默认值（推荐）:
   integrated_processor.exe

2. 指定年份和季度:
   integrated_processor.exe --start-year 2024 --end-year 2024 --quarters 0331

3. 导入多个季度:
   integrated_processor.exe --start-year 2024 --end-year 2025 --quarters 0331 0630 0930 1231

4. 仅检查未处理数据:
   integrated_processor.exe --check-only

5. 强制重新计算:
   integrated_processor.exe --force-recalculate

6. 跳过数据导入:
   integrated_processor.exe --skip-import

7. 不保存到数据库:
   integrated_processor.exe --no-save

参数说明:
--start-year: 开始年份
--end-year: 结束年份
--quarters: 季度列表 (0331, 0630, 0930, 1231)
--check-only: 仅检查模式，不执行计算
--force-recalculate: 强制重新计算
--skip-import: 跳过Wind数据导入
--no-save: 不保存结果到数据库

注意事项:
1. 确保Wind金融终端已启动
2. 确保数据库连接正常
3. 首次运行可能需要较长时间
4. 建议先使用 --check-only 检查数据状态

技术支持:
如有问题，请查看日志文件或联系技术支持。
"""
    
    guide_path = os.path.join('dist', '使用说明.txt')
    with open(guide_path, 'w', encoding='utf-8') as f:
        f.write(guide_content)
    print(f"✓ 使用说明已创建: {guide_path}")

def main():
    """主函数"""
    print("=" * 60)
    print("整合程序EXE构建工具")
    print("=" * 60)
    print(f"构建时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"Python版本: {sys.version}")
    print(f"工作目录: {os.getcwd()}")
    print("-" * 60)
    
    # 检查依赖
    print("检查依赖包...")
    if not check_dependencies():
        print("依赖检查失败，退出构建")
        return False
    
    # 清理构建目录
    print("\n清理构建目录...")
    clean_build_dirs()
    
    # 构建EXE
    if not build_exe():
        print("构建失败")
        return False
    
    # 创建使用说明
    print("\n创建使用说明...")
    create_usage_guide()
    
    # 显示结果
    exe_path = os.path.join('dist', 'integrated_processor.exe')
    if os.path.exists(exe_path):
        file_size = os.path.getsize(exe_path) / (1024 * 1024)  # MB
        print(f"\n✓ 构建完成!")
        print(f"EXE文件: {exe_path}")
        print(f"文件大小: {file_size:.2f} MB")
        print(f"使用说明: {os.path.join('dist', '使用说明.txt')}")
        
        print("\n测试命令:")
        print(f"{exe_path} --help")
        print(f"{exe_path} --check-only")
    else:
        print("✗ EXE文件未找到")
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    if success:
        print("\n构建成功完成!")
    else:
        print("\n构建失败!")
        sys.exit(1) 