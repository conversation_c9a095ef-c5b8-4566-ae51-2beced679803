import React from 'react';
import HorizontalBar from './HorizontalBar';
import TwoFundIncomePanel from './TwoFundIncomePanel';

const TwoFundAnalysisPanel = ({
  allCompanies = [],
  twoFundNetData = [],
  twoFundIncomeData = [],
}) => {
  // 对齐数据，确保公司顺序一致
  const alignDataByCompany = (companies, data) => {
    return companies.map(company => data.find(d => d.company === company) || { company });
  };

  // 两金分析数据不需要对齐，因为它包含多个指标
  const alignedTwoFundNetData = twoFundNetData;
  const alignedTwoFundIncomeData = alignDataByCompany(allCompanies, twoFundIncomeData);
  const rowHeight =50;
  const chartHeight = allCompanies.length * rowHeight + 60; // 60是标题和padding的高度

  return (
    <div style={{ display: 'flex', alignItems: 'flex-start', gap: 0, padding: 2 ,marginBottom: 8, marginTop: 12 }}>
      {/* 两金分析图表 */}
      <div style={{ flex: 1, marginLeft: 0 }}>
        <HorizontalBar
          data={alignedTwoFundNetData}
          title="两金分析"
          encode={{ x: 'company', y: 'value', color: 'type' }}
          height={chartHeight}
        />
      </div>
     
    </div>
  );
};

export default TwoFundAnalysisPanel; 