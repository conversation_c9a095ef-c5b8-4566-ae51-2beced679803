from flask import Blueprint, jsonify, request, current_app
from ..services.metric_value_service import MetricValueService
from ..utils.error_handler import handle_exception

bp = Blueprint('metric_values', __name__, url_prefix='/api/metrics/values')

@bp.route('', methods=['GET'])
def get_metric_values():
    """获取指标值数据"""
    table_name = 'L2Metrics';
    report_date = request.args.get('reportDate')
    metrics = request.args.getlist('metrics') or None
    companies = request.args.getlist('companies') or None
    try:
        values = MetricValueService.get_values(table_name, report_date=report_date, metrics=metrics, companies=companies)
        if isinstance(values, dict) and 'error' in values:
            current_app.logger.error(f"Service error getting values: {values['error']}")
            return jsonify(values), 500
        return jsonify(values)
    except Exception as e:
        error_response, status_code = handle_exception(e, "获取指标数值")
        return jsonify(error_response), status_code
