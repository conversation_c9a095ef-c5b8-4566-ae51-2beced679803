# Wind数据导入配置示例文件
# 此文件展示了不同的配置场景，可以根据需要复制到 import_config.py

# ============================================================================
# 场景1: 导入2024-2025年的所有季度数据（完整数据）
# ============================================================================
START_YEAR = 2024
END_YEAR = 2025
QUARTERS = ['0331', '0630', '0930', '1231']

# ============================================================================
# 场景2: 只导入2024年的年报数据
# ============================================================================
# START_YEAR = 2024
# END_YEAR = 2024
# QUARTERS = ['1231']

# ============================================================================
# 场景3: 只导入2025年的半年报数据
# ============================================================================
# START_YEAR = 2025
# END_YEAR = 2025
# QUARTERS = ['0630']

# ============================================================================
# 场景4: 导入2023-2024年的年报和半年报
# ============================================================================
# START_YEAR = 2023
# END_YEAR = 2024
# QUARTERS = ['0630', '1231']

# ============================================================================
# 场景5: 只导入2025年一季度数据（用于测试）
# ============================================================================
# START_YEAR = 2025
# END_YEAR = 2025
# QUARTERS = ['0331']

# ============================================================================
# 场景6: 导入最近3年的年报数据
# ============================================================================
# from datetime import datetime
# current_year = datetime.now().year
# START_YEAR = current_year - 3
# END_YEAR = current_year
# QUARTERS = ['1231']

# ============================================================================
# 场景7: 导入2024年的所有季度数据
# ============================================================================
# START_YEAR = 2024
# END_YEAR = 2024
# QUARTERS = ['0331', '0630', '0930', '1231']

# ============================================================================
# 场景8: 只导入2025年三季度数据
# ============================================================================
# START_YEAR = 2025
# END_YEAR = 2025
# QUARTERS = ['0930']

# 使用说明：
# 1. 将需要的场景配置复制到 import_config.py 文件中
# 2. 确保只保留一组配置（取消注释需要的配置，注释掉其他配置）
# 3. 运行 import_wind_data.py 开始数据导入 