@echo off
chcp 65001 >nul
echo ============================================================
echo Wind数据导入工具 - 打包脚本
echo ============================================================
echo.

echo 🔍 检查Python环境...
python --version
if %errorlevel% neq 0 (
    echo ❌ Python未安装或未添加到PATH
    pause
    exit /b 1
)

echo.
echo 🔍 检查依赖包...
python -c "import pyinstaller" 2>nul
if %errorlevel% neq 0 (
    echo ⚠️  PyInstaller未安装，正在安装...
    pip install pyinstaller
    if %errorlevel% neq 0 (
        echo ❌ PyInstaller安装失败
        pause
        exit /b 1
    )
)

python -c "import pandas" 2>nul
if %errorlevel% neq 0 (
    echo ⚠️  pandas未安装，正在安装...
    pip install pandas
)

python -c "import numpy" 2>nul
if %errorlevel% neq 0 (
    echo ⚠️  numpy未安装，正在安装...
    pip install numpy
)

python -c "import pyodbc" 2>nul
if %errorlevel% neq 0 (
    echo ⚠️  pyodbc未安装，正在安装...
    pip install pyodbc
)

echo.
echo ✅ 依赖检查完成
echo.
echo 🚀 开始打包...
python build_exe_simple.py

if %errorlevel% equ 0 (
    echo.
    echo 🎉 打包成功！
    echo 📁 输出目录: dist\
    echo 📄 主程序: dist\wind_import.exe
    echo 📖 使用说明: dist\使用说明.txt
    echo.
    echo 💡 提示: 可以直接运行 wind_import.exe 使用智能默认值
) else (
    echo.
    echo ❌ 打包失败，请检查错误信息
)

echo.
pause 