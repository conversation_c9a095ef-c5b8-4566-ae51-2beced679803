/* 基础样式 */
body {
    font-family: 'Microsoft YaHei', sans-serif;
    margin: 0;
    padding: 10px 0 0 10px;
    box-sizing: border-box;
    width: 100%; 
    min-width: 1280px;
}

/* 响应式布局 */
@media (max-width: 768px) {
    body {
        padding: 10px;
    }
}

/* 指标选择器样式 */
.metric-selectors {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 20px;
}

.metric-tag {
    background: #f0f0f0;
    border-radius: 16px;
    padding: 4px 12px;
    display: inline-flex;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s;
}

.metric-tag:hover {
    background: #e0e0e0;
}

.metric-tag input {
    display: none;
}

.metric-tag label {
    cursor: pointer;
    padding: 4px 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 200px;
}

/* 表格样式 */
#metrics-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
}

#metrics-table th, #metrics-table td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid #ddd;
    position: relative;
}

#metrics-table th {
    background-color: #f8f9fa;
    font-weight: bold;
}

#metrics-table tr:hover td {
    background-color: #f5f5f5;
}

/* 悬浮提示样式 */
td[title]:hover:after {
    content: attr(title);
    position: absolute;
    left: 100%;
    top: 0;
    min-width: 200px;
    background: #fff;
    border: 1px solid #ddd;
    padding: 8px;
    z-index: 100;
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    white-space: pre-line;
}


/* Ant Design Table Customizations for Enhanced Appearance */
.ant-table-thead > tr > th {
  background-color: #007bff; /* A primary color for header */
  color: white;
  font-weight: 600;
}

.ant-table-tbody > tr.ant-table-row:nth-child(odd) > td {
  background-color: #f8f9fa; /* Light gray for odd rows */
}

.ant-table-tbody > tr.ant-table-row:hover > td {
  background-color: #e9ecef; /* Light grey hover */
}

.container {
    display: flex;
    flex-direction: column; 
    min-height: 0;
}

/* Card外层包裹一个flex容器 */
.card-flex {
    display: flex;
    flex-direction: column;
    flex: 1;
    min-height: 0;
}

.selectors {
    margin-bottom: 0px;
}

/* 表格自适应剩余高度 */
.ant-card-body {
    display: flex;
    flex-direction: column;
    flex: 1;
    min-height: 0;
}

.ant-table-wrapper {
    flex: 1;
    min-height: 0;
    display: flex;
    flex-direction: column;
}

.ant-table {
    flex: 1;
    min-height: 0;
    display: flex;
    flex-direction: column;
}

.ant-table-content {
    flex: 1;
    min-height: 0;
    display: flex;
    flex-direction: column;
}

.ant-table-body {
    flex: 1 !important;
    min-height: 0 !important;
    overflow: auto !important;
}
