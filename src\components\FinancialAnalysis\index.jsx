import React from "react";
import { Card, message } from "antd";
import FilterPanel from "./FilterPanel";
import FinancialTable from "./FinancialTable";
import QuantitativeTable from "./QuantitativeTable";
import {
  processTableData,
  generateReportDate,
  displayError,
  recalculateRanks,
} from "./utils";
import { formatMetricValue } from "../../utils/formatters";
import { utils as xlsxUtils, writeFile } from "xlsx";
import { getDefaultYearAndQuarter } from '../../utils/formatters';

// 常量定义
const METRIC_CONFIGS = {
  quantitative: [
    "roe",
    "roe_basic", 
    "debt_to_assets_ratio",
    "rd_intensity",
    "operating_cash_ratio",
    "cash_collection_ratio",
    "tot_profit",
    "oper_rev",
    "eqy_belongto_parcomsh",
    "tot_equity",
    "gross_margin",
    "net_profit_margin",
    "total_asset_turnover",
    "equity_turnover",
    "net_profit_cash_ratio",
    "three_fee_ratio",
    "rd_to_three_fee_ratio",
    "rd_exp",
    "stmnote_rdexp",
  ],
  quantitative_assessment: [
   // 财务质量 (financial_quality)
   "roe", "roic", "total_asset_turnover", "net_profit_cash_ratio", 
   "debt_to_assets_ratio", "interest_coverage_ratio", "oper_rev_growth_rate",
   
   // 创新能力 (innovation_capability)
   "rd_intensity", "rd_personnel_ratio", "revenue_per_rd_personnel",
   
   // 公司治理 (corporate_governance)
   "related_party_revenue_ratio", "related_party_cost_ratio",
   
   // 利益相关者关系管理 (stakeholder_relationship)
   "inventory_turnover", "receivables_turnover","div_aualcashdividend","cash_dividend_ratio",
  ]
};

const QUANTITATIVE_ASSESSMENT_GROUPS = [
  {
    category: "财务质量",
    categoryKey: "financial_quality",
    metrics: [
      { name: "净资产收益率", key: "roe", unit: "(%)" },
      { name: "资本回报率", key: "roic", unit: "(%)" },
      { name: "总资产周转率", key: "total_asset_turnover", unit: "(%)" },
      { name: "净利润现金比率", key: "net_profit_cash_ratio", unit: "(%)" },
      { name: "资产负债率", key: "debt_to_assets_ratio", unit: "(%)" },
      { name: "利息保障倍数", key: "interest_coverage_ratio", unit: "" },
      { name: "营业收入增长率", key: "oper_rev_growth_rate", unit: "(%)" }
    ]
  },
  {
    category: "创新能力",
    categoryKey: "innovation_capability",
    metrics: [
      { name: "研发投入强度", key: "rd_intensity", unit: "(%)" },
      { name: "研发人员占比", key: "rd_personnel_ratio", unit: "(%)" },
      { name: "每万名研发人员主营业务收入", key: "revenue_per_rd_personnel", unit: "（亿元）" },
    ]
  },
  {
    category: "公司治理",
    categoryKey: "corporate_governance",
    metrics: [
      { name: "关联业务收入占比", key: "related_party_revenue_ratio", unit: "(%)" },
      { name: "关联业务成本占比", key: "related_party_cost_ratio", unit: "(%)" }
    ]
  },
  {
    category: "利益相关者关系管理",
    categoryKey: "stakeholder_relationship",
    metrics: [
      { name: "库存周转率", key: "inventory_turnover", unit: "" },
      { name: "应收账款周转率", key: "receivables_turnover", unit: "" },
      { name: "分红金额", key: "div_aualcashdividend", unit: "(亿元)" },
      { name: "现金分红率", key: "cash_dividend_ratio", unit: "" }
    ]
  },

];

/**
 * 财务分析主组件
 */
class FinancialAnalysis extends React.PureComponent {
  // 工具方法：获取指定表格类型的指标列表
  getMetricsForTableType = (tableType, availableMetricCodes) => {
    const metricCodes = METRIC_CONFIGS[tableType] || availableMetricCodes;
    const selectedMetrics = metricCodes.filter(code => 
      availableMetricCodes.includes(code)
    );
    return selectedMetrics.length > 0 ? selectedMetrics : availableMetricCodes;
  };

  // 工具方法：更新表格数据和排名
  updateTableDataWithRanks = (tableData, selectedCompanies, metrics) => {
    return recalculateRanks(tableData, selectedCompanies, metrics);
  };

  // 工具方法：预处理渲染数据
  prepareRenderData = (tableData, selectedCompanies, selectedMetrics, metrics) => {
    // 预筛选公司数据
    const filteredTableData = tableData.filter(
      (item) => item.isRankRow || selectedCompanies.includes(item.company_id)
    );

    // 预筛选指标对象
    const selectedMetricsObjects = selectedMetrics
      .map((metricCode) => metrics.find((m) => m.code === metricCode))
      .filter((metric) => metric);

    return {
      filteredTableData,
      selectedMetricsObjects
    };
  };

  // 工具方法：筛选公司
  filterCompaniesByType = (companies, companyType) => {
    switch (companyType) {
      case "internal":
        return companies.filter(company => company.IsInternal === true);
      case "external":
        return companies.filter(company => company.IsInternal !== true);
      default:
        return companies;
    }
  };

  // 工具方法：获取API数据
  fetchApiData = async (reportDate) => {
    const apiBaseUrl = "./api";
    
    const [definitionsRes, companiesRes, valuesRes] = await Promise.all([
      fetch(`${apiBaseUrl}/metrics/definitions?table=L2Metrics&reportDate=${reportDate}`),
      fetch(`${apiBaseUrl}/companies?table=L2Metrics&reportDate=${reportDate}`),
      fetch(`${apiBaseUrl}/metrics/values?table=L2Metrics&reportDate=${reportDate}`),
    ]);

    if (!definitionsRes.ok || !companiesRes.ok || !valuesRes.ok) {
      throw new Error(
        `HTTP error! status: ${definitionsRes.status}, ${companiesRes.status}, ${valuesRes.status}`
      );
    }

    return Promise.all([
      definitionsRes.json(),
      companiesRes.json(), 
      valuesRes.json(),
    ]);
  };

  // 工具方法：设置Excel工作表格式
  setWorksheetFormat = (worksheet, tableType, filteredData, selectedMetricsObjects) => {
    if (tableType === "quantitative_assessment") {
      // 定量评估表的列宽设置
      const wscols = [
        { wch: 20 }, // 指标分类列宽
        { wch: 25 }, // 指标名称列宽
      ];
      
      filteredData.forEach(() => {
        wscols.push({ wch: 15 }); // 公司数据列宽
      });
      
      worksheet["!cols"] = wscols;
      
      // 为定量评估表添加合并单元格（指标分类列）
      const merges = [];
      let currentRow = 1;
      
      const metricGroupCounts = [
        { category: "财务质量", count: 7 },
        { category: "创新能力", count: 3 },
        { category: "公司治理", count: 2 },
        { category: "利益相关者关系管理", count: 4 }
      ];
      
      metricGroupCounts.forEach(group => {
        if (group.count > 1) {
          merges.push({
            s: { r: currentRow, c: 0 },
            e: { r: currentRow + group.count - 1, c: 0 }
          });
        }
        currentRow += group.count;
      });
      
      worksheet["!merges"] = merges;
    } else {
      // 其他表格类型的列宽设置
      const wscols = [
        { wch: 20 }, // 公司名称列宽
        { wch: 15 }, // 公司分组列宽
      ];
      
      selectedMetricsObjects.forEach(() => {
        wscols.push({ wch: 15 }); // 指标列宽
      });
      
      worksheet["!cols"] = wscols;
    }
  };
  state = {
    loading: true,
    metrics: [],
    companies: [],
    selectedMetrics: [],
    selectedCompanies: [],
    tableData: [],
    // 生成从2019年到当前年份的年份数组
    years: (() => {
      const currentDate = new Date();
      const currentYear = currentDate.getFullYear();
      const currentMonth = currentDate.getMonth() + 1; // getMonth()返回0-11
      
      // 如果当前月份小于4月,使用上一年作为最新年份
      const maxYear = currentMonth < 4 ? currentYear - 1 : currentYear;
      
      // 生成从2019到maxYear的年份数组
      const years = [];
      for(let year = 2019; year <= maxYear; year++) {
        years.push(year);
      }
      return years;
    })(),
    
    // 默认选择最新的年份（将在componentDidMount中更新）
    selectedYear: 2024,
    quarters: [
      { label: "一季度", value: "Q1", month: "03", day: "31" },
      { label: "二季度", value: "Q2", month: "06", day: "30" },
      { label: "三季度", value: "Q3", month: "09", day: "30" },
      { label: "年度报告", value: "ANNUAL", month: "12", day: "31" },
    ],
    selectedQuarter: "ANNUAL", // 默认年度报告
    reportDate: `2024-12-31`, // 初始化 reportDate
    tableType: "quantitative", // "quantitative", "quantitative_assessment", or "custom"
    dataUnit: "元", // 数据单位：元、万元、亿元
    companyType: "all", // 公司所属：all(全部)、internal(集团内)、external(集团外)
    
    // 筛选条件的临时状态，只有点击检索时才应用到实际选择
    tempSelectedMetrics: [],
    tempSelectedCompanies: [],
    tempSelectedYear: 2024,
    tempSelectedQuarter: "ANNUAL",
    tempTableType: "quantitative",
    tempDataUnit: "元",
    tempCompanyType: "all",
    

  };

  async componentDidMount() {
    try {
      // 首先获取默认年份和季度
      const { year, quarter } = await getDefaultYearAndQuarter();
      
      // 根据季度确定报告日期
      const quarterMap = {
        'Q1': '03-31',
        'Q2': '06-30', 
        'Q3': '09-30',
        'ANNUAL': '12-31'
      };
      const reportDate = `${year}-${quarterMap[quarter]}`;
      
      // 更新状态
      this.setState({
        selectedYear: year,
        selectedQuarter: quarter,
        reportDate: reportDate,
        tempSelectedYear: year,
        tempSelectedQuarter: quarter
      });
      
      const [definitions, companies, values] = await this.fetchApiData(reportDate);
      
      const availableMetricCodes = definitions.map((m) => m.code);
      const selectedMetricsToUse = this.getMetricsForTableType('quantitative', availableMetricCodes);
      
      const tableData = processTableData(
        {
          metrics_values: values,
          metrics_definitions: definitions,
          companies,
        },
        definitions
      );

      const selectedCompanies = companies.map((c) => c.id);
      const updatedTableData = this.updateTableDataWithRanks(tableData, selectedCompanies, definitions);

      this.setState({
        loading: false,
        metrics: definitions,
        companies: companies,
        selectedMetrics: selectedMetricsToUse,
        selectedCompanies: selectedCompanies,
        // 同时初始化临时状态
        tempSelectedMetrics: selectedMetricsToUse,
        tempSelectedCompanies: selectedCompanies,
        tableData: updatedTableData,
      });
    } catch (error) {
      displayError(error.message || "数据加载失败");
      this.setState({ loading: false });
    }
  }

  handleMetricChange = (selectedMetrics) => {
    // 只更新临时选中的指标，不影响表格显示
    this.setState({
      tempSelectedMetrics: selectedMetrics,
    });
  };

  handleCompanyChange = (selectedCompanies) => {
    // 只更新临时选中的公司，不影响表格显示
    this.setState({
      tempSelectedCompanies: selectedCompanies,
    });
  };

  handleDateChange = (changedValue, type) => {
    let { tempSelectedYear, tempSelectedQuarter } = this.state;

    if (type === "year") {
      tempSelectedYear = changedValue;
    } else if (type === "quarter") {
      tempSelectedQuarter = changedValue;
    }

    // 只更新临时年份和季度，不影响表格显示
    this.setState({
      tempSelectedYear,
      tempSelectedQuarter,
    });
  };

  handleTableTypeChange = (tableType) => {
    const { metrics } = this.state;
    const availableMetricCodes = metrics.map((m) => m.code);
    
    let selectedMetricsToUse;
    if (tableType === "custom") {
      selectedMetricsToUse = availableMetricCodes;
    } else {
      selectedMetricsToUse = this.getMetricsForTableType(tableType, availableMetricCodes);
    }

    // 只更新临时表格类型和临时选中指标，不影响表格显示
    this.setState({
      tempTableType: tableType,
      tempSelectedMetrics: selectedMetricsToUse,
    });
  };

  // 处理数据单位变更
  handleDataUnitChange = (dataUnit) => {
    this.setState({ tempDataUnit: dataUnit });
  };

  // 处理公司类型筛选变更
  handleCompanyTypeChange = (companyType) => {
    const { companies } = this.state;
    const filteredCompanies = this.filterCompaniesByType(companies, companyType);
    const selectedCompanies = filteredCompanies.map(c => c.id);

    // 只更新临时公司类型和临时选中公司，不影响表格显示
    this.setState({
      tempCompanyType: companyType,
      tempSelectedCompanies: selectedCompanies,
    });
  };



  // 定量评估表分组格式导出
  exportQuantitativeAssessmentFormat = (filteredData, selectedMetricsObjects, dataUnit, rankRow) => {
    const metricGroups = QUANTITATIVE_ASSESSMENT_GROUPS;

    const worksheetData = [];

    // 创建表头行
    const headers = ["指标", "指标名称"];
    filteredData.forEach(company => {
      headers.push(company.company_name);
    });
    worksheetData.push(headers);

    // 按分组添加数据行
    metricGroups.forEach(group => {
      group.metrics.forEach((metric, metricIndex) => {
        const rowData = [];
        
        // 第一列：指标分类（只在每组第一行显示）
        if (metricIndex === 0) {
          rowData.push(group.category);
        } else {
          rowData.push(""); // 其他行留空，Excel中可以手动合并单元格
        }
        
        // 第二列：指标名称和单位
        rowData.push(`${metric.name}${metric.unit}`);
        
        // 后续列：各公司数据
        filteredData.forEach(company => {
          // 使用key字段匹配指标，因为QUANTITATIVE_ASSESSMENT_GROUPS中的key对应指标的code
          const metricObj = selectedMetricsObjects.find(m => m.code === metric.key);
          if (metricObj) {
            const metricData = company.metric_values[metricObj.code];
            
            if (metricData && metricData.value !== undefined && metricData.value !== null) {
              // 使用formatMetricValue函数处理数值，forExport=true返回数字格式
              const value = formatMetricValue(
                metricData.value,
                metricObj.value_type,
                dataUnit,
                metricObj,
                true // forExport参数
              );
              rowData.push(value);
            } else {
              rowData.push(null);
            }
          } else {
            rowData.push(null);
          }
        });
        
        worksheetData.push(rowData);
      });
    });

    // 定量评估表导出不添加排名行

    return worksheetData;
  };

  // 原有平铺格式导出
  exportFlatFormat = (filteredData, selectedMetricsObjects, dataUnit, rankRow, maxRow, minRow) => {
    const worksheetData = [];
    
    // 创建表头行
    const headers = ["公司名称", "公司分组"];
    selectedMetricsObjects.forEach((metric) => {
      headers.push(metric.name);
    });
    
    worksheetData.push(headers);

    // 添加排名行（如果存在）
    if (rankRow) {
      const rankRowData = ["排名", ""];
      selectedMetricsObjects.forEach((metric) => {
        const diankeRank = rankRow.metric_values[metric.code]?.diankeRank;
        if (diankeRank !== undefined && diankeRank !== null && diankeRank !== "-") {
          // 确保排名是整数格式
          const rankValue = parseInt(diankeRank, 10);
          rankRowData.push(isNaN(rankValue) ? null : rankValue);
        } else {
          rankRowData.push(null);
        }
      });
      worksheetData.push(rankRowData);
    }

    // 添加最大值行
    if (maxRow) {
      const maxRowData = ["最大值", ""];
      selectedMetricsObjects.forEach((metric) => {
        const metricData = maxRow.metric_values[metric.code];
        if (metricData && metricData.value !== undefined && metricData.value !== null && metricData.value !== '-') {
          const value = formatMetricValue(
            metricData.value,
            metric.value_type,
            dataUnit,
            metric,
            true // forExport参数
          );
          // 显示格式：公司名称(数值)
          const displayText = `${metricData.company_name}(${value})`;
          maxRowData.push(displayText);
        } else {
          maxRowData.push(null);
        }
      });
      worksheetData.push(maxRowData);
    }

    // 添加最小值行
    if (minRow) {
      const minRowData = ["最小值", ""];
      selectedMetricsObjects.forEach((metric) => {
        const metricData = minRow.metric_values[metric.code];
        if (metricData && metricData.value !== undefined && metricData.value !== null && metricData.value !== '-') {
          const value = formatMetricValue(
            metricData.value,
            metric.value_type,
            dataUnit,
            metric,
            true // forExport参数
          );
          // 显示格式：公司名称(数值)
          const displayText = `${metricData.company_name}(${value})`;
          minRowData.push(displayText);
        } else {
          minRowData.push(null);
        }
      });
      worksheetData.push(minRowData);
    }

    // 添加数据行
    filteredData.forEach((company) => {
      // 跳过排名行、最大值行、最小值行，只处理公司数据行
      if (company.isRankRow || company.isMaxRow || company.isMinRow) {
        return;
      }

      const rowData = [company.company_name, company.companyGroup];

      // 添加每个指标的值
      selectedMetricsObjects.forEach((metric) => {
        const metricData = company.metric_values[metric.code];

        if (metricData && metricData.value !== undefined && metricData.value !== null) {
          // 使用formatMetricValue函数处理数值，forExport=true返回数字格式
          const value = formatMetricValue(
            metricData.value,
            metric.value_type,
            dataUnit,
            metric,
            true // forExport参数
          );
          rowData.push(value);
        } else {
          rowData.push(null);
        }
      });

      worksheetData.push(rowData);
    });

    return worksheetData;
  };

  // 导出表格数据为Excel
  handleExport = () => {
    const {
      tableData,
      metrics,
      selectedMetrics,
      selectedCompanies,
      dataUnit,
      tableType,
    } = this.state;

    try {
      // 获取排名行
      const rankRow = tableData.find((item) => item.isRankRow);

      // 筛选选中的公司数据行（不包含排名行、最大值行、最小值行）
      const companyDataRows = tableData.filter(
        (item) => !item.isRankRow && !item.isMaxRow && !item.isMinRow && selectedCompanies.includes(item.company_id)
      );

      // 获取选中的指标对象，并按照界面显示的顺序排序
      const selectedMetricsObjects = selectedMetrics
        .map((metricCode) => metrics.find((m) => m.code === metricCode))
        .filter((metric) => metric); // 过滤掉未找到的指标

      // 动态计算最大值和最小值行
      const maxRow = {
        company_id: 'max_row',
        company_name: '',
        companyGroup: '',
        key: 'max_row',
        isMaxRow: true,
        isFixedRow: true,
        metric_values: {},
      };

      const minRow = {
        company_id: 'min_row',
        company_name: '',
        companyGroup: '',
        key: 'min_row',
        isMinRow: true,
        isFixedRow: true,
        metric_values: {},
      };

      // 对每个指标计算最大值和最小值
      selectedMetricsObjects.forEach(metric => {
        // 过滤掉无效值并转换单位
        const validCompanies = companyDataRows.filter(company => {
          const value = company.metric_values[metric.code]?.value;
          return value !== undefined && value !== null && !isNaN(value);
        }).map(company => {
          const originalValue = company.metric_values[metric.code]?.value;
          // 转换数值
          let convertedValue = originalValue;
          if (originalValue !== undefined && originalValue !== null && !isNaN(originalValue)) {
            const numValue = Number(originalValue);
            // 如果是百分比，不需要转换单位
            if (metric.value_type && metric.value_type.trim() !== "元") {
              convertedValue = numValue;
            } else {
              // 根据选择的单位进行转换
              if (dataUnit === "万元") {
                convertedValue = numValue / 10000;
              } else if (dataUnit === "亿元") {
                convertedValue = numValue / *********;
              } else {
                // 默认单位为"元"，不需要转换
                convertedValue = numValue;
              }
            }
          }
          return {
            ...company,
            convertedValue,
            originalValue
          };
        });

        if (validCompanies.length > 0) {
          // 找到最大值公司（使用转换后的值进行比较）
          const maxCompany = validCompanies.reduce((max, current) => {
            return current.convertedValue > max.convertedValue ? current : max;
          });

          // 找到最小值公司（使用转换后的值进行比较）
          const minCompany = validCompanies.reduce((min, current) => {
            return current.convertedValue < min.convertedValue ? current : min;
          });

          const maxValue = maxCompany.originalValue;
          const minValue = minCompany.originalValue;

          // 设置最大值行
          maxRow.metric_values[metric.code] = {
            value: maxValue,
            company_name: maxCompany.company_name,
            display_value: maxValue
          };

          // 设置最小值行
          minRow.metric_values[metric.code] = {
            value: minValue,
            company_name: minCompany.company_name,
            display_value: minValue
          };
        } else {
          // 如果没有有效值，显示占位符
          maxRow.metric_values[metric.code] = {
            value: '-',
            company_name: '-',
            display_value: '-'
          };
          minRow.metric_values[metric.code] = {
            value: '-',
            company_name: '-',
            display_value: '-'
          };
        }
      });

      // 根据表格类型选择不同的导出格式
      let worksheetData = [];
      
      if (tableType === "quantitative_assessment") {
        // 定量评估表使用分组格式导出
        worksheetData = this.exportQuantitativeAssessmentFormat(
          companyDataRows, 
          selectedMetricsObjects, 
          dataUnit, 
          rankRow
        );
      } else {
        // 其他表格类型使用原有的平铺格式
        worksheetData = this.exportFlatFormat(
          companyDataRows, 
          selectedMetricsObjects, 
          dataUnit, 
          rankRow,
          maxRow,
          minRow
        );
      }

      // 创建工作簿
      const worksheet = xlsxUtils.aoa_to_sheet(worksheetData);
      const workbook = xlsxUtils.book_new();
      xlsxUtils.book_append_sheet(workbook, worksheet, "财务指标对比");

      // 设置列宽和格式
      this.setWorksheetFormat(worksheet, tableType, companyDataRows, selectedMetricsObjects);

      // 设置单元格格式为数字类型
      const range = xlsxUtils.decode_range(worksheet["!ref"]);
      for (let R = 1; R <= range.e.r; R++) {
        // 从第二行开始（跳过表头）
        const startCol = tableType === "quantitative_assessment" ? 2 : 2;
        for (let C = startCol; C <= range.e.c; C++) {
          const cell_address = xlsxUtils.encode_cell({ r: R, c: C });
          const cell = worksheet[cell_address];
          if (cell && (typeof cell.v === "number" || (cell.v !== null && cell.v !== undefined && !isNaN(Number(cell.v))))) {
            // 确保单元格值是数字类型
            if (typeof cell.v !== "number") {
              cell.v = Number(cell.v);
              cell.t = "n"; // 设置单元格类型为数字
            }
            
            // 检查是否是排名行（第二行）
            if (R === 2 && rankRow && tableType !== "quantitative_assessment") {
              // 排名行使用整数格式（仅在非定量评估表中）
              cell.z = "0";
            } else {
              // 其他数值使用2位小数格式
              cell.z = "0.00";
            }
          }
        }
      }

      // 生成文件名
      let sheetName = "财务指标对比";
      if (tableType === "quantitative") {
        sheetName = "一利五率";
      } else if (tableType === "quantitative_assessment") {
        sheetName = "定量评估表";    
      } else if (tableType === "custom") {
        sheetName = "自定义指标";
      }

      // 添加数据单位到sheet名称
      sheetName = `${sheetName}_${dataUnit}`;

      const fileName = `${sheetName}_${
        new Date().toISOString().split("T")[0]
      }.xlsx`;

      // 导出文件
      writeFile(workbook, fileName);

      // 显示成功消息
      message.success("导出成功");
    } catch (error) {
      console.error("导出失败:", error);
      message.error("导出失败: " + error.message);
    }
  };

  // 处理检索按钮点击
  handleSearch = async () => {
    const { 
      tempSelectedYear, 
      tempSelectedQuarter, 
      quarters, 
      tempTableType, 
      tempCompanyType,
      tempSelectedMetrics 
    } = this.state;

    this.setState({ loading: true });

    try {
      const reportDate = generateReportDate(tempSelectedYear, tempSelectedQuarter, quarters);
      const [definitions, companies, values] = await this.fetchApiData(reportDate);
      
      const availableMetricCodes = definitions.map((m) => m.code);
      let selectedMetricsToUse;
      
      if (tempTableType === "custom") {
        if (
          tempSelectedMetrics.length === 0 ||
          !tempSelectedMetrics.some((code) => availableMetricCodes.includes(code))
        ) {
          selectedMetricsToUse = availableMetricCodes;
        } else {
          selectedMetricsToUse = tempSelectedMetrics;
        }
      } else {
        selectedMetricsToUse = this.getMetricsForTableType(tempTableType, availableMetricCodes);
      }

      const tableData = processTableData(
        {
          metrics_values: values,
          metrics_definitions: definitions,
          companies,
        },
        definitions
      );

      // 根据公司类型筛选公司
      const filteredCompanies = this.filterCompaniesByType(companies, tempCompanyType);
      
      // 使用临时选择的公司，如果临时选择为空或不在筛选范围内，则使用所有筛选后的公司
      const { tempSelectedCompanies } = this.state;
      const validSelectedCompanies = tempSelectedCompanies.filter(companyId => 
        filteredCompanies.some(company => company.id === companyId)
      );
      
      // 如果临时选择的公司都不在筛选范围内，则使用所有筛选后的公司
      const companiesToUse = validSelectedCompanies.length > 0 ? validSelectedCompanies : filteredCompanies.map(c => c.id);
      
      const updatedTableData = this.updateTableDataWithRanks(
        tableData,
        companiesToUse,
        definitions
      );

      // 将临时状态应用到实际状态
      this.setState({
         metrics: definitions,
         companies: companies,
         selectedMetrics: selectedMetricsToUse,
         selectedCompanies: companiesToUse,
         tableData: updatedTableData,
         selectedYear: tempSelectedYear,
         selectedQuarter: tempSelectedQuarter,
         tableType: tempTableType,
         dataUnit: this.state.tempDataUnit,
         companyType: tempCompanyType,
         loading: false,
       });
     } catch (error) {
       displayError(error.message || "数据加载失败");
       this.setState({ loading: false });
     }
   };

  render() {
    const {
      loading,
      metrics,
      companies,
      selectedMetrics,
      selectedCompanies,
      tableData,
      years,
      selectedYear,
      tableType,
      quarters,
      selectedQuarter,
      dataUnit,
      companyType,

      // 临时状态用于FilterPanel显示
      tempSelectedMetrics,
      tempSelectedCompanies,
      tempSelectedYear,
      tempSelectedQuarter,
      tempTableType,
      tempDataUnit,
      tempCompanyType,
    } = this.state;

    // 预处理渲染数据，避免在子组件中重复过滤
    const { filteredTableData, selectedMetricsObjects } = this.prepareRenderData(
      tableData,
      selectedCompanies, // 使用用户选择的公司ID
      selectedMetrics,
      metrics
    );

    return (
      <div className="container">
        <Card   style={{ marginBottom: 4,padding: 4 }}>
          <FilterPanel
            years={years}
            selectedYear={tempSelectedYear}
            quarters={quarters}
            selectedQuarter={tempSelectedQuarter}
            tableType={tempTableType}
            metrics={metrics}
            selectedMetrics={tempSelectedMetrics}
            companies={companies}
            selectedCompanies={tempSelectedCompanies}
            dataUnit={tempDataUnit}
            companyType={tempCompanyType}
            onDateChange={this.handleDateChange}
            onTableTypeChange={this.handleTableTypeChange}
            onMetricChange={this.handleMetricChange}
            onCompanyChange={this.handleCompanyChange}
            onDataUnitChange={this.handleDataUnitChange}
            onCompanyTypeChange={this.handleCompanyTypeChange}
            onSearch={this.handleSearch}
            onExport={this.handleExport}
            exportDisabled={loading || tableData.length <= 1}
            loading={loading}
          />
        </Card>

        {tableType === "quantitative_assessment" ? (
          <QuantitativeTable
            tableData={filteredTableData}
            metrics={metrics}
            selectedMetrics={selectedMetrics}
            selectedCompanies={selectedCompanies}
            selectedMetricsObjects={selectedMetricsObjects}
            dataUnit={dataUnit}
            loading={loading}
          />
        ) : (
          <FinancialTable
            tableData={filteredTableData}
            metrics={metrics}
            selectedMetrics={selectedMetrics}
            selectedCompanies={selectedCompanies}
            selectedMetricsObjects={selectedMetricsObjects}
            dataUnit={dataUnit}
            loading={loading}
          />
        )}
      </div>
    );
  }
}

export default FinancialAnalysis;
