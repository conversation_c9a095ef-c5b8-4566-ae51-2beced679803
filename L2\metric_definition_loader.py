# L2/metric_definition_loader.py
import pandas as pd
import os
from database.db_handler import DatabaseHandler

class MetricDefinitionLoader:
    """
    负责加载指标定义和映射。
    可以从数据库或文件加载。
    """
    def __init__(self):
        self.db = DatabaseHandler()
        # 加载指标定义文件中的中英文对照关系
        self.indicator_definitions = self.get_metric_formulas_from_db()
        # 加载MetricsDefinition表中的基础指标定义
        self.metrics_definitions = self.load_metrics_definitions_from_db()

    def load_metrics_definitions_from_db(self):
        """
        从MetricsDefinition表加载基础指标定义
        
        返回:
            dict: 包含指标定义的字典，格式为{指标英文名: {'ShowName': 中文名称}}
        """
        query = """
        SELECT Name, ShowName 
        FROM MetricsDefinition
        WHERE Enabled = 1
        """
        try:
            with self.db as db:
                cursor = db.connection.cursor()
                cursor.execute(query)
                rows = cursor.fetchall()
            
            return {
                row[0]: {'ShowName': row[1]} 
                for row in rows if row[0] and row[1]
            }
        except Exception as e:
            print(f"从MetricsDefinition表加载指标定义时出错: {str(e)}")
            return {}
    def load_metrics_definitions(self):
        """
        从MetricsDefinition表加载基础指标定义
        
        返回:
            dict: 包含指标定义的字典，格式为{指标英文名: {'ShowName': 中文名称}}
        """
        query = """
        SELECT WindCode, ShowName 
        FROM MetricsDefinition
        WHERE Enabled = 1
        """
        try:
            with self.db as db:
                cursor = db.connection.cursor()
                cursor.execute(query)
                rows = cursor.fetchall()
            
            return {
                row[0]: {'ShowName': row[1]} 
                for row in rows if row[0] and row[1]
            }
        except Exception as e:
            print(f"从MetricsDefinition表加载指标定义时出错: {str(e)}")
            return {}

    def load_metrics_mapping(self):
        """
        从L2BenchmarkIndicators表加载指标映射，如果失败则从CSV加载。
        同时会合并MetricsDefinition表中的基础指标定义。
        """
        try:
            # 首先尝试从数据库加载
            query = """
            SELECT IndicatorNameEN, IndicatorNameCN, Formula, FormulaDesc, LowerBound, UpperBound, RangeValue, LowerBoundLabel, UpperBoundLabel
            FROM L2BenchmarkIndicators
            """
            with self.db as db:
                cursor = db.connection.cursor()
                cursor.execute(query)
                rows = cursor.fetchall()

            if rows:
                mapping = {}
                for row in rows:
                    indicator_name_en = row[0]
                    indicator_name_cn = row[1]
                    formula = row[2]
                    formula_desc = row[3]
                    lower_bound = row[4]
                    upper_bound = row[5]
                    range_value = row[6]
                    lower_bound_label = row[7] if len(row) > 7 else None
                    upper_bound_label = row[8] if len(row) > 8 else None

                    if indicator_name_en:
                        mapping[indicator_name_en] = {
                            'wind_code': indicator_name_en,
                            
                            'formula': formula,
                            'formula_desc': formula_desc,
                            'lower_bound': lower_bound,
                            'upper_bound': upper_bound,
                            'range_value': range_value,
                            'lower_bound_label': lower_bound_label,
                            'upper_bound_label': upper_bound_label
                        }

                # 使用指标定义.txt中的对照关系补充映射
                # self._enrich_mapping_with_definitions(mapping)
                print(f"从数据库加载了 {len(mapping)} 个指标映射")
                return mapping
            else:
                # 如果数据库中没有数据，则从CSV文件加载
                print("数据库中没有指标映射数据，尝试从CSV文件加载")
                return self._load_mapping_from_csv()
        except Exception as e:
            print(f"从数据库加载指标映射失败: {str(e)}，尝试从CSV文件加载")
            #return self._load_mapping_from_csv()

    def _enrich_mapping_with_definitions(self, mapping):
        """使用指标定义.txt中的对照关系补充映射"""
        for cn_name, info in list(mapping.items()):
            # 如果映射中的英文代码在指标定义中有对应的中文名称，但与当前中文名称不同
            if info['wind_code'] in self.indicator_definitions:
                txt_cn_name = self.indicator_definitions[info['wind_code']]
                if txt_cn_name != cn_name:
                    # 添加一个新的映射条目，使用指标定义.txt中的中文名称
                    mapping[txt_cn_name] = info.copy()
                    print(f"补充映射: {info['wind_code']} -> {txt_cn_name}")

    def _load_mapping_from_csv(self):
        """从CSV文件加载指标映射"""
        try:
            # 注意：确保CSV文件路径相对于项目根目录是正确的
            df = pd.read_csv('docs/指标名称映射.csv', encoding='utf-8')
            mapping = {}
            for _, row in df.iterrows():
                if pd.notna(row['中文指标名称']) and pd.notna(row['英文指标名称']):
                    mapping[row['中文指标名称']] = {
                        'wind_code': row['英文指标名称'],
                        'formula': row['英文公式'] if pd.notna(row['英文公式']) else None,
                        'lower_bound': row['LowerBound'] if pd.notna(row['LowerBound']) else None,
                        'upper_bound': row['UpperBound'] if pd.notna(row['UpperBound']) else None,
                        'range_value': row['RangeValue'] if pd.notna(row['RangeValue']) else None
                    }

            # 使用指标定义.txt中的对照关系补充映射
            self._enrich_mapping_with_definitions(mapping)
            print(f"从CSV文件加载了 {len(mapping)} 个指标映射")
            return mapping
        except FileNotFoundError:
            print(f"错误: CSV文件 'docs/指标名称映射.csv' 未找到。")
            return {}
        except Exception as e:
            print(f"从CSV文件加载指标映射也失败: {str(e)}")
            return {}

    def get_metric_formulas_from_db(self):
        """从L2BenchmarkIndicators表获取指标公式"""
        query = """
        SELECT IndicatorNameCN, IndicatorNameEN, IndicatorDesc, Formula, FormulaDesc, LowerBound, UpperBound, RangeValue, LowerBoundLabel, UpperBoundLabel
        FROM L2BenchmarkIndicators 
         --Where IndicatorNameEN='revenue_per_rd_personnel'
        """
        try:
            with self.db as db:
                cursor = db.connection.cursor()
                cursor.execute(query)
                rows = cursor.fetchall()

            return {
                row[1]: {
                     'indicator_name_cn': row[0],
                    'indicator_name_en': row[1],
                    'formula': row[3],  # Formula字段
                    'formula_desc': row[4],  # FormulaDesc字段
                    'indicator_desc': row[2],  # IndicatorDesc字段
                    'lower_bound': row[5],
                    'upper_bound': row[6],
                    'range_value': row[7],
                    'lower_bound_label': row[8] if len(row) > 8 else None,
                    'upper_bound_label': row[9] if len(row) > 9 else None
                } for row in rows if row[0] # Ensure IndicatorNameCN is not null
            }
        except Exception as e:
            print(f"从数据库获取指标公式时出错: {str(e)}")
            return {}

    def get_all_benchmark_indicators(self):
        """
        一次性获取所有指标定义信息

        返回:
            dict: 包含指标定义信息的字典，以指标英文名称和中文名称为键
        """
        try:
            with self.db as db:
                cursor = db.connection.cursor()
                cursor.execute("""
                SELECT IndicatorNameEN, IndicatorNameCN, IndicatorDesc, Formula, FormulaDesc,
                       LowerBound, UpperBound, RangeValue, Category, LowerBoundLabel, UpperBoundLabel
                FROM L2BenchmarkIndicators
                """)
                rows = cursor.fetchall()

                if not rows:
                    print("L2BenchmarkIndicators表中没有数据")
                    return {}

                # 创建以指标英文名称和中文名称为键的字典
                indicators = {}
                for row in rows:
                    indicator_name_en = row[0]
                    indicator_name_cn = row[1]

                    indicator_info = {
                        'indicator_name_en': indicator_name_en,
                        'indicator_name_cn': indicator_name_cn,
                        'indicator_desc': row[2],
                        'formula': row[3],
                        'formula_desc': row[4],
                        'lower_bound': row[5],
                        'upper_bound': row[6],
                        'range_value': row[7],
                        'category': row[8],
                        'lower_bound_label': row[9] if len(row) > 9 else None,
                        'upper_bound_label': row[10] if len(row) > 10 else None
                    }

                    # 同时以英文名称和中文名称为键存储
                    if indicator_name_en:
                        indicators[indicator_name_en] = indicator_info
                    if indicator_name_cn:
                        indicators[indicator_name_cn] = indicator_info

                return indicators
        except Exception as e:
            print(f"从L2BenchmarkIndicators表获取指标定义信息时出错: {str(e)}")
            return {}
