#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试配置文件功能
"""

# 导入配置文件
try:
    from import_config import START_YEAR, END_YEAR, QUARTERS
    print("✅ 配置文件导入成功")
    print(f"   开始年份: {START_YEAR}")
    print(f"   结束年份: {END_YEAR}")
    print(f"   季度列表: {QUARTERS}")
except ImportError as e:
    print(f"❌ 配置文件导入失败: {e}")
    # 使用默认值
    START_YEAR = 2024
    END_YEAR = 2025
    QUARTERS = ['0331', '0630', '0930', '1231']
    print("   使用默认配置:")
    print(f"   开始年份: {START_YEAR}")
    print(f"   结束年份: {END_YEAR}")
    print(f"   季度列表: {QUARTERS}")

# 测试日期生成逻辑
def test_date_generation():
    """测试日期生成逻辑"""
    print("\n📅 测试日期生成:")
    all_dates = []
    for year in range(START_YEAR, END_YEAR + 1):
        for quarter in QUARTERS:
            date_str = f"{year}{quarter}"
            all_dates.append(date_str)
    
    print(f"   生成的日期数量: {len(all_dates)}")
    print(f"   日期范围: {all_dates[0]} 到 {all_dates[-1]}")
    print(f"   前5个日期: {all_dates[:5]}")
    print(f"   后5个日期: {all_dates[-5:]}")

# 测试配置验证
def test_config_validation():
    """测试配置验证"""
    print("\n🔍 配置验证:")
    
    # 检查年份范围
    if START_YEAR > END_YEAR:
        print("   ❌ 错误: 开始年份不能大于结束年份")
    else:
        print("   ✅ 年份范围正确")
    
    # 检查季度格式
    valid_quarters = ['0331', '0630', '0930', '1231']
    invalid_quarters = [q for q in QUARTERS if q not in valid_quarters]
    if invalid_quarters:
        print(f"   ❌ 错误: 无效的季度格式: {invalid_quarters}")
    else:
        print("   ✅ 季度格式正确")
    
    # 检查季度是否为空
    if not QUARTERS:
        print("   ❌ 错误: 季度列表不能为空")
    else:
        print("   ✅ 季度列表不为空")

if __name__ == "__main__":
    print("=" * 50)
    print("Wind数据导入配置测试")
    print("=" * 50)
    
    test_config_validation()
    test_date_generation()
    
    print("\n" + "=" * 50)
    print("测试完成")
    print("=" * 50) 