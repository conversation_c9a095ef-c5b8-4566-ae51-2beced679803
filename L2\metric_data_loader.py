# L2/metric_data_loader.py
from database.db_handler import DatabaseHandler
from datetime import datetime
from decimal import Decimal

class MetricDataLoader:
    """
    负责从数据库加载指标计算所需的数据。
    包括公司名称、期间处理和指标值。
    """
    def __init__(self):
        self.db = DatabaseHandler()

    def _get_quarter_number(self, date_obj):
        """
        根据日期计算季度数
        参数:
            date_obj (datetime): 日期对象
        返回:
            int: 季度数 (1-4)，分别对应一季度、半年度、三季度、年度
                 对应月份: 3/6/9/12
        """
        month = date_obj.month
        if month == 3:
            return 1  # 一季度
        elif month == 6:
            return 2  # 半年度
        elif month == 9:
            return 3  # 三季度
        elif month == 12:
            return 4  # 年度
        else:
            # 对于非标准月份，根据月份范围推断
            if month <= 3:
                return 1
            elif month <= 6:
                return 2
            elif month <= 9:
                return 3
            else:
                return 4

    def get_company_name(self, stock_code):
        """根据股票代码获取公司名称"""
        query = """
        SELECT CompanyName
        FROM CompanyInfo
        WHERE TickerSymbol=?
        """
        try:
            with self.db as db:
                cursor = db.connection.cursor()
                cursor.execute(query, (stock_code,))
                row = cursor.fetchone()
            return row[0] if row else stock_code
        except Exception as e:
            print(f"获取公司名称时出错 (股票代码: {stock_code}): {str(e)}")
            return stock_code # 返回股票代码作为备用

    def _get_previous_period(self, date_obj):
        """
        获取上期日期
        对于年报（12-31），上期为去年同期
        对于季报（03-31, 06-30, 09-30），上期为去年同期
        """
        year = date_obj.year
        month = date_obj.month
        day = date_obj.day
        # 上期日期（去年同期）
        previous_date = datetime(year - 1, month, day)
        return previous_date

    def _get_previous_end_period(self, date_obj):
        """
        获取上期末日期
        对于年报（12-31），上期末为去年年末
        对于一季报（03-31），上期末为去年年末
        对于中报（06-30），上期末为去年年末
        对于三季报（09-30），上期末为去年年末
        """
        year = date_obj.year
        # 上期末日期（去年年末）
        previous_end_date = datetime(year - 1, 12, 31)
        return previous_end_date

    def get_metrics_data(self, stock_code, date_obj):
        """
        从数据库获取指标数据，包括当期、上期和上期末的数据

        参数:
            stock_code (str): 股票代码
            date_obj (datetime): 当期日期

        返回:
            tuple: (当期数据, 上期数据, 上期末数据)
                   如果找不到当期数据，返回 (None, None, None)
        """
        # 获取上期和上期末日期
        previous_date = self._get_previous_period(date_obj)
        previous_end_date = self._get_previous_end_period(date_obj)

        # 格式化日期为字符串
        date_str = date_obj.strftime('%Y%m%d')
        previous_date_str = previous_date.strftime('%Y%m%d')
        previous_end_date_str = previous_end_date.strftime('%Y%m%d')

        print(f"数据加载 - 当期日期: {date_str}, 上期日期: {previous_date_str}, 上期末日期: {previous_end_date_str}")

        # 查询语句
        current_query = "SELECT WindCode,ISNULL( Value,0), YoYValue FROM WindMetrics WHERE TickerSymbol=? AND Date=?"
        previous_query = "SELECT WindCode, ISNULL( Value,0) FROM WindMetrics WHERE TickerSymbol=? AND Date=?" # 上期不直接用YoY
        previous_end_query = "SELECT WindCode, ISNULL( Value,0) FROM WindMetrics WHERE TickerSymbol=? AND Date=?" # 上期末不直接用YoY

        current_values = {}
        previous_values = {}
        previous_end_values = {}
        previous_from_yoy = set()
        previous_end_from_yoy = set()

        try:
            with self.db as db:
                cursor = db.connection.cursor()

                # 获取当期数据，并尝试填充上期/上期末数据（来自YoYValue）
                cursor.execute(current_query, (stock_code, date_str))
                current_rows = cursor.fetchall()

                if not current_rows:
                    print(f"警告: 未找到股票 {stock_code} 在 {date_str} 的当期数据。")
                    return None, None, None # 如果没有当期数据，直接返回

                for row in current_rows:
                    wind_code, value, yoy_value = row[0], row[1], row[2] if len(row) > 2 else None
                    wind_code_lower = wind_code.lower() if wind_code else None

                    # 处理当期值
                    try:
                        if value is not None and str(value).strip():
                            current_values[wind_code] = float(value)
                            # 同时以小写形式存储，以便不区分大小写查找
                            if wind_code_lower:
                                current_values[wind_code_lower] = float(value)
                    except (ValueError, TypeError):
                        print(f"警告: 无法转换当期值 '{value}' (指标: {wind_code}) 为数值，跳过。")

                    # 处理同比值 (优先用于上期数据)
                    if yoy_value is not None and str(yoy_value).strip():
                        try:
                            numeric_yoy_value = float(yoy_value)
                            previous_values[wind_code] = numeric_yoy_value
                            # 同时以小写形式存储，以便不区分大小写查找
                            if wind_code_lower:
                                previous_values[wind_code_lower] = numeric_yoy_value
                            previous_from_yoy.add(wind_code)
                            if wind_code_lower:
                                previous_from_yoy.add(wind_code_lower)

                            # 如果是年报，同比值也用于上期末
                            if date_obj.month == 12:
                                previous_end_values[wind_code] = numeric_yoy_value
                                # 同时以小写形式存储，以便不区分大小写查找
                                if wind_code_lower:
                                    previous_end_values[wind_code_lower] = numeric_yoy_value
                                previous_end_from_yoy.add(wind_code)
                                if wind_code_lower:
                                    previous_end_from_yoy.add(wind_code_lower)
                        except (ValueError, TypeError):
                             print(f"警告: 无法转换同比值 '{yoy_value}' (指标: {wind_code}) 为数值，跳过。")


                # 获取上期数据（仅获取那些没有从YoYValue获取的指标）
                cursor.execute(previous_query, (stock_code, previous_date_str))
                for row in cursor.fetchall():
                    wind_code, value = row[0], row[1]
                    wind_code_lower = wind_code.lower() if wind_code else None

                    # 检查原始代码和小写形式是否已经从YoYValue获取
                    if wind_code not in previous_from_yoy and (not wind_code_lower or wind_code_lower not in previous_from_yoy):
                        try:
                            if value is not None and str(value).strip():
                                previous_values[wind_code] = float(value)
                                # 同时以小写形式存储，以便不区分大小写查找
                                if wind_code_lower:
                                    previous_values[wind_code_lower] = float(value)
                        except (ValueError, TypeError):
                            print(f"警告: 无法转换上期值 '{value}' (指标: {wind_code}) 为数值，跳过。")

                # 获取上期末数据（仅获取那些没有从YoYValue获取的指标）
                cursor.execute(previous_end_query, (stock_code, previous_end_date_str))
                for row in cursor.fetchall():
                    wind_code, value = row[0], row[1]
                    wind_code_lower = wind_code.lower() if wind_code else None

                    # 检查原始代码和小写形式是否已经从YoYValue获取
                    if wind_code not in previous_end_from_yoy and (not wind_code_lower or wind_code_lower not in previous_end_from_yoy):
                        try:
                            if value is not None and str(value).strip():
                                previous_end_values[wind_code] = float(value)
                                # 同时以小写形式存储，以便不区分大小写查找
                                if wind_code_lower:
                                    previous_end_values[wind_code_lower] = float(value)
                        except (ValueError, TypeError):
                            print(f"警告: 无法转换上期末值 '{value}' (指标: {wind_code}) 为数值，跳过。")

            # 添加季度数据到各期数据中
            current_quarter = self._get_quarter_number(date_obj)
            previous_quarter = self._get_quarter_number(previous_date)
            previous_end_quarter = self._get_quarter_number(previous_end_date)
            
            # 添加季度指标到数据字典中
            current_values['QUARTER'] = current_quarter
            current_values['quarter'] = current_quarter  # 小写版本
            previous_values['QUARTER'] = previous_quarter
            previous_values['quarter'] = previous_quarter  # 小写版本
            previous_end_values['QUARTER'] = previous_end_quarter
            previous_end_values['quarter'] = previous_end_quarter  # 小写版本
            
            print(f"季度信息已添加 - 当期季度: {current_quarter}, 上期季度: {previous_quarter}, 上期末季度: {previous_end_quarter}")

            print(f"数据加载完成 - 当期: {len(current_values)}, 上期: {len(previous_values)} (来自YoY: {len(previous_from_yoy)}), 上期末: {len(previous_end_values)} (来自YoY: {len(previous_end_from_yoy)})")

            if len(current_values) < 10:
                 print(f"警告: 股票 {stock_code} 在 {date_str} 的当期数据量 ({len(current_values)}) 过少，可能影响计算准确性。")

            return current_values, previous_values, previous_end_values

        except Exception as e:
            print(f"获取指标数据时出错 (股票代码: {stock_code}, 日期: {date_str}): {str(e)}")
            return None, None, None # 出错时返回 None

    def get_all_active_stock_codes(self):
        """获取所有活跃的股票代码"""
        query = """
        SELECT TickerSymbol
        FROM CompanyInfo
        WHERE IsActive = '1'  -- 假设有一个Status字段标记活跃状态，如果不同请修改
        ORDER BY TickerSymbol
        """
        try:
            with self.db as db:
                cursor = db.connection.cursor()
                cursor.execute(query)
                rows = cursor.fetchall()
            return [row[0] for row in rows] if rows else []
        except Exception as e:
            print(f"获取所有活跃股票代码时出错: {str(e)}")
            return []
