import React from 'react';
import { Select, Button, Tag, Checkbox, Space, Divider, Typography, Row, Col } from 'antd';

const { Option } = Select;
const { Text } = Typography;

/**
 * 筛选面板组件
 *
 * @param {Object} props
 * @param {Array} props.years - 年份列表
 * @param {number} props.selectedYear - 选中的年份
 * @param {Array} props.quarters - 季度列表
 * @param {string} props.selectedQuarter - 选中的季度
 * @param {string} props.tableType - 表格类型
 * @param {Array} props.metrics - 指标列表
 * @param {Array} props.selectedMetrics - 选中的指标
 * @param {Array} props.companies - 公司列表
 * @param {Array} props.selectedCompanies - 选中的公司
 * @param {string} props.dataUnit - 数据单位
 * @param {string} props.companyType - 公司类型筛选
 * @param {Function} props.onDateChange - 日期变更处理函数
 * @param {Function} props.onTableTypeChange - 表格类型变更处理函数
 * @param {Function} props.onMetricChange - 指标变更处理函数
 * @param {Function} props.onCompanyChange - 公司变更处理函数
 * @param {Function} props.onDataUnitChange - 数据单位变更处理函数
 * @param {Function} props.onCompanyTypeChange - 公司类型筛选变更处理函数
 * @param {Function} props.onSearch - 检索按钮点击处理函数
 * @param {Function} props.onExport - 导出按钮点击处理函数
 * @param {boolean} props.exportDisabled - 导出按钮是否禁用
 * @param {boolean} props.loading - 是否正在加载数据
 * @returns {JSX.Element}
 */
const FilterPanel = ({
  years,
  selectedYear,
  quarters,
  selectedQuarter,
  tableType,
  metrics,
  selectedMetrics,
  companies,
  selectedCompanies,
  dataUnit,
  companyType,
  onDateChange,
  onTableTypeChange,
  onMetricChange,
  onCompanyChange,
  onDataUnitChange,
  onCompanyTypeChange,
  onSearch,
  onExport,
  exportDisabled,
  loading
}) => {
  // 处理指标选择
  const handleMetricChange = (metricCode) => {
    let newSelectedMetrics;
    if (selectedMetrics.includes(metricCode)) {
      newSelectedMetrics = selectedMetrics.filter(code => code !== metricCode);
    } else {
      newSelectedMetrics = [...selectedMetrics, metricCode];
    }
    onMetricChange(newSelectedMetrics);
  };

  // 处理公司选择
  const handleCompanyChange = (companyId) => {
    let newSelectedCompanies;
    if (selectedCompanies.includes(companyId)) {
      newSelectedCompanies = selectedCompanies.filter(id => id !== companyId);
    } else {
      newSelectedCompanies = [...selectedCompanies, companyId];
    }
    onCompanyChange(newSelectedCompanies);
  };

  // 全选/取消全选指标
  const handleSelectAllMetrics = (checked) => {
    if (checked) {
      onMetricChange(metrics.map(metric => metric.code));
    } else {
      onMetricChange([]);
    }
  };

  // 全选/取消全选公司
  const handleSelectAllCompanies = (checked) => {
    const filteredCompanies = companyType === "all"
      ? companies
      : companyType === "internal"
        ? companies.filter(company => company.IsInternal === true)
        : companies.filter(company => company.IsInternal !== true);

    if (checked) {
      onCompanyChange(filteredCompanies.map(company => company.id));
    } else {
      onCompanyChange([]);
    }
  };

  // 获取当前可选择的公司列表
  const getAvailableCompanies = () => {
    switch (companyType) {
      case "internal":
        return companies.filter(company => company.IsInternal === true);
      case "external":
        return companies.filter(company => company.IsInternal !== true);
      default:
        return companies;
    }
  };

  const availableCompanies = getAvailableCompanies();

  return (
    <div className="selectors">
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', width: '100%', marginBottom: 10 }}>
        <div>
          <Select
            value={selectedYear}
            onChange={(value) => onDateChange(value, "year")}
            style={{ width: 120, marginRight: 16 }}
          >
            {years.map((year) => (
              <Option key={year} value={year}>
                {year}年
              </Option>
            ))}
          </Select>

          <Select
            value={selectedQuarter}
            onChange={(value) => onDateChange(value, "quarter")}
            style={{ width: 140, marginRight: 16 }}
          >
            {quarters.map((q) => (
              <Option key={q.value} value={q.value}>
                {q.label}
              </Option>
            ))}
          </Select>

          <Select
            value={tableType}
            onChange={onTableTypeChange}
            style={{ width: 140, marginRight: 16 }}
          >
            <Option value="quantitative">一利五率</Option>
            <Option value="quantitative_assessment">定量评估表</Option>
            <Option value="custom">自定义</Option>
          </Select>

          {/* 数据单位选择器 */}
          <Select
            value={dataUnit}
            onChange={onDataUnitChange}
            style={{ width: 120, marginRight: 16 }}
          >
            <Option value="元">元</Option>
            <Option value="万元">万元</Option>
            <Option value="亿元">亿元</Option>
          </Select>

          {/* 公司类型筛选器 */}
          <Select
            value={companyType}
            onChange={onCompanyTypeChange}
            style={{ width: 140, marginRight: 16 }}
          >
            <Option value="all">全部公司</Option>
            <Option value="internal">集团内公司</Option>
            <Option value="external">集团外公司</Option>
          </Select>
        </div>

        <div>
          <Button
            type="primary"
            onClick={onSearch}
            loading={loading}
            style={{ marginRight: 8 }}
          >
            检索
          </Button>

          <Button
            onClick={onExport}
            disabled={exportDisabled}
          >
            导出Excel
          </Button>
        </div>
      </div>

      {/* 指标选择区域 */}
      {tableType === "custom" && (
        <div style={{ marginBottom: 12 }}>
          <div style={{
            
          }}>
         
            <div style={{ display: 'flex', flexWrap: 'wrap', gap: '3px',alignItems: 'center' }}>
            <Checkbox
                checked={selectedMetrics.length === metrics.length && metrics.length > 0}
                indeterminate={selectedMetrics.length > 0 && selectedMetrics.length < metrics.length}
                onChange={(e) => handleSelectAllMetrics(e.target.checked)}
                size="small"
              >
                全选   </Checkbox>
              <Text type="secondary" style={{ marginLeft: 0, fontSize: '12px' }}>
                ({selectedMetrics.length}/{metrics.length})
              </Text> {metrics.map((metric) => (
                <Tag
                  key={metric.code}
                  color={selectedMetrics.includes(metric.code) ? 'blue' : 'default'}
                  style={{
                    cursor: 'pointer',
                    marginRight: '2px',
                    padding: '4px 6px', 
                    border: selectedMetrics.includes(metric.code) ? '1px solid #1890ff' : '1px solid #d9d9d9'
                  }}
                  onClick={() => handleMetricChange(metric.code)}
                >
                  {metric.name}
                </Tag>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* 公司选择区域 - 集团内和集团外公司一行显示 */}
      <div style={{ marginBottom: 0 }}>
        <div style={{
        
        }}>
      

          {companyType === "all" && (
            <Row gutter={8}>
              {/* 集团内公司 */}
              <Col span={24}>

                <div style={{ display: 'flex', flexWrap: 'wrap', alignItems: 'center' }}>
                  <Checkbox
                    checked={selectedCompanies.length === availableCompanies.length && availableCompanies.length > 0}
                    indeterminate={selectedCompanies.length > 0 && selectedCompanies.length < availableCompanies.length}
                    onChange={(e) => handleSelectAllCompanies(e.target.checked)}
                    size="small"
                  >
                    全选
                  </Checkbox>
                  <Text type="secondary" style={{ marginRight: 5 }}>
                    ({selectedCompanies.length}/{availableCompanies.length})
                  </Text>
                  {companies
                    .filter((company) => company.IsInternal === true)
                    .map((company) => (
                      <Tag
                        key={company.id}
                        color={selectedCompanies.includes(company.id) ? 'green' : 'default'}
                        style={{
                          cursor: 'pointer',
                          margin: '2px',
                          padding: '4px 6px', 
                          border: selectedCompanies.includes(company.id) ? '1px solid #52c41a' : '1px solid #d9d9d9'
                        }}
                        onClick={() => handleCompanyChange(company.id)}
                      >
                        {company.name}
                      </Tag>
                    ))}
                  {companies
                    .filter((company) => company.IsInternal !== true)
                    .map((company) => (
                      <Tag
                        key={company.id}
                        color={selectedCompanies.includes(company.id) ? 'orange' : 'default'}
                        style={{
                          cursor: 'pointer',
                          margin: '2px',
                          padding: '4px 6px',  
                          border: selectedCompanies.includes(company.id) ? '1px solid #fa8c16' : '1px solid #d9d9d9'
                        }}
                        onClick={() => handleCompanyChange(company.id)}
                      >
                        {company.name}
                      </Tag>
                    ))}
                </div>
              </Col>
            </Row>
          )}

          {companyType === "internal" && (
            <div style={{ display: 'flex', flexWrap: 'wrap', gap: '2px' }}>
              {companies
                .filter((company) => company.IsInternal === true)
                .map((company) => (
                  <Tag
                    key={company.id}
                    color={selectedCompanies.includes(company.id) ? 'green' : 'default'}
                    style={{
                      cursor: 'pointer',
                      margin: '0',
                      padding: '2px 5px', 
                      border: selectedCompanies.includes(company.id) ? '1px solid #52c41a' : '1px solid #d9d9d9'
                    }}
                    onClick={() => handleCompanyChange(company.id)}
                  >
                    {company.name}
                  </Tag>
                ))}
            </div>
          )}

          {companyType === "external" && (
            <div style={{ display: 'flex', flexWrap: 'wrap', gap: '2px' }}>
              {companies
                .filter((company) => company.IsInternal !== true)
                .map((company) => (
                  <Tag
                    key={company.id}
                    color={selectedCompanies.includes(company.id) ? 'orange' : 'default'}
                    style={{
                      cursor: 'pointer',
                      margin: '0',
                      padding: '2px 5px', 
                      border: selectedCompanies.includes(company.id) ? '1px solid #fa8c16' : '1px solid #d9d9d9'
                    }}
                    onClick={() => handleCompanyChange(company.id)}
                  >
                    {company.name}
                  </Tag>
                ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default FilterPanel;
