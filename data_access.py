import pyodbc
import threading
from flask import current_app
from queue import Queue, Empty
from .utils.error_handler import log_error, DatabaseError

# 使用简单的连接池实现
class ConnectionPool:
    def __init__(self, max_connections=5):
        self.max_connections = max_connections
        self.connections = Queue(maxsize=max_connections)
        self.created_connections = 0
        self._lock = threading.Lock()
    
    def get_connection(self, conn_str):
        try:
            # 尝试从池中获取连接
            return self.connections.get(block=False)
        except Empty:
            # 如果池为空，创建新连接（如果未达到最大连接数）
            with self._lock:
                if self.created_connections < self.max_connections:
                    conn = pyodbc.connect(conn_str)
                    self.created_connections += 1
                    return conn
                else:
                    # 如果达到最大连接数，等待可用连接
                    return self.connections.get(block=True, timeout=30)
    
    def return_connection(self, conn):
        # 如果连接有效，放回池中
        if conn and not conn.closed:
            self.connections.put(conn)

# 初始化连接池
_pool = ConnectionPool(max_connections=10)

def get_db_config():
    """获取当前应用的数据库配置"""
    return current_app.config['DB_CONFIG']

def get_connection_string():
    """构建连接字符串"""
    db_config = get_db_config()
    return (
        f"DRIVER={{{db_config['driver']}}};"
        f"SERVER={db_config['server']};"
        f"DATABASE={db_config['database']};"
        f"UID={db_config['user']};" 
        f"PWD={db_config['password']};" 
        f"Encrypt=no;"
     
    )

def get_connection():
    """从连接池获取数据库连接"""
    try:
        conn_str = get_connection_string()
        return _pool.get_connection(conn_str)
    except Exception as e:
        log_error(e, "获取数据库连接失败")
        raise DatabaseError("数据库连接失败")

def return_connection(conn):
    """将连接返回到连接池"""
    _pool.return_connection(conn)

def execute_query(query, params=None):
    """执行只读查询"""
    conn = None
    cursor = None
    try:
        conn = get_connection()
        cursor = conn.cursor()
        if params:
            cursor.execute(query, params)
        else:
            cursor.execute(query)
        columns = [column[0] for column in cursor.description]
        results = []
        for row in cursor.fetchall():  # 确保获取所有结果
            results.append(dict(zip(columns, row)))
        return results
    except pyodbc.Error as e:
        log_error(e, "数据库查询错误", {"query": query, "params": params})
        raise DatabaseError("数据库查询失败")
    finally:
        if cursor:
            cursor.close()
        if conn:
            return_connection(conn)

def execute_update(query, params=None):
    """执行插入、更新或删除操作"""
    conn = None
    cursor = None
    try:
        conn = get_connection()
        cursor = conn.cursor()
        if params:
            cursor.execute(query, params)
        else:
            cursor.execute(query)
        conn.commit()
        return cursor.rowcount
    except pyodbc.Error as e:
        log_error(e, "数据库更新错误", {"query": query, "params": params})
        if conn:
            conn.rollback()
        raise DatabaseError("数据库更新失败")
    finally:
        if cursor:
            cursor.close()
        if conn:
            return_connection(conn)