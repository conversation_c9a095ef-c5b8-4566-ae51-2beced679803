from WindPy import w
from database.db_handler import DatabaseHandler
from logging_config import setup_logging
import pandas as pd

logger = setup_logging()

def check_indicators():
    """检查Wind指标的有效性"""
    db = DatabaseHandler()
    w.start()
    
    # 查询指标定义
    select_metrics_query = "SELECT Name, WindCode FROM MetricsDefinition"
    try:
        with db as db:
            cursor = db.connection.cursor()
            cursor.execute(select_metrics_query)
            metrics = cursor.fetchall()
            logger.info(f"成功获取 {len(metrics)} 条指标定义")
    except Exception as e:
        logger.error(f"获取指标定义失败: {str(e)}", exc_info=True)
        return
    
    # 获取一个示例股票代码用于测试
    sample_stock_query = "SELECT TOP 1 TickerSymbol FROM CompanyInfo"
    try:
        with db as db:
            cursor = db.connection.cursor()
            cursor.execute(sample_stock_query)
            sample_stock = cursor.fetchone()[0]
    except Exception as e:
        logger.error(f"获取示例股票代码失败: {str(e)}", exc_info=True)
        return

    invalid_indicators = []
    valid_indicators = []
    
    # 逐个检查指标
    for name, wind_code in metrics:
        logger.info(f"正在检查指标: {name} ({wind_code})")
        
        result = w.wss(
            sample_stock,
            wind_code,
            "unit=1;rptDate=20240331;rptType=1;tradeDate=20240331"
        )
        
        if result.ErrorCode == -40522006:
            invalid_indicators.append((name, wind_code))
            logger.warning(f"无效指标: {name} ({wind_code})")
        elif result.ErrorCode != 0:
            logger.warning(f"指标 {name} ({wind_code}) 返回错误: {result.ErrorCode}")
        else:
            valid_indicators.append((name, wind_code))
            logger.info(f"有效指标: {name} ({wind_code})")
    
    # 输出结果
    logger.info("=== 检查结果汇总 ===")
    logger.info(f"总计检查指标: {len(metrics)}")
    logger.info(f"有效指标数量: {len(valid_indicators)}")
    logger.info(f"无效指标数量: {len(invalid_indicators)}")
    
    if invalid_indicators:
        logger.error("无效指标列表:")
        for name, code in invalid_indicators:
            logger.error(f"- {name} ({code})")
        
        # 将无效指标保存到文件
        with open('invalid_indicators.txt', 'w', encoding='utf-8') as f:
            f.write("指标名称,Wind代码\n")
            for name, code in invalid_indicators:
                f.write(f"{name},{code}\n")
        logger.info("无效指标列表已保存到 invalid_indicators.txt")

if __name__ == "__main__":
    check_indicators()