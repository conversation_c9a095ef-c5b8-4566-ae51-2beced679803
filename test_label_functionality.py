#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试lower_bound_label和upper_bound_label功能
"""

from metrics_calculator import print_results

# 创建测试数据
test_data = {
    'stock_code': '000001',
    'company_name': '平安银行',
    'report_date': '2023-12-31',
    'statistics': {
        'success_count': 3,
        'failure_count': 0,
        'execution_time': 0.123,
        'error_logs': []
    },
    'metrics': {
        '毛利率': {
            'indicator_name_en': 'gross_margin',
            'value': 0.25,
            'lower_bound': 0.20,
            'upper_bound': 0.30,
            'lower_bound_label': '较差',
            'upper_bound_label': '良好',
            'error': None
        },
        '净利率': {
            'indicator_name_en': 'net_profit_margin',
            'value': 0.02,
            'lower_bound': 0.01,
            'upper_bound': 0.05,
            'lower_bound_label': None,  # 测试默认标签
            'upper_bound_label': None,  # 测试默认标签
            'error': None
        },
        '净资产收益率': {
            'indicator_name_en': 'roe',
            'value': -0.02,
            'lower_bound': 0.05,
            'upper_bound': 0.15,
            'lower_bound_label': '表现不佳',
            'upper_bound_label': '表现优异',
            'error': None
        }
    }
}

print("=== 测试lower_bound_label和upper_bound_label功能 ===")
print("\n测试场景：")
print("1. gross_margin (0.25): 在范围内，应显示'良好'")
print("2. net_profit_margin (0.02): 在范围内，无自定义标签，应显示'良好'")
print("3. roe (-0.02): 低于下限，有自定义标签，应显示'表现不佳'")
print("\n" + "="*50)

# 调用print_results函数进行测试
print_results(test_data, test_data['stock_code'])

print("\n" + "="*50)
print("测试完成！请检查上面的输出是否正确显示了自定义标签。")