import sqlite3
import pyodbc
from ..config.database_config import get_db_connection

class IndicatorRepository:
    def __init__(self):
        self.connection = get_db_connection()

    def get_all_indicators(self): # 或者更名为 get_all_benchmark_definitions
        cursor = self.connection.cursor()
        # 返回更完整的指标定义和通用基准信息
        # 根据 L2BenchmarkIndicators 表结构选择需要的字段
        query = '''
            SELECT 
                IndicatorNameCN, 
                IndicatorNameEN, 
                IndicatorDesc, 
                FormulaDesc, 
                Formula, 
                LowerBound, 
                UpperBound, 
                RangeValue, 
                Category ,
                ValueType,Sort
            FROM L2BenchmarkIndicators 
            WHERE IsActive = 1 
            ORDER BY Sort
        '''
        # 注意：原先的 DISTINCT 可能不再适用，或者需要根据实际情况决定如何处理重复（如果存在）
        # 如果 IndicatorNameEN 是唯一的，则不需要 DISTINCT
        # 假设 IndicatorNameEN 在 L2BenchmarkIndicators 中是唯一的，或者我们取 IsActive=1 的最新/默认条目
        # 这里暂时移除了 DISTINCT，如果需要，可以基于 IndicatorNameEN 进行分组或去重处理
        cursor.execute(query)
        
        # 将结果转换为字典列表，方便前端使用
        columns = [column[0] for column in cursor.description]
        results_as_dict = []
        for row in cursor.fetchall():
            results_as_dict.append(dict(zip(columns, row)))
        
        return results_as_dict
