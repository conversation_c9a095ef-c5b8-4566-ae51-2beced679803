import React from "react";
import { Table } from "antd";
import MetricTooltip, { MetricHeaderTooltip } from "./MetricTooltip";
import { formatMetricValue } from "./utils";

import { useState, useEffect, useMemo, useCallback } from "react";

/**
 * 财务表格组件
 *
 * @param {Object} props
 * @param {Array} props.tableData - 表格数据
 * @param {Array} props.metrics - 指标列表
 * @param {Array} props.selectedMetrics - 选中的指标
 * @param {Array} props.selectedCompanies - 选中的公司
 * @param {string} props.dataUnit - 数据单位
 * @param {boolean} props.loading - 加载状态
 * @returns {JSX.Element}
 */
const FinancialTable = ({
  tableData,
  metrics,
  selectedMetrics,
  selectedCompanies,
  selectedMetricsObjects,
  dataUnit = "元",
  loading,
}) => {
  // 添加排序状态
  const [sortedInfo, setSortedInfo] = useState({
    columnKey: "index",
    order: "ascend",
  });

  // 当筛选条件变化时，重置排序状态
  // 移除自动重置排序的逻辑，保持用户的排序选择
  // useEffect(() => {
  //   setSortedInfo({
  //     columnKey: "index",
  //     order: "ascend",
  //   });
  // }, [selectedCompanies, selectedMetrics]);

  // 当表格数据变化时，重置排序状态
  useEffect(() => {
    setSortedInfo({
      columnKey: "index",
      order: "ascend",
    });
  }, [tableData]);

  // 处理表格变更事件
  const handleTableChange = (_, __, sorter) => {
    setSortedInfo(sorter);
  };
  // 直接使用父组件传递的预处理数据，无需重复过滤
  const filteredData = tableData;
  
  // 如果父组件没有传递selectedMetricsObjects，则进行兜底处理
  const metricsObjects = selectedMetricsObjects || useMemo(() => {
    return selectedMetrics
      .map((metricCode) => metrics.find((m) => m.code === metricCode))
      .filter((metric) => metric);
  }, [selectedMetrics, metrics]);

  // 使用useCallback优化数据单位转换函数
  const convertValueByUnit = useCallback((value, valueType) => {
    if (value === undefined || value === null || isNaN(value)) return value;

    const numValue = Number(value);

    // 如果是百分比，不需要转换单位
    if (valueType && valueType.trim() !== "元") {
      return numValue;
    }

    // 根据选择的单位进行转换
    if (dataUnit === "万元") {
      return numValue / 10000;
    } else if (dataUnit === "亿元") {
      return numValue / *********;
    }

    // 默认单位为"元"，不需要转换
    return numValue;
  }, [dataUnit]);

  // 计算最大值和最小值行
  const calculateMaxMinRows = useMemo(() => {
    const companyRows = filteredData.filter(item => !item.isRankRow);
    
    if (companyRows.length === 0) return { maxRow: null, minRow: null };

    // 创建最大值行
    const maxRow = {
      company_id: 'max_row',
      company_name: '',
      companyGroup: '',
      key: 'max_row',
      isMaxRow: true,
      isFixedRow: true,
      metric_values: {},
    };

    // 创建最小值行
    const minRow = {
      company_id: 'min_row',
      company_name: '',
      companyGroup: '',
      key: 'min_row',
      isMinRow: true,
      isFixedRow: true,
      metric_values: {},
    };

    // 对每个指标计算最大值和最小值
    metricsObjects.forEach(metric => {
      // 过滤掉无效值并转换单位
      const validCompanies = companyRows.filter(company => {
        const value = company.metric_values[metric.code]?.value;
        return value !== undefined && value !== null && !isNaN(value);
      }).map(company => {
        const originalValue = company.metric_values[metric.code]?.value;
        const convertedValue = convertValueByUnit(originalValue, metric.value_type);
        return {
          ...company,
          convertedValue,
          originalValue
        };
      });

      if (validCompanies.length > 0) {
        // 找到最大值公司（使用转换后的值进行比较）
        const maxCompany = validCompanies.reduce((max, current) => {
          return current.convertedValue > max.convertedValue ? current : max;
        });

        // 找到最小值公司（使用转换后的值进行比较）
        const minCompany = validCompanies.reduce((min, current) => {
          return current.convertedValue < min.convertedValue ? current : min;
        });

        const maxValue = maxCompany.originalValue;
        const minValue = minCompany.originalValue;

        // 格式化显示值
        const displayMaxValue = formatMetricValue(maxCompany.convertedValue, metric.value_type, dataUnit, metric);
        const displayMinValue = formatMetricValue(minCompany.convertedValue, metric.value_type, dataUnit, metric);

        // 设置最大值行
        maxRow.metric_values[metric.code] = {
          value: maxValue,
          company_name: maxCompany.company_name,
          display_value: displayMaxValue
        };

        // 设置最小值行
        minRow.metric_values[metric.code] = {
          value: minValue,
          company_name: minCompany.company_name,
          display_value: displayMinValue
        };
      } else {
        // 如果没有有效值，显示占位符
        maxRow.metric_values[metric.code] = {
          value: '-',
          company_name: '-',
          display_value: '-'
        };
        minRow.metric_values[metric.code] = {
          value: '-',
          company_name: '-',
          display_value: '-'
        };
      }
    });

    return { maxRow, minRow };
  }, [filteredData, metricsObjects, convertValueByUnit, dataUnit]);

  // 合并表格数据：排名行 + 最大值行 + 最小值行 + 公司数据行
  const finalTableData = useMemo(() => {
    const rankRow = filteredData.find(item => item.isRankRow);
    const companyRows = filteredData.filter(item => !item.isRankRow);
    const { maxRow, minRow } = calculateMaxMinRows;

    let result = [];
    
    // 添加排名行（如果存在）
    if (rankRow) {
      result.push(rankRow);
    }
    
    // 添加最大值行
    if (maxRow) {
      result.push(maxRow);
    }
    
    // 添加最小值行
    if (minRow) {
      result.push(minRow);
    }
    
    // 添加公司数据行
    result.push(...companyRows);
    
    return result;
  }, [filteredData, calculateMaxMinRows]);

  // 使用useMemo优化表格列配置，避免每次渲染都重新生成
  const columns = useMemo(() => [
    {
      title: "序号",
      dataIndex: "key",
      key: "index",
      fixed: "left",
      width: 80,
      render: (_, record, index) => {
        // 如果是排名行，显示"排名"
        if (record.isRankRow) return "排名";
        // 如果是最大值行，显示"最大值"
        if (record.isMaxRow) return "最大值";
        // 如果是最小值行，显示"最小值"
        if (record.isMinRow) return "最小值";

        // 否则显示序号（从1开始）
        return index;
      },
      sorter: (a, b) => {
        // 排名行始终排在最前面
        if (a.isRankRow) return -1;
        if (b.isRankRow) return 1;
        // 最大值行排在第二位
        if (a.isMaxRow) return -1;
        if (b.isMaxRow) return 1;
        // 最小值行排在第三位
        if (a.isMinRow) return -1;
        if (b.isMinRow) return 1;

        // 根据公司分组排序（集团内公司在前）
        if (a.isInternal !== b.isInternal) {
          return a.isInternal ? -1 : 1;
        }

        // 然后按公司名称排序
        return (a.company_name || "").localeCompare(b.company_name || "");
      },
      sortDirections: ["ascend", "descend"],
      sortOrder: sortedInfo.columnKey === "index" ? sortedInfo.order : null,
    },
    {
      title: "公司分组",
      dataIndex: "companyGroup",
      key: "companyGroup",
      fixed: "left",
      width: 120,
      sorter: (a, b) => {
        // 排名行始终排在最前面
        if (a.isRankRow) return -1;
        if (b.isRankRow) return 1;
        // 最大值行排在第二位
        if (a.isMaxRow) return -1;
        if (b.isMaxRow) return 1;
        // 最小值行排在第三位
        if (a.isMinRow) return -1;
        if (b.isMinRow) return 1;

        // 集团内公司排在前面
        if (a.companyGroup !== b.companyGroup) {
          return a.companyGroup === "集团内公司" ? -1 : 1;
        }

        // 同组内按公司名称排序
        return (a.company_name || "").localeCompare(b.company_name || "");
      },
      sortDirections: ["ascend", "descend"],
      sortOrder:
        sortedInfo.columnKey === "companyGroup" ? sortedInfo.order : null,
    },
    {
      title: "公司名称",
      dataIndex: "company_name",
      key: "company_name",
      fixed: "left",
      width: 120,
      sorter: (a, b) => {
        // 排名行始终排在最前面
        if (a.isRankRow) return -1;
        if (b.isRankRow) return 1;
        // 最大值行排在第二位
        if (a.isMaxRow) return -1;
        if (b.isMaxRow) return 1;
        // 最小值行排在第三位
        if (a.isMinRow) return -1;
        if (b.isMinRow) return 1;

        return (a.company_name || "").localeCompare(b.company_name || "");
      },
      sortDirections: ["ascend", "descend"],
      sortOrder:
        sortedInfo.columnKey === "company_name" ? sortedInfo.order : null,
    },
    // 根据选择的指标动态生成列，并按照固定顺序排序
    ...metricsObjects
      .map((metric) => ({
        title: <MetricHeaderTooltip metric={metric} />,
        dataIndex: "metric_values",
        key: metric.code,
        width: 150,
        align: "right",
        sorter: (a, b) => {
          // 排名行始终排在最前面
          if (a.isRankRow) return -1;
          if (b.isRankRow) return 1;
          // 最大值行排在第二位
          if (a.isMaxRow) return -1;
          if (b.isMaxRow) return 1;
          // 最小值行排在第三位
          if (a.isMinRow) return -1;
          if (b.isMinRow) return 1;

          const va = a.metric_values[metric.code]?.value;
          const vb = b.metric_values[metric.code]?.value;
          if (va === undefined && vb === undefined) return 0;
          if (va === undefined) return 1;
          if (vb === undefined) return -1;
          return va - vb;
        },
        sortDirections: ["descend", "ascend"],
        sortOrder:
          sortedInfo.columnKey === metric.code ? sortedInfo.order : null,
        render: (metricValues, record) => {
          const metricData = metricValues
            ? metricValues[metric.code]
            : undefined;
          const value = metricData ? metricData.value : undefined;
          const evaluationResult = metricData
            ? metricData.evaluation_result
            : undefined;
          let style = {};

          // 如果是排名行，显示电科数字公司的排名
          if (record.isRankRow) {
            // 从排名行的 metric_values 中获取电科数字公司的排名
            const diankeRank = metricData?.diankeRank || "-";

            return (
              <div
                style={{
                  textAlign: "center",
                  backgroundColor: "#f0f0f0",
                  padding: "4px",
                }}
              >
                {diankeRank}
              </div>
            );
          }

          // 如果是最大值行，显示最大值公司名称和值
          if (record.isMaxRow) {
            const companyName = metricData?.company_name || "-";
            const displayValue = metricData?.display_value || "-";
            return (
              <div
                style={{
                  textAlign: "center",
                  padding: "4px",
                }}
              >
                <div>{companyName}</div>
                <div>{displayValue}</div>
              </div>
            );
          }

          // 如果是最小值行，显示最小值公司名称和值
          if (record.isMinRow) {
            const companyName = metricData?.company_name || "-";
            const displayValue = metricData?.display_value || "-";
            return (
              <div
                style={{
                  textAlign: "center",
                  padding: "4px",
                }}
              >
                <div>{companyName}</div>
                <div>{displayValue}</div>
              </div>
            );
          }

          // 根据评估结果设置样式
          if (evaluationResult === "优秀") {
            style = {
              backgroundColor: "#fff1f0",
              color: "#f5222d",
              padding: "4px",
              textAlign: "right",
            };
          } else if (evaluationResult === "较差") {
            style = {
              backgroundColor: "#e8f5e9",
              color: "#2e7d32",
              padding: "4px",
              textAlign: "right",
            };
          } else {
            style = { padding: "4px", textAlign: "right" };
          }

          // 转换数值并格式化显示
          const convertedValue = convertValueByUnit(value, metric.value_type);
          const displayValue = formatMetricValue(
            convertedValue,
            metric.value_type,
            dataUnit,
            metric
          );

          return (
            <MetricTooltip
              metric={metric}
              metricData={metricData}
              dataUnit={dataUnit}
              convertedValue={convertedValue}
            >
              <div style={style}>{displayValue}</div>
            </MetricTooltip>
          );
        },
      })),
  ], [metricsObjects, sortedInfo, convertValueByUnit, dataUnit]);

  return (
    <>
      <Table
        columns={columns}
        dataSource={finalTableData}
        bordered
        loading={loading}
        showSorterTooltip={false}
        size="small"
        scroll={{ x: "max-content", y: "calc(100vh - 430px)" }}
        rowClassName={(record) => (record.isFixedRow ? "fixed-row" : "")}
        pagination={false}
        className="flex-table"
        style={{
          height: "calc(100vh - 500px)",
          minHeight: "400px",
        }}
        onChange={handleTableChange}
        sticky={{
          offsetHeader: 0,
          offsetScroll: 0,
          getContainer: () => document.querySelector(".ant-table-body"),
        }}
      />
      <style>
        {`.fixed-row {
          position: sticky;
          top: 0;
          z-index: 10;
          background: #f0f0f0;
        }
        .fixed-row td {
          position: sticky;
          z-index: 11;
          background: #f0f0f0;
        }
        .fixed-row td:nth-child(1) {
          position: sticky;
          left: 0;
          z-index: 12;
          background: #f0f0f0;
        }
        .fixed-row td:nth-child(2) {
          position: sticky;
          left: 80px;
          z-index: 12;
          background: #f0f0f0;
        }
        .fixed-row td:nth-child(3) {
          position: sticky;
          left: 200px;
          z-index: 12;
          background: #f0f0f0;
        }
        /* 固定列样式 */
        .ant-table-cell-fix-left {
          background: #fff;
        }
        /* 固定列和固定行的交叉单元格 */
        .fixed-row .ant-table-cell-fix-left {
          background: #f0f0f0;
        }`}
      </style>
    </>
  );
};

export default FinancialTable;
