import argparse
import pandas as pd
import json
import os
import time
from datetime import datetime
from metrics_calculator import MetricsCalculator

def load_stock_list(file_path):
    """
    从文件加载股票列表

    参数:
        file_path (str): 文件路径，支持CSV和TXT格式

    返回:
        list: 股票代码列表
    """
    if file_path.endswith('.csv'):
        df = pd.read_csv(file_path)
        # 假设CSV文件的第一列是股票代码
        return df.iloc[:, 0].tolist()
    elif file_path.endswith('.txt'):
        with open(file_path, 'r', encoding='utf-8') as f:
            return [line.strip() for line in f if line.strip()]
    else:
        raise ValueError("不支持的文件格式，请使用CSV或TXT文件")

def batch_calculate(stock_list, date_str, save_to_db=True, output_dir=None):
    """
    批量计算多个股票的财务指标

    参数:
        stock_list (list): 股票代码列表
        date_str (str): 日期，格式为YYYYMMDD
        save_to_db (bool): 是否保存结果到数据库，默认为True
        output_dir (str): 输出目录，如果指定，则将结果保存到该目录下的JSON文件

    返回:
        dict: 包含计算结果的字典
    """
    calculator = MetricsCalculator()
    results = {}
    success_count = 0
    failure_count = 0

    start_time = time.time()

    for i, stock_code in enumerate(stock_list):
        print(f"[{i+1}/{len(stock_list)}] 计算 {stock_code} 在 {date_str} 的财务指标...")
        try:
            stock_result = calculator.calculate_metrics(stock_code, date_str, save_to_db)
            if stock_result:
                results[stock_code] = stock_result
                success_count += 1

                # 如果指定了输出目录，将结果保存到JSON文件
                if output_dir:
                    os.makedirs(output_dir, exist_ok=True)
                    output_file = os.path.join(output_dir, f"{stock_code}_{date_str}.json")
                    with open(output_file, 'w', encoding='utf-8') as f:
                        json.dump(stock_result, f, ensure_ascii=False, indent=4)
            else:
                failure_count += 1
                print(f"计算 {stock_code} 失败")
        except Exception as e:
            failure_count += 1
            print(f"计算 {stock_code} 时出错: {str(e)}")

    end_time = time.time()
    execution_time = end_time - start_time

    # 汇总结果
    summary = {
        'date': date_str,
        'total_stocks': len(stock_list),
        'success_count': success_count,
        'failure_count': failure_count,
        'execution_time': execution_time,
        'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    }

    print(f"\n批量计算完成:")
    print(f"总计: {len(stock_list)} 只股票")
    print(f"成功: {success_count} 只")
    print(f"失败: {failure_count} 只")
    print(f"执行时间: {execution_time:.2f} 秒")

    # 如果指定了输出目录，将汇总结果保存到JSON文件
    if output_dir:
        summary_file = os.path.join(output_dir, f"summary_{date_str}.json")
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(summary, f, ensure_ascii=False, indent=4)
        print(f"汇总结果已保存到: {summary_file}")

    return results

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='批量计算财务指标')
    parser.add_argument('--stock_list', type=str, required=True, help='股票列表文件路径，支持CSV和TXT格式')
    parser.add_argument('--date', type=str, default="20241231", help='日期，格式为YYYYMMDD')
    parser.add_argument('--no_save', action='store_true', help='不保存结果到数据库')
    parser.add_argument('--output_dir', type=str, help='输出目录，如果指定，则将结果保存到该目录下的JSON文件')

    args = parser.parse_args()

    # 加载股票列表
    stock_list = load_stock_list(args.stock_list)
    print(f"加载了 {len(stock_list)} 只股票")

    # 批量计算
    batch_calculate(stock_list, args.date, not args.no_save, args.output_dir)
