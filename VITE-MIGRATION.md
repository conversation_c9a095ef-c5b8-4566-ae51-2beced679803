# Webpack to Vite Migration Guide

This document outlines the steps taken to migrate the project from Webpack to Vite.

## Changes Made

1. **Updated package.json**:

   - Changed scripts to use Vite commands
   - Added Vite and related dependencies
   - Removed webpack-related dependencies

2. **Created Vite Configuration**:

   - Created vite.config.js with environment variable handling
   - Set up proper build paths and server options

3. **Updated Project Structure**:

   - Created src directory with main.jsx as the entry point
   - Moved the React component to its own file
   - Created App.jsx as the main component
   - Moved CSS to src/index.css

4. **Updated index.html**:

   - Removed webpack-specific elements
   - Added Vite module script

5. **Environment Variables**:
   - Updated environment variable access from `process.env.REACT_APP_*` to `import.meta.env.REACT_APP_*`
   - Added vite-plugin-env-compatible for backward compatibility

## Steps to Complete the Migration

1. **Install Dependencies**:

   ```bash
   cd web
   npm install @vitejs/plugin-react vite vite-plugin-env-compatible --save-dev
   ```

2. **Run Development Server**:

   ```bash
   cd web
   npm run dev
   ```

3. **Build for Production**:
   ```bash
   cd web
   npm run build
   ```

## Environment Variables

Vite 使用与 webpack 不同的环境变量处理方式。默认情况下，Vite 通过 `import.meta.env` 对象暴露环境变量。我们已经添加了 `vite-plugin-env-compatible` 插件来保持与 `process.env.REACT_APP_*` 格式的兼容性。

### 环境变量文件加载顺序

我们已经修改了 `vite.config.js` 文件，实现了以下环境变量加载逻辑：

1. 首先加载 `.env` 文件作为基础环境变量
2. 然后根据优先级加载特定环境变量：
   - 如果设置了 `ENV_FILE` 环境变量，则加载该文件中的环境变量
   - 否则，根据当前模式（development/production）加载对应的 `.env.{mode}` 文件
3. 特定环境的变量会覆盖基础环境变量

### 环境变量文件

- `.env`: 默认环境变量，始终加载
- `.env.development`: 开发环境特定变量
- `.env.production`: 生产环境特定变量
- 自定义环境文件：可以通过设置 `ENV_FILE` 环境变量来指定

### 使用方式

在代码中，您应该使用 `process.env` 来访问环境变量，这样可以保持与 webpack 的兼容性：

```javascript
// 使用 process.env (推荐方式)
const apiUrl = process.env.REACT_APP_API_BASE_URL || "/api";
```

> **注意**：Vite 默认使用 `import.meta.env` 来访问环境变量，但我们已经在 vite.config.js 中进行了配置，使得 `process.env` 也能正常工作。

### 环境变量问题排查

如果环境变量没有正确加载，可以尝试以下方法：

1. **检查环境变量文件**：
   确保环境变量文件（.env, .env.development 等）存在且格式正确。

2. **使用测试脚本**：
   我们提供了一个测试脚本，用于验证环境变量是否正确加载：

   ```bash
   node test-env.js .env.development
   ```

3. **检查 vite.config.js**：
   确保 vite.config.js 中的环境变量配置正确：

   ```javascript
   define: {
     'process.env': env,
     // 显式定义关键环境变量
     __REACT_APP_API_BASE_URL__: JSON.stringify(env.REACT_APP_API_BASE_URL)
   }
   ```

4. **在代码中添加日志**：
   在组件中添加日志，查看环境变量是否正确加载：
   ```javascript
   console.log("API Base URL:", process.env.REACT_APP_API_BASE_URL);
   ```

### 使用环境变量文件

我们提供了多种方式来使用不同的环境变量文件：

#### 1. 使用 npm 脚本

package.json 中已经添加了预设的脚本，可以直接使用：

```bash
# 使用默认环境变量
npm run dev

# 使用开发环境变量
npm run dev:dev

# 使用生产环境变量
npm run dev:prod

# 构建时也可以指定环境
npm run build:dev
npm run build:prod
```

#### 2. 使用辅助脚本

我们提供了两个辅助脚本，可以更灵活地指定环境变量文件：

```bash
# 开发模式
node start.js .env.development
node start.js .env.production
node start.js 自定义环境文件.env

# 构建模式
node build.js .env.development
node build.js .env.production
node build.js 自定义环境文件.env
```

#### 3. 手动设置环境变量

如果需要更灵活的控制，可以手动设置环境变量：

```bash
# Windows
set ENV_FILE=.env.staging
npm run dev

# Linux/Mac
ENV_FILE=.env.staging npm run dev
```

## Key Benefits of Vite

1. **Faster Development Server**:

   - Vite uses native ES modules for development, which means it doesn't need to bundle your entire application during development.
   - This results in significantly faster startup times and hot module replacement.

2. **Optimized Production Builds**:

   - Vite uses Rollup for production builds, which provides efficient code splitting and tree-shaking.

3. **Simplified Configuration**:

   - Vite requires less configuration than webpack, with sensible defaults for most use cases.

4. **Better Error Messages**:
   - Vite provides more readable and helpful error messages during development.

## Troubleshooting

If you encounter issues with the migration:

1. **Environment Variables Not Working**:

   - Make sure your environment variables are prefixed with `REACT_APP_`
   - Check that the vite-plugin-env-compatible plugin is properly configured

2. **Module Resolution Issues**:

   - Vite uses ES modules by default, which may cause issues with CommonJS modules
   - Add problematic packages to the `optimizeDeps.include` array in vite.config.js

3. **Build Errors**:
   - Check for any webpack-specific code or plugins that may need to be replaced with Vite equivalents
