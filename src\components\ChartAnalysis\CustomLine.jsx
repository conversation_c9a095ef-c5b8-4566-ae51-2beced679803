import { useEffect, useRef } from 'react';
import { Chart } from '@antv/g2';

const CustomLine = ({ data, height=320, title, style, isFullscreen = false, isIndividualFullscreen = false }) => {
  const container = useRef();
  
  // 解析style.height，优先使用style.height，否则用props.height
  let computedHeight = height;
  if (isIndividualFullscreen) {
    // 单个全屏时，使用视口高度的90%
    computedHeight = window.innerHeight * 0.9;
  } else if (isFullscreen) {
    // 整体全屏时，直接使用传入的 height（已经通过 scale 计算过）
    computedHeight = height;
  } else if (!height) {
    computedHeight = 320; // 默认高度
  } else if (style && style.height) {
    if (typeof style.height === 'number') {
      computedHeight = style.height;
    } else if (typeof style.height === 'string' && style.height.endsWith('px')) {
      computedHeight = parseInt(style.height);
    } else if (typeof style.height === 'string') {
      computedHeight = parseInt(style.height);
    }
  }
  
  // 如果height是字符串10用容器高度
  if (typeof height === 'string' && height === '100%') {
    computedHeight = '100%';
  }

  useEffect(() => {
    if (!container.current) return;
    container.current.innerHTML = '';

    // 处理数据
    const processedData = data.map(item => ({
      ...item,
    }));

    // 如果computedHeight是"100%"，获取容器实际高度
    let finalHeight = computedHeight;
    if (computedHeight === '100%') {
      const containerHeight = container.current.offsetHeight;
      finalHeight = containerHeight > 0 ? containerHeight : 320;
    }

    let chart = null;
    
    // 使用setTimeout确保DOM完全准备好
    const timer = setTimeout(() => {
      if (!container.current) return;
      
      try {
        chart = new Chart({
          container: container.current,
          autoFit: true,
          height: finalHeight,
          title: {
            title: title,titleFontSize: 22,
            },interaction: {
              tooltip: {
                css: {
      
                  '.g2-tooltip-list-item': {
                    'font-size': '18px',
                  },
      
                },
      
              }
            },
        });
        
        // 计算缩放比例，用于响应式调整字体大小
        const baseHeight = 320; // 基准高度
        const scaleRatio = finalHeight / baseHeight;
        const fontSize = Math.max(12, Math.min(20, 12 * scaleRatio)); // 字体大小在12-20之间
        const labelFontSize = Math.max(10, Math.min(16, 10 * scaleRatio)); // 标签字体大小

        chart
          .data(processedData)

          .encode('x', 'year')
          .encode('y', 'value')
          .encode('color', 'company')
          .axis({ x: { title: false,labelFill:'#000000',labelFillOpacity:2 }, y: false })
          .scale('x', { nice: true  })
          .scale('y', { nice: true })
          .legend({
            color:{
            maxRows: 1, 
            },
            itemMarker: {
              style: {
                r: 4 * scaleRatio, // 图例标记大小
              }
            },
            itemContent: {
              style: {
                fontSize: fontSize, // 图例文字大小
              }
            }
          })

        chart.line()
          .encode('shape', 'smooth')
          .style('lineWidth', 2);

        chart.point()
          .encode('shape', 'point')
          .tooltip(false)
          .label({
            text: (d) => {
              if (d.displayValue !== undefined && d.displayValue !== null) return String(d.displayValue);
              if (d.value !== undefined && d.value !== null) return String(d.value);
              return '';
            },
            position: 'top',
            style: {
              fontSize: 14, 
            },
          })
        chart
          .labelTransform({ type: 'overlapHide' })
          .labelTransform({ type: 'contrastReverse' });
        
        chart.render();

        // 添加 resize 监听器，当容器尺寸变化时重新渲染
        const resizeObserver = new ResizeObserver(() => {
          if (chart && !chart.destroyed) {
            chart.render();
          }
        });
        
        if (container.current) {
          resizeObserver.observe(container.current);
        }

        return () => {
          clearTimeout(timer);
          resizeObserver.disconnect();
          if (chart) {
            try {
              chart.destroy();
            } catch (error) {
              // 忽略销毁时的错误
            }
          }
        };
      } catch (error) {
        console.error('Error creating chart:', error);
      }
    }, 0);
    
    return () => {
      clearTimeout(timer);
      if (chart) {
        try {
          chart.destroy();
        } catch (error) {
          // 忽略销毁时的错误
        }
      }
    };
  }, [data, computedHeight, title, style, isFullscreen, isIndividualFullscreen]);

  return (
    <div style={{ width: '100%', height: '100%' }}>
      <div ref={container} style={{ width: '100%', height: '100%' }} />
    </div>
  );
};
export default CustomLine; 