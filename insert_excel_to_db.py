import pandas as pd

from database.db_handler import DatabaseHandler


# 读取 Excel 文件
excel_file = pd.ExcelFile('database\指标\财务分析数据 (version 1).xlsx')
df = excel_file.parse('Sheet18')  # 假设数据在 Sheet1 中


with DatabaseHandler() as db:
    # 遍历每一列（无论列名是什么）
    for name in df.columns:
        # 提取当前列的两行数据
        col_data = df[name]  # 获取当前列的所有数据

        windValue = col_data.iloc[0]   # 第一行是key（索引0）

        # 参数化SQL语句（防SQL注入）
        insert_query = "INSERT INTO MetricsDefinition ([Name], [WindValue]) VALUES (?, ?)"

        try:
            cursor = db.connection.cursor()
            cursor.execute(insert_query, (name, windValue))  # 参数必须为元组
            db.connection.commit()
        except Exception as e:
            db.connection.rollback()
            print(f"插入失败（列 {col}）: {str(e)}")

print("数据插入完成！")
