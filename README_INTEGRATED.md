# 整合数据导入和指标计算程序

## 概述

本程序整合了Wind数据导入和财务指标计算功能，提供一站式数据处理解决方案。

## 功能特性

### 🚀 核心功能
- **数据导入**: 从Wind终端导入财务数据
- **指标计算**: 计算财务指标并保存到数据库
- **智能检查**: 自动检查未处理的数据，避免重复计算
- **日志记录**: 完整的执行过程日志记录

### 📊 处理模式
- **完整流程**: 导入数据 + 计算指标
- **仅计算**: 跳过导入，只计算指标
- **强制重新计算**: 忽略已处理数据，重新计算所有指标
- **仅检查**: 只检查未处理的数据，不执行计算

### 🛠️ 灵活配置
- 支持指定年份、季度范围
- 支持指定单个或多个股票代码
- 支持跳过数据库保存（测试模式）
- 支持不同日志级别

## 文件结构

```
windgather/
├── integrated_data_processor.py    # 主程序
├── run_integrated.bat              # Windows批处理脚本
├── integrated_config.py            # 配置文件
├── import_wind_data.py             # Wind数据导入模块
├── metrics_calculator.py           # 指标计算模块
└── README_INTEGRATED.md            # 本文档
```

## 使用方法

### 1. 基本使用

#### 完整流程（推荐）
```bash
# 导入2024年半年报和年报数据，并计算指标
python integrated_data_processor.py --start-year 2024 --end-year 2024 --quarters 0630 1231
```

#### 仅计算指标
```bash
# 跳过数据导入，只计算指标
python integrated_data_processor.py --start-year 2024 --end-year 2024 --quarters 0630 --skip-import
```

#### 强制重新计算
```bash
# 强制重新计算所有指标，忽略已处理数据
python integrated_data_processor.py --start-year 2024 --end-year 2024 --quarters 0630 --force-recalculate
```

### 2. 高级使用

#### 指定股票代码
```bash
# 处理单个股票
python integrated_data_processor.py --start-year 2024 --end-year 2024 --quarters 0630 --stock-code 000001.SZ

# 处理多个股票
python integrated_data_processor.py --start-year 2024 --end-year 2024 --quarters 0630 --stock-codes 000001.SZ 000002.SZ 000858.SZ
```

#### 测试模式
```bash
# 不保存到数据库，仅测试计算逻辑
python integrated_data_processor.py --start-year 2024 --end-year 2024 --quarters 0630 --no-save
```

#### 仅检查数据
```bash
# 检查未处理的数据，不执行计算
python integrated_data_processor.py --start-year 2024 --end-year 2024 --quarters 0630 --check-only
```

### 3. 批处理脚本

#### Windows用户
```bash
# 双击运行
run_integrated.bat

# 或命令行运行
run_integrated.bat
```

### 4. 日志控制

```bash
# 详细日志（调试用）
python integrated_data_processor.py --start-year 2024 --end-year 2024 --quarters 0630 --log-level DEBUG

# 仅错误日志
python integrated_data_processor.py --start-year 2024 --end-year 2024 --quarters 0630 --log-level ERROR
```

## 命令行参数

### 必需参数
- `--start-year`: 开始年份
- `--end-year`: 结束年份

### 可选参数
- `--quarters`: 季度列表，默认所有季度
  - 可选值: `0331`, `0630`, `0930`, `1231`
- `--stock-code`: 指定单个股票代码
- `--stock-codes`: 指定多个股票代码
- `--skip-import`: 跳过数据导入阶段
- `--force-recalculate`: 强制重新计算指标
- `--no-save`: 不保存结果到数据库
- `--check-only`: 仅检查未处理的数据
- `--log-level`: 日志级别 (DEBUG/INFO/WARNING/ERROR)

## 使用场景

### 场景1: 首次运行
```bash
# 导入2024年所有季度数据并计算指标
python integrated_data_processor.py --start-year 2024 --end-year 2024 --quarters 0331 0630 0930 1231
```

### 场景2: 增量更新
```bash
# 只处理2024年半年报（跳过已处理的季度）
python integrated_data_processor.py --start-year 2024 --end-year 2024 --quarters 0630
```

### 场景3: 数据修正
```bash
# 强制重新计算2024年半年报数据
python integrated_data_processor.py --start-year 2024 --end-year 2024 --quarters 0630 --force-recalculate
```

### 场景4: 测试验证
```bash
# 测试单个股票的计算逻辑
python integrated_data_processor.py --start-year 2024 --end-year 2024 --quarters 0630 --stock-code 000001.SZ --no-save --log-level DEBUG
```

### 场景5: 批量处理
```bash
# 处理多个年份的数据
python integrated_data_processor.py --start-year 2023 --end-year 2024 --quarters 0630 1231
```

## 输出结果

### 控制台输出
```
================================================================================
整合数据处理完成
================================================================================
开始时间: 2024-08-05 10:00:00
结束时间: 2024-08-05 10:30:00
总耗时: 1800.00 秒

数据导入:
  状态: 成功

指标计算:
  总数据项: 5000
  处理项: 1000
  未处理项: 4000
  成功项: 950
  失败项: 50
  跳过项: 0
  计算耗时: 1200.00 秒
```

### 日志文件
- `integrated_processor_YYYYMMDD.log`: 详细执行日志
- `metrics_calculation_YYYYMMDD.log`: 指标计算日志
- `wind_import_YYYYMMDD.log`: 数据导入日志

## 注意事项

### 前置条件
1. **Wind终端**: 确保Wind终端已登录并正常运行
2. **数据库连接**: 确保数据库连接正常
3. **Python环境**: 确保所有依赖包已安装

### 性能考虑
1. **数据量**: 处理大量数据时建议分批进行
2. **网络**: 数据导入需要稳定的网络连接
3. **内存**: 大量股票计算时注意内存使用

### 错误处理
1. **导入失败**: 检查Wind终端状态和网络连接
2. **计算失败**: 检查数据库连接和公式定义
3. **中断恢复**: 程序支持断点续传，可重新运行

## 故障排除

### 常见问题

#### 1. Wind连接失败
```
错误: Wind数据导入失败
解决: 检查Wind终端是否登录，网络是否正常
```

#### 2. 数据库连接失败
```
错误: 无法连接数据库
解决: 检查数据库服务是否启动，连接字符串是否正确
```

#### 3. 内存不足
```
错误: 内存不足
解决: 减少处理的股票数量，分批处理
```

#### 4. 权限问题
```
错误: 无法写入日志文件
解决: 检查目录权限，或指定有写权限的目录
```

### 调试技巧

#### 1. 启用详细日志
```bash
python integrated_data_processor.py --log-level DEBUG
```

#### 2. 测试模式
```bash
python integrated_data_processor.py --no-save --stock-code 000001.SZ
```

#### 3. 分步检查
```bash
# 先检查未处理数据
python integrated_data_processor.py --check-only --start-year 2024 --end-year 2024 --quarters 0630

# 再执行处理
python integrated_data_processor.py --start-year 2024 --end-year 2024 --quarters 0630
```

## 版本信息

- **版本**: 1.0
- **更新日期**: 2024-08-05
- **支持平台**: Windows
- **Python版本**: 3.7+

## 更新日志

### v1.0 (2024-08-05)
- 初始版本发布
- 支持Wind数据导入和指标计算整合
- 支持智能数据重复检查
- 支持完整的日志记录
- 支持多种处理模式 