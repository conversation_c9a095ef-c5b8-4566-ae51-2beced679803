import re


class FormulaExecutor:
    """
    公式执行器，支持处理上期和上期末数据
    """

    def __init__(self, indicator_definitions=None, metrics_definitions=None):
        """
        初始化公式执行器

        参数:
            indicator_definitions (dict): 中英文指标对照关系字典，可选
            metrics_definitions (dict): MetricsDefinition表中的基础指标定义，可选
        """
        self.indicator_definitions = indicator_definitions or {}
        # 确保metrics_definitions是字典且包含ShowName字段
        self.metrics_definitions = {}
        if metrics_definitions:
            for metric_id, metric in metrics_definitions.items():
                if isinstance(metric, dict):
                    self.metrics_definitions[metric.get('IndicatorCode', metric_id)] = {
                        'ShowName': metric.get('IndicatorName', metric.get('ShowName', metric_id))
                    }
                else:
                    self.metrics_definitions[metric_id] = {'ShowName': str(metric)}

    def _check_period_value_exists(self, var_name, period_type, previous_values, previous_end_values):
        """
        检查上期或上期末的值是否存在

        参数:
            var_name (str): 变量名
            period_type (str): 期间类型，'上期' 或 '上期末'
            previous_values (dict): 上期值字典
            previous_end_values (dict): 上期末值字典

        返回:
            bool: 如果值存在返回True，否则返回False
        """
        var_name_lower = var_name.lower()
        values_dict = previous_values if period_type == '上期' else previous_end_values

        # 1. 直接从values_dict获取，不区分大小写
        for k in values_dict:
            if k.lower() == var_name_lower:
                return True

        # 2. 如果没有找到，尝试使用指标定义中的映射
        if var_name in self.indicator_definitions:
            alt_name = self.indicator_definitions[var_name]
            if alt_name in values_dict:
                return True

        return False

    def _get_period_value(self, var_name, period_type, previous_values, previous_end_values):
        """
        获取上期或上期末的值

        参数:
            var_name (str): 变量名
            period_type (str): 期间类型，'上期' 或 '上期末'
            previous_values (dict): 上期值字典
            previous_end_values (dict): 上期末值字典

        返回:
            float: 变量的值，如果不存在则返回0
        """
        var_name_lower = var_name.lower()
        values_dict = previous_values if period_type == '上期' else previous_end_values

        # 1. 直接从values_dict获取，不区分大小写
        for k, v in values_dict.items():
            if k.lower() == var_name_lower:
                return v

        # 2. 如果没有找到，尝试使用指标定义中的映射
        if var_name in self.indicator_definitions:
            alt_name = self.indicator_definitions[var_name]
            if alt_name in values_dict:
                return values_dict[alt_name]

        # 3. 如果仍然没有找到，记录警告并使用0
        print(f"警告: 未找到变量 {var_name} 的{period_type}值，使用0代替")
        return 0

    def _get_chinese_name(self, var_name):
        """获取指标的中文名称"""
        # 处理带后缀的变量名（上期/上期末）
        suffix = ""
        if var_name.endswith('_上期'):
            base_name = var_name[:-3]
            suffix = "_上期"
        elif var_name.endswith('_上期末'):
            base_name = var_name[:-4]
            suffix = "_上期末"
        else:
            base_name = var_name

        # 1. 先尝试从metrics_definitions获取ShowName（不区分大小写）
        lower_base_name = base_name.lower()
        for metric_name, metric_def in self.metrics_definitions.items():
            if metric_name.lower() == lower_base_name:
                return metric_def.get('ShowName', base_name) + suffix
            
        # 2. 尝试从indicator_definitions获取
        definition = self.indicator_definitions.get(base_name)
        
        # 如果是字典类型，提取中文名称
        if isinstance(definition, dict):
            return definition.get('indicator_name_cn', base_name) + suffix
        
        # 如果是字符串类型，直接返回
        elif definition:
            return definition + suffix
            
        # 3. 默认返回原变量名（带后缀）
        return base_name + suffix

    def execute_formula(self, formula, current_values, previous_values, previous_end_values):
        """
        执行财务指标公式

        参数:
            formula (str): 从数据库读取的公式字符串
            current_values (dict): 当期值字典
            previous_values (dict): 上期值字典
            previous_end_values (dict): 上期末值字典

        返回:
            dict: 包含计算结果和翻译后公式
                {
                    "result": float,  # 计算结果
                    "original_formula": str,  # 原始公式
                    "translated_formula": str,  # 翻译后公式(含中间步骤)
                    "variables": dict  # 变量值快照
                }
        """
        translated_parts = []
        # 如果公式为空或者只是一个变量名，直接返回对应的值
        if not formula or formula.strip() == '':
            return {
                "result": 0,
                "original_formula": formula,
                "translated_formula": "",
                "variables": {}
            }

        # 创建本地变量字典，用于eval执行
        local_vars = {}

        # 将当前值添加到本地变量字典（不区分大小写）
        for var, value in current_values.items():
            local_vars[var.lower()] = value

        # 将上期值添加到本地变量字典
        for var, value in previous_values.items():
            local_vars[f"{var.lower()}_上期"] = value

        # 将上期末值添加到本地变量字典
        for var, value in previous_end_values.items():
            local_vars[f"{var.lower()}_上期末"] = value

        # 检查公式是否只是一个简单的变量名
        formula_stripped = formula.strip()
        if formula_stripped in current_values:
            chinese_name = self._get_chinese_name(formula_stripped)
            return {
                "result": current_values[formula_stripped],
                "original_formula": formula,
                "translated_formula": f"{chinese_name}({current_values[formula_stripped]})",
                "variables": {formula_stripped: current_values[formula_stripped]}
            }



        # 如果公式以等号开头，去掉等号
        if formula.startswith('='):
            formula = formula[1:]

        # 替换所有中文特殊字符
        replacements = {
            "（": "(", "）": ")",
            "×": "*", "÷": "/",
            "％": "%", "／": "/",
            " ": "",  # 移除空格
        }
        for ch, rep in replacements.items():
            formula = formula.replace(ch, rep)

        # 处理公式中的特殊标记
        # 替换[上期]和[上期末]为对应的历史数据
        pattern = r'([a-zA-Z0-9_]+)\[(上期|上期末)\]'
        matches = re.findall(pattern, formula)

        for var_name, period in matches:
            var_name_lower = var_name.lower()

            # 检查上期或上期末值是否存在
            exists = self._check_period_value_exists(var_name, period, previous_values, previous_end_values)
            if not exists:
                print(f"警告: 变量 {var_name} 的{period}值不存在")

            # 获取上期或上期末值
            value = self._get_period_value(var_name, period, previous_values, previous_end_values)

            # 替换公式中的标记
            formula = formula.replace(f"{var_name}[{period}]", str(value))

            # 添加到本地变量字典
            if period == '上期':
                local_vars[f"{var_name_lower}_上期"] = value
            else:  # 上期末
                local_vars[f"{var_name_lower}_上期末"] = value

        # 打印上期和上期末变量的数量，用于调试
        upper_count = sum(1 for k in local_vars if k.endswith('_上期'))
        upper_end_count = sum(1 for k in local_vars if k.endswith('_上期末'))
        print(f"上期变量数量: {upper_count}, 上期末变量数量: {upper_end_count}")

        # 处理公式中的特殊情况
        # 例如，处理"total_invested_capital"这样的复合计算
        if "total_invested_capital" in formula.lower():


            # 处理上期末的total_invested_capital
            if 'total_invested_capital_上期末' in formula.lower():
                # 检查所有组成部分是否存在
                components = ['eqy_belongto_parcomsh', 'st_borrow', 'wgsd_liabs_trading',
                             'non_cur_liab_due_within_1y', 'bonds_payable', 'lt_borrow', 'lease_obligation']

                for comp in components:
                    exists = self._check_period_value_exists(comp, '上期末', previous_values, previous_end_values)
                    if not exists:
                        print(f"警告: total_invested_capital_上期末 的组成部分 {comp} 不存在")

                prev_total_invested_capital = (
                    previous_end_values.get('eqy_belongto_parcomsh', 0) +
                    previous_end_values.get('st_borrow', 0) +
                    previous_end_values.get('wgsd_liabs_trading', 0) +
                    previous_end_values.get('non_cur_liab_due_within_1y', 0) +
                    previous_end_values.get('bonds_payable', 0) +
                    previous_end_values.get('lt_borrow', 0) +
                    previous_end_values.get('lease_obligation', 0)
                )
                formula = re.sub(r'total_invested_capital_上期末', str(prev_total_invested_capital), formula, flags=re.IGNORECASE)
                local_vars['total_invested_capital_上期末'] = prev_total_invested_capital
            
        # 计算total_invested_capital
            total_invested_capital = (
                current_values.get('eqy_belongto_parcomsh', 0) +
                current_values.get('st_borrow', 0) +
                current_values.get('wgsd_liabs_trading', 0) +
                current_values.get('non_cur_liab_due_within_1y', 0) +
                current_values.get('bonds_payable', 0) +
                current_values.get('lt_borrow', 0) +
                current_values.get('lease_obligation', 0)
            )
            formula = re.sub(r'total_invested_capital', str(total_invested_capital), formula, flags=re.IGNORECASE)
            local_vars['total_invested_capital'] = total_invested_capital
        # 处理EBITDA计算
        if "EBITDA" in formula.upper():
            # 检查所有组成部分是否存在
            components = ['tot_profit', 'fin_int_exp', 'depr_fa_coga_dpba',
                         'depre_prop_right_use', 'amort_intang_assets', 'amort_lt_deferred_exp']

            for comp in components:
                if comp not in current_values:
                    print(f"警告: EBITDA 的组成部分 {comp} 不存在")

            ebitda = (
                current_values.get('tot_profit', 0) +
                current_values.get('fin_int_exp', 0) +
                current_values.get('depr_fa_coga_dpba', 0) +
                current_values.get('depre_prop_right_use', 0) +
                current_values.get('amort_intang_assets', 0) +
                current_values.get('amort_lt_deferred_exp', 0)
            )
            formula = re.sub(r'EBITDA', str(ebitda), formula, flags=re.IGNORECASE)
            local_vars['ebitda'] = ebitda

            # 处理上期的EBITDA
            if 'ebitda_上期' in formula.lower():
                for comp in components:
                    exists = self._check_period_value_exists(comp, '上期', previous_values, previous_end_values)
                    if not exists:
                        print(f"警告: EBITDA_上期 的组成部分 {comp} 不存在")

                ebitda_prev = (
                    previous_values.get('tot_profit', 0) +
                    previous_values.get('fin_int_exp', 0) +
                    previous_values.get('depr_fa_coga_dpba', 0) +
                    previous_values.get('depre_prop_right_use', 0) +
                    previous_values.get('amort_intang_assets', 0) +
                    previous_values.get('amort_lt_deferred_exp', 0)
                )
                formula = re.sub(r'ebitda_上期', str(ebitda_prev), formula, flags=re.IGNORECASE)
                local_vars['ebitda_上期'] = ebitda_prev

        # 处理两金净额计算
        if "two_fund_net" in formula.lower():
            # 检查组成部分是否存在
            components = ['acct_rcv', 'inventories']
            for comp in components:
                if comp not in current_values:
                    print(f"警告: two_fund_net 的组成部分 {comp} 不存在")

            two_fund_net = current_values.get('acct_rcv', 0) + current_values.get('inventories', 0)
            formula = re.sub(r'two_fund_net', str(two_fund_net), formula, flags=re.IGNORECASE)
            local_vars['two_fund_net'] = two_fund_net

            # 处理上期的两金净额
            if 'two_fund_net_上期' in formula.lower():
                for comp in components:
                    exists = self._check_period_value_exists(comp, '上期', previous_values, previous_end_values)
                    if not exists:
                        print(f"警告: two_fund_net_上期 的组成部分 {comp} 不存在")

                two_fund_net_prev = previous_values.get('acct_rcv', 0) + previous_values.get('inventories', 0)
                formula = re.sub(r'two_fund_net_上期', str(two_fund_net_prev), formula, flags=re.IGNORECASE)
                local_vars['two_fund_net_上期'] = two_fund_net_prev

        # 处理人事费用计算
        if "personnel_cost_total" in formula.lower():
            # 检查组成部分是否存在
            components = ['stmnote_others_7626', 'stmnote_others_7627', 'stmnote_rdsalary']
            for comp in components:
                if comp not in current_values:
                    print(f"警告: personnel_cost_total 的组成部分 {comp} 不存在")

            personnel_cost_total = (
                current_values.get('stmnote_others_7626', 0) +  # 销售费用-工资薪酬
                current_values.get('stmnote_others_7627', 0) +  # 管理费用-工资薪酬
                current_values.get('stmnote_rdsalary', 0)       # 研发费用-工资薪酬
            )
            formula = re.sub(r'personnel_cost_total', str(personnel_cost_total), formula, flags=re.IGNORECASE)
            local_vars['personnel_cost_total'] = personnel_cost_total

            # 处理上期的人事费用
            if 'personnel_cost_total_上期' in formula.lower():
                for comp in components:
                    exists = self._check_period_value_exists(comp, '上期', previous_values, previous_end_values)
                    if not exists:
                        print(f"警告: personnel_cost_total_上期 的组成部分 {comp} 不存在")

                personnel_cost_total_prev = (
                    previous_values.get('stmnote_others_7626', 0) +
                    previous_values.get('stmnote_others_7627', 0) +
                    previous_values.get('stmnote_rdsalary', 0)
                )
                formula = re.sub(r'personnel_cost_total_上期', str(personnel_cost_total_prev), formula, flags=re.IGNORECASE)
                local_vars['personnel_cost_total_上期'] = personnel_cost_total_prev

        # 处理研发人员数量计算
        if "stmnote_rdemployee_prev" in formula.lower() or "stmnote_rdemployee_curr" in formula.lower():
            rd_employee_curr = current_values.get('stmnote_RDemployee', 0)
            rd_employee_prev = previous_end_values.get('stmnote_RDemployee', 0)

            formula = re.sub(r'stmnote_RDemployee_curr', str(rd_employee_curr), formula, flags=re.IGNORECASE)
            formula = re.sub(r'stmnote_RDemployee_prev', str(rd_employee_prev), formula, flags=re.IGNORECASE)

            local_vars['stmnote_rdemployee_curr'] = rd_employee_curr
            local_vars['stmnote_rdemployee_prev'] = rd_employee_prev

        # 清理公式中的换行符和其他不必要的字符
        formula = formula.replace('\n', ' ')

        # 处理可能的语法错误
        formula = formula.replace('x100%', '*100%')
        formula = formula.replace('x100％', '*100%')
        formula = formula.replace('%', '/100')

        # 处理公式中的乘法
        formula = formula.replace('*100/100', '')

        # 处理公式中的除法
        formula = formula.replace('/100*100', '')

        # 处理公式中的特殊情况
        formula = formula.replace('x100', '*100')

        # 处理公式中的逗号
        formula = formula.replace('，', ',')

        # 处理公式中的分号
        formula = formula.replace('；', ';')

        # 处理公式中的冒号
        formula = formula.replace('：', ':')

        # 处理公式中的括号
        formula = formula.replace('（', '(')
        formula = formula.replace('）', ')')

        # 处理公式中的空格
        formula = formula.replace(' ', '')

        # 处理公式中的注释
        if ';' in formula:
            formula = formula.split(';')[0]

        # 处理公式中的注释
        if ',' in formula:
            formula = formula.split(',')[0]

        # 处理公式中的2inventories这样的表达式
        # formula = re.sub(r'(\d+)([a-zA-Z_]+)', r'\1*\2', formula)

        # 将公式中的变量名转换为小写，以匹配本地变量字典中的键
        for var in re.findall(r'[a-zA-Z_]+', formula):
            if var.lower() in local_vars and var != var.lower():
                formula = re.sub(r'\b' + var + r'\b', var.lower(), formula)



        # 使用指标定义中的映射关系处理其他中文变量名
        # 查找公式中的所有中文字符

        # 处理公式中的特殊符号
        # formula = formula.replace('／', '/')
        # formula = formula.replace('x', '*')

        # 打印处理后的公式，用于调试
        print(f"处理后的公式: {formula}")

        # 检查公式中是否包含None值
        for var_name in re.findall(r'[a-zA-Z_]+', formula):
            var_name_lower = var_name.lower()
            if var_name_lower in local_vars and local_vars[var_name_lower] is None:
                print(f"警告: 变量 {var_name_lower} 的值为None，无法进行计算")
                return 0

        # 计算表达式
        try:
            # 使用本地变量字典执行公式
            # 创建一个安全的执行环境，只允许使用特定的内置函数
            safe_builtins = {
                "abs": abs,  # 添加对abs()函数的支持
            }
            original_formula = formula  # 保存原始公式
            result = eval(formula, {"__builtins__": safe_builtins}, local_vars)
            print(f"计算结果: {result}")
            
            # 构建带中文名称的计算过程
            translated_formula = ""
            tokens = re.split(r'([+\-*/()])', formula)
            
            for token in tokens:
                if not token:
                    continue
                    
                if token in '+-*/()':
                    translated_formula += token
                elif token.replace('.', '').isdigit():
                    translated_formula += token
                else:
                    # 处理变量
                    var = token.strip()
                    # 检查变量名是否已包含上期/上期末后缀
                    if var.endswith('_上期') or var.endswith('_上期末'):
                        chinese_name = self._get_chinese_name(var)
                        translated_formula += f"{chinese_name}({local_vars.get(var.lower(), 0)})"
                    elif var.lower() in local_vars:
                        chinese_name = self._get_chinese_name(var)
                        translated_formula += f"{chinese_name}({local_vars[var.lower()]})"
                    else:
                        translated_formula += var
            
            translated_formula = f"{translated_formula} = {result}"
            
            return {
                "result": result,
                "original_formula": original_formula,
                "translated_formula": translated_formula 
            }
            
        except Exception as e:
            print(f"公式计算错误: {formula}")
            print(f"错误信息: {str(e)}")
            return {
                "result": 0,
                "original_formula": formula,
                "translated_formula": "",
                "variables": {}
            }
