import React, { useEffect, useRef, useState } from 'react';
import { Chart } from '@antv/g2';

const CustomRadar = ({ data, title = '', height = 320, isFullscreen = false, isIndividualFullscreen = false }) => {
  const container = useRef();
  
  // 监听单个全屏状态变化
  useEffect(() => {
    const handleFullscreenChange = () => {
      // setIsIndividualFullscreen(!!document.fullscreenElement); // 移除 useState 和 setIsIndividualFullscreen
    };
    document.addEventListener('fullscreenchange', handleFullscreenChange);
    document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
    document.addEventListener('mozfullscreenchange', handleFullscreenChange);
    document.addEventListener('MSFullscreenChange', handleFullscreenChange);
    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
      document.removeEventListener('webkitfullscreenchange', handleFullscreenChange);
      document.removeEventListener('mozfullscreenchange', handleFullscreenChange);
      document.removeEventListener('MSFullscreenChange', handleFullscreenChange);
    };
  }, []);
  useEffect(() => {
    if (!container.current) return;
    if (!data || data.length === 0) return;

    container.current.innerHTML = '';
    // 数据需转为宽表
    // 1. 获取指标顺序
    const indicators = Array.from(new Set(data.map(d => d.item)));
    // 2. 转为宽表
    const wideData = data.reduce((acc, cur) => {
      if (!acc[cur.company]) acc[cur.company] = { name: cur.company };
      acc[cur.company][cur.item] = cur.value;
      return acc;
    }, {});
    const wideDataArr = Object.values(wideData);
    // 计算实际高度
    const computedHeight = isIndividualFullscreen ? window.innerHeight * 0.9 : height;
    
    const chart = new Chart({
      container: container.current,
      autoFit: true,
      height: computedHeight,
    });
    chart.options({
      type: 'line',
      title: {
        title,
        titleFontSize: 22,
      },
      data: wideDataArr,
      coordinate: { type: 'radar' },
      encode: {
        position: indicators, // 用itemKey做encode
        color: 'name',
      },
      axis: {
     
        // 隐藏所有轴的标签
        ...Object.fromEntries(
          indicators.map((ind, i) => [
            `position${i === 0 ? '' : i}`,
            { label: false }
          ])
        ),
      },
      style: {
        lineWidth: 2 // 加粗线条
      },
      point: {
        style: {
          fill: 'white',
          stroke: '#5B8FF9',
          lineWidth: 2,
        },
      },
      label: { 
        text: (d) => {
          // 遍历所有指标，找到对应的值
          for (let indicator of indicators) {
            if (d[indicator] !== undefined) {
              return typeof d[indicator] === 'number' ? d[indicator].toFixed(2) : d[indicator];
            }
          }
          return '';
        },
        style: {
          fontSize: 10,
          fill: '#333',
          fontWeight: 'bold',
        },
        position: 'top',
      },
     
      scale: Object.fromEntries(
        indicators.map((ind, i) => [
          `position${i === 0 ? '' : i}`,
          {
            nice: true,
          },
        ])
      ),
 


      legend: { position: 'top', maxRows: 2, color: { maxRows: 2, length: 230, itemSpacing: [0, 0] } },
      interaction: {
        tooltip: {
          series: false, css: {
            '.g2-tooltip-list-item': {
              'font-size': '18px',
            },
          },
        }
      }
    });
    chart.render();

    // 添加 resize 监听器，当容器尺寸变化时重新渲染
    const resizeObserver = new ResizeObserver(() => {
      if (chart && !chart.destroyed) {
        chart.render();
      }
    });
    
    if (container.current) {
      resizeObserver.observe(container.current);
    }

    return () => {
      resizeObserver.disconnect();
      chart.destroy();
    };
  }, [data, height, isIndividualFullscreen]);
  if (!data || data.length === 0) {
    return (
      <div style={{ textAlign: 'center', padding: '50px 0', color: '#999', background: '#fff', borderRadius: 8 }}>
        暂无数据
      </div>
    );
  }

  return (
    <div style={{ width: '100%', height: '100%' }}>
      <div ref={container} style={{ width: '100%', height: '100%' }} />
    </div>
  );
};
export default CustomRadar; 