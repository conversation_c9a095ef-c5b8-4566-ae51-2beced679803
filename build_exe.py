#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Wind数据导入工具 - PyInstaller打包脚本
"""

import os
import sys
import subprocess
import shutil
from datetime import datetime

def clean_build_dirs():
    """清理构建目录"""
    dirs_to_clean = ['build', 'dist', '__pycache__']
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            print(f"🧹 清理目录: {dir_name}")
            shutil.rmtree(dir_name)
    
    # 清理spec文件
    spec_files = [f for f in os.listdir('.') if f.endswith('.spec')]
    for spec_file in spec_files:
        print(f"🧹 删除spec文件: {spec_file}")
        os.remove(spec_file)

def create_spec_file():
    """创建PyInstaller spec文件"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['import_wind_data.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('database', 'database'),
        ('logging_config.py', '.'),
        ('import_config.py', '.'),
        ('import_config_examples.py', '.'),
    ],
    hiddenimports=[
        'pandas',
        'numpy',
        'pyodbc',
        'WindPy',
        'decimal',
        'concurrent.futures',
        'threading',
        'queue',
        'datetime',
        'uuid',
        'logging',
        'argparse',
        'importlib.util',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='wind_import',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,
    version_file=None,
)
'''
    
    with open('wind_import.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("📝 创建spec文件: wind_import.spec")

def build_exe():
    """构建exe文件"""
    print("🔨 开始构建exe文件...")
    
    # 检查PyInstaller是否安装
    try:
        import PyInstaller
        print(f"✅ PyInstaller版本: {PyInstaller.__version__}")
    except ImportError:
        print("❌ PyInstaller未安装，正在安装...")
        subprocess.run([sys.executable, '-m', 'pip', 'install', 'pyinstaller'], check=True)
    
    # 清理旧文件
    clean_build_dirs()
    
    # 创建spec文件
    create_spec_file()
    
    # 执行构建
    print("🚀 执行PyInstaller构建...")
    result = subprocess.run([
        'pyinstaller',
        '--clean',
        'wind_import.spec'
    ], capture_output=True, text=True)
    
    if result.returncode == 0:
        print("✅ 构建成功!")
        
        # 检查生成的文件
        exe_path = os.path.join('dist', 'wind_import.exe')
        if os.path.exists(exe_path):
            file_size = os.path.getsize(exe_path) / (1024 * 1024)  # MB
            print(f"📦 生成文件: {exe_path}")
            print(f"📏 文件大小: {file_size:.2f} MB")
            
            # 创建使用说明
            create_usage_guide()
            
            return True
        else:
            print("❌ exe文件未生成")
            return False
    else:
        print("❌ 构建失败!")
        print("错误输出:")
        print(result.stderr)
        return False

def create_usage_guide():
    """创建使用说明文件"""
    guide_content = f'''# Wind数据导入工具 - 使用说明

## 文件说明
- `wind_import.exe`: 主程序文件
- `import_config.py`: 配置文件（可选）
- `import_config_examples.py`: 配置示例文件

## 使用方法

### 1. 智能默认模式（推荐）
直接双击运行 `wind_import.exe`，程序会自动：
- 当前2季度 → 查询1季度数据
- 当前1季度 → 查询上一年4季度数据
- 其他情况 → 查询上一个季度数据

### 2. 命令行模式
在命令行中运行：

```bash
# 使用智能默认值
wind_import.exe

# 指定年份和季度
wind_import.exe --start-year 2024 --end-year 2024 --quarters 0331

# 导入多个季度
wind_import.exe --start-year 2024 --end-year 2025 --quarters 0331 0630 0930 1231

# 只导入年报数据
wind_import.exe --start-year 2024 --end-year 2024 --quarters 1231

# 使用配置文件
wind_import.exe --config-file my_config.py

# 查看帮助
wind_import.exe --help
```

### 3. 配置文件模式
创建 `my_config.py` 文件：
```python
START_YEAR = 2024
END_YEAR = 2024
QUARTERS = ['0331', '0630', '0930', '1231']
```

然后运行：
```bash
wind_import.exe --config-file my_config.py
```

## 注意事项
1. 确保Wind终端已登录
2. 确保数据库连接正常
3. 程序会自动创建日志文件
4. 支持断点续传，重复运行不会重复导入

## 故障排除
1. 如果程序无法启动，请检查是否安装了必要的依赖
2. 如果数据导入失败，请查看日志文件
3. 如果Wind连接失败，请检查Wind终端状态

生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
'''
    
    with open('dist/使用说明.txt', 'w', encoding='utf-8') as f:
        f.write(guide_content)
    
    print("📖 创建使用说明: dist/使用说明.txt")

def main():
    """主函数"""
    print("=" * 60)
    print("Wind数据导入工具 - PyInstaller打包")
    print("=" * 60)
    
    # 检查当前目录
    if not os.path.exists('import_wind_data.py'):
        print("❌ 错误: 请在包含 import_wind_data.py 的目录中运行此脚本")
        return
    
    # 构建exe
    if build_exe():
        print("\n🎉 打包完成!")
        print("📁 输出目录: dist/")
        print("📄 主程序: dist/wind_import.exe")
        print("📖 使用说明: dist/使用说明.txt")
        print("\n💡 提示: 可以直接运行 wind_import.exe 使用智能默认值")
    else:
        print("\n❌ 打包失败，请检查错误信息")

if __name__ == "__main__":
    main() 