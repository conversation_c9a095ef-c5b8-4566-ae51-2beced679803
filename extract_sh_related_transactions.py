import os
import re
import pandas as pd
import pdfplumber
from logging_config import setup_logging
import time

logger = setup_logging()


def extract_related_transactions_from_pdf(pdf_path):
    try:
        with pdfplumber.open(pdf_path) as pdf:
            found_section = False
            all_tables = []
            
            # 优化：预先编译正则表达式
            section_pattern = re.compile(r'重大关联交易')
            
            # 优化：只处理前50页
            for page in pdf.pages[:50]:
                try:
                    # 优化：使用更高效的文本提取参数
                    text = page.extract_text(
                        x_tolerance=1,  # 水平方向容差
                        y_tolerance=1,  # 垂直方向容差
                        keep_blank_chars=False,  # 忽略空白字符
                        extra_attrs=[]  # 不提取额外属性
                    ) or ""
                    if not found_section:
                        if section_pattern.search(text):
                            found_section = True
                        else:
                            continue  # 跳过不相关页面
                    
                    # 如果找到章节，提取表格
                    tables = page.extract_tables({
                        'vertical_strategy': 'text', 
                        'horizontal_strategy': 'text',
                        'intersection_y_tolerance': 10
                    })
                    
                    if tables:
                        for table in tables:
                            if table and len(table) > 1:
                                header = table[0]
                                # 优化：简化表头检查
                                if any('关联' in str(col) for col in header if col):
                                    all_tables.append(table)
                                    # 如果已经收集到5个表格，直接返回
                                    if len(all_tables) >= 5:
                                        break
                        
                    # 如果已经收集到5个表格，直接返回
                    if len(all_tables) >= 1:
                        break
                        
                except Exception as e:
                    continue
                
                # 查找章节
                if not found_section and '重大关联交易' in text:
                    found_section = True
                
                if found_section:
                    try:
                        tables = page.extract_tables()
                        if tables:
                            for table in tables:
                                if table and len(table) > 1:
                                    header = table[0]
                                    # 优化表头检查
                                    if any(any(keyword in str(col) for col in header if col) 
                                           for keyword in ['关联交易', '关联方', '交易类型']):
                                        all_tables.append(table)
                    except:
                        continue
                    
                    # 检查是否到达下一章节
                    if re.search(r'[、\.\s]*重大合同及其履行情况', text) or \
                       re.search(r'[、\.\s]*其他重大事项的说明', text):
                        break
            
            # 处理表格数据
            if all_tables:
                dfs = []
                for table in all_tables:
                    try:
                        df = pd.DataFrame(table[1:], columns=table[0])
                        df['股票代码'] = stock_code
                        df['公司名称'] = company_name or "未知公司"
                        df['报告年份'] = report_year
                        dfs.append(df)
                    except:
                        continue
                
                if dfs:
                    return dfs
            
            return None
    except Exception as e:
        logger.error(f'处理PDF文件时出错: {str(e)}，文件：{pdf_path}')
        return None


def process_sh_annual_reports():
    """
    批量处理SH目录下的所有年报PDF文件，提取重大关联交易表格
    """
    # 定义SH目录路径
    sh_dir = r'.\SINA\000158'
    
    # 检查SH目录是否存在
    if not os.path.exists(sh_dir):
        logger.error(f'SH目录不存在: {sh_dir}')
        return
    
    # 创建SH目录（如果不存在）
    if not os.path.exists(sh_dir):
        os.makedirs(sh_dir)
        logger.info(f'创建SH目录: {sh_dir}')
    
    # 查找SH目录下的所有包含"年报"的PDF文件
    all_pdfs = []
    # 递归遍历所有子目录
    for root, dirs, files in os.walk(sh_dir):
        # 过滤掉隐藏目录
        dirs[:] = [d for d in dirs if not d.startswith('.')]
        for file in files:
            if file.endswith('.pdf') and '2023年年度报告' in file:
                all_pdfs.append(os.path.join(root, file))
    
    if not all_pdfs:
        logger.warning(f'未找到包含"年度报告"的PDF文件，目录: {sh_dir}')
        return

    logger.info(f'找到 {len(all_pdfs)} 个原始年报PDF文件')

    # 按股票代码筛选2023年的年报
    pdfs_2023 = {}
    for pdf_path in all_pdfs:
        file_name = os.path.basename(pdf_path)
        # 尝试从文件名或路径中提取代码和年份
        code_match = re.search(r'(\d{6})', pdf_path) # 尝试从路径中获取代码
        if not code_match:
             code_match = re.search(r'(\d{6})', file_name) # 尝试从文件名获取代码

        year_match = re.search(r'(20\d{2})年', file_name)

        if code_match and year_match:
            stock_code = code_match.group(1)
            report_year = year_match.group(1)
            if report_year == '2023':
                # 如果同一代码已有2023年报，选择覆盖（假设每个代码只有一个明确的2023年报）
                pdfs_2023[stock_code] = pdf_path
        else:
            logger.warning(f"无法从文件名或路径提取代码或年份: {pdf_path}")

    filtered_pdfs = list(pdfs_2023.values())

    if not filtered_pdfs:
        logger.warning(f'在 {sh_dir} 目录及其子目录下未找到任何明确标记为2023年的年报PDF文件')
        return

    logger.info(f'筛选后，将处理 {len(filtered_pdfs)} 个2023年报PDF文件')


    # 提取所有PDF文件中的重大关联交易表格
    all_dfs = []
    processed_count = 0
    start_time = time.time()
    # 修改循环以使用筛选后的列表
    for pdf_path in filtered_pdfs:
        try:
            # 更新日志中的总文件数
            logger.info(f'处理文件 ({processed_count+1}/{len(filtered_pdfs)}): {pdf_path}')
            dfs = extract_related_transactions_from_pdf(pdf_path)

            if dfs:
                all_dfs.extend(dfs)

            processed_count += 1
            # 更新进度日志中的总文件数
            if processed_count % 5 == 0 or processed_count == len(filtered_pdfs):
                elapsed_time = time.time() - start_time
                # 避免除以零错误
                avg_time = elapsed_time / processed_count if processed_count > 0 else 0
                remaining = (len(filtered_pdfs) - processed_count) * avg_time
                # 确保剩余时间不为负
                remaining = max(0, remaining)
                # 安全计算百分比
                percentage = (processed_count / len(filtered_pdfs) * 100) if len(filtered_pdfs) > 0 else 0
                logger.info(f'已处理 {processed_count}/{len(filtered_pdfs)} 个文件 ({percentage:.1f}%)，'
                            f'预计剩余时间: {remaining/60:.1f}分钟')
        except Exception as e:
            logger.error(f'处理文件失败: {str(e)}，文件：{pdf_path}')
            # 即使失败也增加计数器以反映尝试并更新进度
            processed_count += 1
    
    # 合并所有表格并保存到Excel
    if all_dfs:
        try:
            # 合并所有DataFrame
            combined_df = pd.concat(all_dfs, ignore_index=True)
            
            # 保存到Excel
            output_path = r'.\SH\重大关联交易汇总.xlsx'
            combined_df.to_excel(output_path, index=False)
            
            logger.info(f'成功提取 {len(all_dfs)} 个表格，合并后保存到: {output_path}')
            print(f'成功提取 {len(all_dfs)} 个表格，合并后保存到: {output_path}')
        except Exception as e:
            logger.error(f'保存Excel文件失败: {str(e)}')
            print(f'保存Excel文件失败: {str(e)}')
    else:
        logger.warning('未能提取到任何重大关联交易表格')
        print('未能提取到任何重大关联交易表格')


if __name__ == "__main__":
    try:
        process_sh_annual_reports()
    except Exception as e:
        logger.error(f'程序执行出错: {str(e)}')
        print(f'程序执行出错: {str(e)}')
