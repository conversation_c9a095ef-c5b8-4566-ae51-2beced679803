// Simple script to test if Vite is installed
const { execSync } = require('child_process');

try {
  console.log('Checking Vite installation...');
  execSync('npx vite --version', { stdio: 'inherit' });
  console.log('Vite is installed!');
} catch (error) {
  console.error('Vite is not installed. Installing now...');
  try {
    execSync('npm install @vitejs/plugin-react vite vite-plugin-env-compatible --save-dev', { stdio: 'inherit' });
    console.log('Vite and plugins installed successfully!');
  } catch (installError) {
    console.error('Failed to install Vite:', installError);
  }
}
