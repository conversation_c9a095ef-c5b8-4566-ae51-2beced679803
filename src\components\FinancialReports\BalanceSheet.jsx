import React from 'react';
import { Table } from 'antd';

/**
 * 资产负债表组件
 */
const BalanceSheet = ({ data = [] }) => {
  // 使用从props传入的数据，如果未提供则使用空数组
  const displayData = Array.isArray(data) && data.length > 0 ? data : [
    // 保留一些默认结构或提示信息，以防数据为空
    {
      key: 'empty',
      item: '暂无数据',
      currentAmount: '',
      previousAmount: '',
      level: 0,
      rowType: 'header'
    }
  ];



  const columns = [
    {
      title: '项目',
      dataIndex: 'item',
      key: 'item',
      width: '50%',
      render: (text, record) => {
        let className = 'balance-sheet-item';
        if (record.rowType === 'header') {
          className += ' header-item';
        } else if (record.rowType === 'subtotal') {
          className += ' subtotal-item';
        } else if (record.rowType === 'total') {
          className += ' total-item';
        }
        
        // 动态计算缩进，每个level使用4个空格
        const indent = '    '.repeat(Math.max(0, record.level || 0));
        // 对标题行(header)和一级标题(level=0)加粗
        const fontWeight = (record.level === 0 || record.rowType === 'header') ? 'bold' : 'normal';
        
        return (
          <span className={className} style={{ fontWeight }}>
            {indent}{text}
          </span>
        );
      }
    },
    {
      title: '本年金额',
      dataIndex: 'currentAmount',
      key: 'currentAmount',
      width: '25%',
      align: 'right',
      render: (text, record) => {
        let className = 'balance-sheet-amount';
        if (record.rowType === 'header') {
          className += ' header-amount';
        } else if (record.rowType === 'subtotal') {
          className += ' subtotal-amount';
        } else if (record.rowType === 'total') {
          className += ' total-amount';
        }
        
        return (
          <span className={className}>
            {text}
          </span>
        );
      }
    },
    {
      title: '上年金额',
      dataIndex: 'previousAmount',
      key: 'previousAmount',
      width: '25%',
      align: 'right',
      render: (text, record) => {
        let className = 'balance-sheet-amount';
        if (record.rowType === 'header') {
          className += ' header-amount';
        } else if (record.rowType === 'subtotal') {
          className += ' subtotal-amount';
        } else if (record.rowType === 'total') {
          className += ' total-amount';
        }
        
        return (
          <span className={className}>
            {text}
          </span>
        );
      }
    }
  ];

  return (
    <div className="balance-sheet">
      <Table
        columns={columns}
        dataSource={displayData}
        pagination={false}
        bordered
        size="small"
        className="balance-sheet-table"
        rowClassName={(record) => {
          if (record.rowType === 'header') return 'header-row';
          if (record.rowType === 'subtotal') return 'subtotal-row';
          if (record.rowType === 'total') return 'total-row';
          if (record.level === 1 && record.rowType !== 'header' && record.rowType !== 'subtotal' && record.rowType !== 'total') return 'sub-row';
          return '';
        }}
      />
    </div>
  );
};

export default BalanceSheet;
