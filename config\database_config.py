#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库配置模块
提供数据库连接功能
"""

import pyodbc
from .settings import config
import os

def get_db_connection():
    """
    获取数据库连接
    
    Returns:
        pyodbc.Connection: 数据库连接对象
    """
    try:
        # 获取当前环境配置
        env = os.environ.get('FLASK_ENV', 'development')
        current_config = config.get(env, config['default'])
        db_config = current_config.DB_CONFIG
        
        # 构建连接字符串
        connection_string = (
            f'DRIVER={{{db_config["driver"]}}};'
            f'SERVER={db_config["server"]};'
            f'DATABASE={db_config["database"]};'
            f'UID={db_config["user"]};'
            f'PWD={db_config["password"]};' 
        )
        
        # 建立连接
        connection = pyodbc.connect(connection_string)
        return connection
        
    except Exception as e:
        raise Exception(f"数据库连接失败: {e}")
