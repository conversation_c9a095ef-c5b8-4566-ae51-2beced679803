#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
部署包创建脚本
将EXE文件和必要的配置文件打包成完整的部署包
"""

import os
import shutil
from datetime import datetime

def create_deployment_package():
    """创建完整的部署包"""
    print("=" * 60)
    print("创建部署包")
    print("=" * 60)
    
    # 创建部署目录
    deployment_dir = "integrated_processor_deployment"
    if os.path.exists(deployment_dir):
        shutil.rmtree(deployment_dir)
    os.makedirs(deployment_dir)
    
    # 复制EXE文件
    exe_source = os.path.join("dist", "integrated_processor.exe")
    exe_dest = os.path.join(deployment_dir, "integrated_processor.exe")
    if os.path.exists(exe_source):
        shutil.copy2(exe_source, exe_dest)
        print(f"✓ 复制EXE文件: {exe_dest}")
    else:
        print(f"✗ EXE文件不存在: {exe_source}")
        return False
    
    # 复制使用说明
    guide_source = os.path.join("dist", "使用说明.txt")
    guide_dest = os.path.join(deployment_dir, "使用说明.txt")
    if os.path.exists(guide_source):
        shutil.copy2(guide_source, guide_dest)
        print(f"✓ 复制使用说明: {guide_dest}")
    
    # 复制数据库目录
    if os.path.exists("database"):
        db_dest = os.path.join(deployment_dir, "database")
        shutil.copytree("database", db_dest)
        print(f"✓ 复制数据库目录: {db_dest}")
    
    # 复制配置文件
    config_files = [
        "logging_config.py",
        "config.py"
    ]
    
    for config_file in config_files:
        if os.path.exists(config_file):
            shutil.copy2(config_file, deployment_dir)
            print(f"✓ 复制配置文件: {config_file}")
    
    # 复制SQL脚本
    sql_files = [
        "create_l2metrics_tables.sql",
        "create_metrics_tables.sql"
    ]
    
    for sql_file in sql_files:
        if os.path.exists(sql_file):
            shutil.copy2(sql_file, deployment_dir)
            print(f"✓ 复制SQL脚本: {sql_file}")
    
    # 创建启动脚本
    create_batch_files(deployment_dir)
    
    # 创建README文件
    create_readme(deployment_dir)
    
    print(f"\n✓ 部署包创建完成: {deployment_dir}")
    return True

def create_batch_files(deployment_dir):
    """创建批处理文件"""
    
    # 创建快速启动脚本
    quick_start_content = """@echo off
chcp 65001 >nul
echo 整合数据处理程序 - 快速启动
echo ================================
echo.
echo 正在启动程序...
integrated_processor.exe
echo.
echo 程序执行完成，按任意键退出...
pause >nul
"""
    
    quick_start_path = os.path.join(deployment_dir, "快速启动.bat")
    with open(quick_start_path, 'w', encoding='utf-8') as f:
        f.write(quick_start_content)
    print(f"✓ 创建快速启动脚本: {quick_start_path}")
    
    # 创建检查模式脚本
    check_only_content = """@echo off
chcp 65001 >nul
echo 整合数据处理程序 - 检查模式
echo ================================
echo.
echo 正在检查未处理数据...
integrated_processor.exe --check-only
echo.
echo 检查完成，按任意键退出...
pause >nul
"""
    
    check_only_path = os.path.join(deployment_dir, "检查模式.bat")
    with open(check_only_path, 'w', encoding='utf-8') as f:
        f.write(check_only_content)
    print(f"✓ 创建检查模式脚本: {check_only_path}")
    
    # 创建帮助脚本
    help_content = """@echo off
chcp 65001 >nul
echo 整合数据处理程序 - 帮助信息
echo ================================
echo.
integrated_processor.exe --help
echo.
echo 按任意键退出...
pause >nul
"""
    
    help_path = os.path.join(deployment_dir, "帮助信息.bat")
    with open(help_path, 'w', encoding='utf-8') as f:
        f.write(help_content)
    print(f"✓ 创建帮助脚本: {help_path}")

def create_readme(deployment_dir):
    """创建README文件"""
    readme_content = f"""整合数据处理程序部署包
===============================

版本: 1.0
构建时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

文件说明:
- integrated_processor.exe: 主程序
- 使用说明.txt: 详细使用说明
- 快速启动.bat: 快速启动脚本
- 检查模式.bat: 检查未处理数据
- 帮助信息.bat: 显示帮助信息
- database/: 数据库相关文件
- *.py: 配置文件
- *.sql: 数据库脚本

使用步骤:
1. 确保Wind金融终端已启动
2. 确保数据库连接正常
3. 双击"快速启动.bat"或"检查模式.bat"
4. 或直接运行 integrated_processor.exe

注意事项:
- 首次运行可能需要较长时间
- 建议先使用检查模式查看数据状态
- 如遇问题，请查看日志文件

技术支持:
如有问题，请联系技术支持。
"""
    
    readme_path = os.path.join(deployment_dir, "README.txt")
    with open(readme_path, 'w', encoding='utf-8') as f:
        f.write(readme_content)
    print(f"✓ 创建README文件: {readme_path}")

def main():
    """主函数"""
    if create_deployment_package():
        print("\n部署包创建成功!")
        print("您可以将整个部署目录复制到目标机器上使用。")
    else:
        print("\n部署包创建失败!")
        return False
    
    return True

if __name__ == "__main__":
    main() 