[{"key": "1", "item": "一、营业总收入", "currentAmount": "10,705,618,162.99", "previousAmount": "10,000,885,260.86", "level": 0, "rowType": "normal", "sheetName": "利润表", "reportType": "利润表", "originalCurrentAmount": "10705618162.99", "originalPreviousAmount": "10000885260.86", "matchedMetrics": [{"name": "营业总收入", "code": "营业总收入", "description": "营业总收入", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "tot_oper_rev"}], "windcode": "tot_oper_rev", "windcodes": ["tot_oper_rev"], "windcodeDetails": [{"windcode": "tot_oper_rev", "metricName": "营业总收入", "metricCode": "营业总收入", "description": "营业总收入", "unit": "", "reportType": "利润表"}], "matchCount": 1, "hasWindCode": true}, {"key": "2", "item": "其中：营业收入", "currentAmount": "10,705,618,162.99", "previousAmount": "10,000,885,260.86", "level": 0, "rowType": "normal", "sheetName": "利润表", "reportType": "利润表", "originalCurrentAmount": "10705618162.99", "originalPreviousAmount": "10000885260.86", "matchedMetrics": [{"name": "营业收入", "code": "营业收入", "description": "营业收入", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "oper_rev"}], "windcode": "oper_rev", "windcodes": ["oper_rev"], "windcodeDetails": [{"windcode": "oper_rev", "metricName": "营业收入", "metricCode": "营业收入", "description": "营业收入", "unit": "", "reportType": "利润表"}], "matchCount": 1, "hasWindCode": true}, {"key": "3", "item": "利息收入", "currentAmount": "", "previousAmount": "", "level": 0, "rowType": "normal", "sheetName": "利润表", "reportType": "利润表", "originalCurrentAmount": "", "originalPreviousAmount": "", "matchedMetrics": [{"name": "财务费用:利息收入", "code": "财务费用:利息收入", "description": "其中:利息收入", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "fin_int_inc"}, {"name": "利息收入", "code": "利息收入", "description": "利息收入", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "int_inc"}], "windcode": "fin_int_inc", "windcodes": ["fin_int_inc", "int_inc"], "windcodeDetails": [{"windcode": "fin_int_inc", "metricName": "财务费用:利息收入", "metricCode": "财务费用:利息收入", "description": "其中:利息收入", "unit": "", "reportType": "利润表"}, {"windcode": "int_inc", "metricName": "利息收入", "metricCode": "利息收入", "description": "利息收入", "unit": "", "reportType": "利润表"}], "matchCount": 2, "hasWindCode": true}, {"key": "4", "item": "已赚保费", "currentAmount": "", "previousAmount": "", "level": 0, "rowType": "normal", "sheetName": "利润表", "reportType": "利润表", "originalCurrentAmount": "", "originalPreviousAmount": "", "matchedMetrics": [{"name": "已赚保费", "code": "已赚保费", "description": "已赚保费", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "insur_prem_unearned"}], "windcode": "insur_prem_unearned", "windcodes": ["insur_prem_unearned"], "windcodeDetails": [{"windcode": "insur_prem_unearned", "metricName": "已赚保费", "metricCode": "已赚保费", "description": "已赚保费", "unit": "", "reportType": "利润表"}], "matchCount": 1, "hasWindCode": true}, {"key": "5", "item": "手续费及佣金收入", "currentAmount": "", "previousAmount": "", "level": 0, "rowType": "normal", "sheetName": "利润表", "reportType": "利润表", "originalCurrentAmount": "", "originalPreviousAmount": "", "matchedMetrics": [{"name": "手续费及佣金收入", "code": "手续费及佣金收入", "description": "手续费及佣金收入", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "handling_chrg_comm_inc"}], "windcode": "handling_chrg_comm_inc", "windcodes": ["handling_chrg_comm_inc"], "windcodeDetails": [{"windcode": "handling_chrg_comm_inc", "metricName": "手续费及佣金收入", "metricCode": "手续费及佣金收入", "description": "手续费及佣金收入", "unit": "", "reportType": "利润表"}], "matchCount": 1, "hasWindCode": true}, {"key": "6", "item": "二、营业总成本", "currentAmount": "10,115,258,932.70", "previousAmount": "9,426,044,597.16", "level": 0, "rowType": "normal", "sheetName": "利润表", "reportType": "利润表", "originalCurrentAmount": "10115258932.7", "originalPreviousAmount": "9426044597.16", "matchedMetrics": [{"name": "营业总成本", "code": "营业总成本", "description": "营业总成本234", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "tot_oper_cost"}], "windcode": "tot_oper_cost", "windcodes": ["tot_oper_cost"], "windcodeDetails": [{"windcode": "tot_oper_cost", "metricName": "营业总成本", "metricCode": "营业总成本", "description": "营业总成本234", "unit": "", "reportType": "利润表"}], "matchCount": 1, "hasWindCode": true}, {"key": "7", "item": "其中：营业成本", "currentAmount": "8,651,338,957.20", "previousAmount": "7,969,070,270.54", "level": 0, "rowType": "normal", "sheetName": "利润表", "reportType": "利润表", "originalCurrentAmount": "8651338957.2", "originalPreviousAmount": "7969070270.54", "matchedMetrics": [{"name": "营业成本", "code": "营业成本", "description": "营业成本", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "oper_cost"}], "windcode": "oper_cost", "windcodes": ["oper_cost"], "windcodeDetails": [{"windcode": "oper_cost", "metricName": "营业成本", "metricCode": "营业成本", "description": "营业成本", "unit": "", "reportType": "利润表"}], "matchCount": 1, "hasWindCode": true}, {"key": "8", "item": "利息支出", "currentAmount": "", "previousAmount": "", "level": 0, "rowType": "normal", "sheetName": "利润表", "reportType": "利润表", "originalCurrentAmount": "", "originalPreviousAmount": "", "matchedMetrics": [{"name": "利息支出", "code": "利息支出", "description": "利息支出", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "int_exp"}], "windcode": "int_exp", "windcodes": ["int_exp"], "windcodeDetails": [{"windcode": "int_exp", "metricName": "利息支出", "metricCode": "利息支出", "description": "利息支出", "unit": "", "reportType": "利润表"}], "matchCount": 1, "hasWindCode": true}, {"key": "9", "item": "手续费及佣金支出", "currentAmount": "", "previousAmount": "", "level": 0, "rowType": "normal", "sheetName": "利润表", "reportType": "利润表", "originalCurrentAmount": "", "originalPreviousAmount": "", "matchedMetrics": [{"name": "手续费及佣金支出", "code": "手续费及佣金支出", "description": "手续费及佣金支出", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "handling_chrg_comm_exp"}], "windcode": "handling_chrg_comm_exp", "windcodes": ["handling_chrg_comm_exp"], "windcodeDetails": [{"windcode": "handling_chrg_comm_exp", "metricName": "手续费及佣金支出", "metricCode": "手续费及佣金支出", "description": "手续费及佣金支出", "unit": "", "reportType": "利润表"}], "matchCount": 1, "hasWindCode": true}, {"key": "10", "item": "退保金", "currentAmount": "", "previousAmount": "", "level": 0, "rowType": "normal", "sheetName": "利润表", "reportType": "利润表", "originalCurrentAmount": "", "originalPreviousAmount": "", "matchedMetrics": [{"name": "退保金", "code": "退保金", "description": "退保金", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "prepay_surr"}], "windcode": "prepay_surr", "windcodes": ["prepay_surr"], "windcodeDetails": [{"windcode": "prepay_surr", "metricName": "退保金", "metricCode": "退保金", "description": "退保金", "unit": "", "reportType": "利润表"}], "matchCount": 1, "hasWindCode": true}, {"key": "11", "item": "赔付支出净额", "currentAmount": "", "previousAmount": "", "level": 0, "rowType": "normal", "sheetName": "利润表", "reportType": "利润表", "originalCurrentAmount": "", "originalPreviousAmount": "", "matchedMetrics": [{"name": "赔付支出净额", "code": "赔付支出净额", "description": "赔付支出净额", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "net_claim_exp"}], "windcode": "net_claim_exp", "windcodes": ["net_claim_exp"], "windcodeDetails": [{"windcode": "net_claim_exp", "metricName": "赔付支出净额", "metricCode": "赔付支出净额", "description": "赔付支出净额", "unit": "", "reportType": "利润表"}], "matchCount": 1, "hasWindCode": true}, {"key": "12", "item": "提取保险责任准备金净额", "currentAmount": "", "previousAmount": "", "level": 0, "rowType": "normal", "sheetName": "利润表", "reportType": "利润表", "originalCurrentAmount": "", "originalPreviousAmount": "", "matchedMetrics": [{"name": "提取保险责任准备金", "code": "提取保险责任准备金", "description": "提取保险责任准备金", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "net_insur_cont_rsrv"}], "windcode": "net_insur_cont_rsrv", "windcodes": ["net_insur_cont_rsrv"], "windcodeDetails": [{"windcode": "net_insur_cont_rsrv", "metricName": "提取保险责任准备金", "metricCode": "提取保险责任准备金", "description": "提取保险责任准备金", "unit": "", "reportType": "利润表"}], "matchCount": 1, "hasWindCode": true}, {"key": "13", "item": "保单红利支出", "currentAmount": "", "previousAmount": "", "level": 0, "rowType": "normal", "sheetName": "利润表", "reportType": "利润表", "originalCurrentAmount": "", "originalPreviousAmount": "", "matchedMetrics": [{"name": "保单红利支出", "code": "保单红利支出", "description": "保单红利支出", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "dvd_exp_insured"}], "windcode": "dvd_exp_insured", "windcodes": ["dvd_exp_insured"], "windcodeDetails": [{"windcode": "dvd_exp_insured", "metricName": "保单红利支出", "metricCode": "保单红利支出", "description": "保单红利支出", "unit": "", "reportType": "利润表"}], "matchCount": 1, "hasWindCode": true}, {"key": "14", "item": "分保费用", "currentAmount": "", "previousAmount": "", "level": 0, "rowType": "normal", "sheetName": "利润表", "reportType": "利润表", "originalCurrentAmount": "", "originalPreviousAmount": "", "matchedMetrics": [{"name": "摊回分保费用", "code": "摊回分保费用", "description": "摊回分保费用", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "reinsur_exp_recoverable"}, {"name": "分保费用", "code": "分保费用", "description": "分保费用", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "reinsurance_exp"}], "windcode": "reinsur_exp_recoverable", "windcodes": ["reinsur_exp_recoverable", "reinsurance_exp"], "windcodeDetails": [{"windcode": "reinsur_exp_recoverable", "metricName": "摊回分保费用", "metricCode": "摊回分保费用", "description": "摊回分保费用", "unit": "", "reportType": "利润表"}, {"windcode": "reinsurance_exp", "metricName": "分保费用", "metricCode": "分保费用", "description": "分保费用", "unit": "", "reportType": "利润表"}], "matchCount": 2, "hasWindCode": true}, {"key": "15", "item": "税金及附加", "currentAmount": "26,451,204.71", "previousAmount": "31,820,609.77", "level": 0, "rowType": "normal", "sheetName": "利润表", "reportType": "利润表", "originalCurrentAmount": "26451204.71", "originalPreviousAmount": "31820609.77", "matchedMetrics": [{"name": "税金及附加", "code": "税金及附加", "description": "税金及附加", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "taxes_surcharges_ops"}], "windcode": "taxes_surcharges_ops", "windcodes": ["taxes_surcharges_ops"], "windcodeDetails": [{"windcode": "taxes_surcharges_ops", "metricName": "税金及附加", "metricCode": "税金及附加", "description": "税金及附加", "unit": "", "reportType": "利润表"}], "matchCount": 1, "hasWindCode": true}, {"key": "16", "item": "销售费用", "currentAmount": "724,550,249.03", "previousAmount": "718,083,886.94", "level": 0, "rowType": "normal", "sheetName": "利润表", "reportType": "利润表", "originalCurrentAmount": "*********.03", "originalPreviousAmount": "*********.94", "matchedMetrics": [{"name": "差旅费(销售费用)", "code": "差旅费(销售费用)", "description": "差旅费(销售费用)", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "stmnote_others_7647"}, {"name": "租赁费(销售费用)", "code": "租赁费(销售费用)", "description": "租赁费(销售费用)", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "stmnote_others_7630"}, {"name": "工资薪酬(销售费用)", "code": "工资薪酬(销售费用)", "description": "工资薪酬(销售费用)", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "stmnote_others_7626"}, {"name": "折旧摊销(销售费用)", "code": "折旧摊销(销售费用)", "description": "折旧摊销(销售费用)", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "stmnote_others_7628"}, {"name": "销售费用", "code": "销售费用", "description": "销售费用", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "selling_dist_exp"}, {"name": "广告宣传推广费(销售费用)", "code": "广告宣传推广费(销售费用)", "description": "广告宣传推广费(销售费用)", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "stmnote_others_7633"}, {"name": "业务招待费(销售费用)", "code": "业务招待费(销售费用)", "description": "业务招待费(销售费用)", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "stmnote_others_7648"}, {"name": "仓储运输费(销售费用)", "code": "仓储运输费(销售费用)", "description": "仓储运输费(销售费用)", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "stmnote_others_7632"}], "windcode": "stmnote_others_7647", "windcodes": ["stmnote_others_7647", "stmnote_others_7630", "stmnote_others_7626", "stmnote_others_7628", "selling_dist_exp", "stmnote_others_7633", "stmnote_others_7648", "stmnote_others_7632"], "windcodeDetails": [{"windcode": "stmnote_others_7647", "metricName": "差旅费(销售费用)", "metricCode": "差旅费(销售费用)", "description": "差旅费(销售费用)", "unit": "", "reportType": "利润表"}, {"windcode": "stmnote_others_7630", "metricName": "租赁费(销售费用)", "metricCode": "租赁费(销售费用)", "description": "租赁费(销售费用)", "unit": "", "reportType": "利润表"}, {"windcode": "stmnote_others_7626", "metricName": "工资薪酬(销售费用)", "metricCode": "工资薪酬(销售费用)", "description": "工资薪酬(销售费用)", "unit": "", "reportType": "利润表"}, {"windcode": "stmnote_others_7628", "metricName": "折旧摊销(销售费用)", "metricCode": "折旧摊销(销售费用)", "description": "折旧摊销(销售费用)", "unit": "", "reportType": "利润表"}, {"windcode": "selling_dist_exp", "metricName": "销售费用", "metricCode": "销售费用", "description": "销售费用", "unit": "", "reportType": "利润表"}, {"windcode": "stmnote_others_7633", "metricName": "广告宣传推广费(销售费用)", "metricCode": "广告宣传推广费(销售费用)", "description": "广告宣传推广费(销售费用)", "unit": "", "reportType": "利润表"}, {"windcode": "stmnote_others_7648", "metricName": "业务招待费(销售费用)", "metricCode": "业务招待费(销售费用)", "description": "业务招待费(销售费用)", "unit": "", "reportType": "利润表"}, {"windcode": "stmnote_others_7632", "metricName": "仓储运输费(销售费用)", "metricCode": "仓储运输费(销售费用)", "description": "仓储运输费(销售费用)", "unit": "", "reportType": "利润表"}], "matchCount": 8, "hasWindCode": true}, {"key": "17", "item": "管理费用", "currentAmount": "269,138,408.43", "previousAmount": "287,758,629.31", "level": 0, "rowType": "normal", "sheetName": "利润表", "reportType": "利润表", "originalCurrentAmount": "269138408.43", "originalPreviousAmount": "287758629.31", "matchedMetrics": [{"name": "租赁费(管理费用)", "code": "租赁费(管理费用)", "description": "租赁费(管理费用)", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "stmnote_others_7631"}, {"name": "工资薪酬(管理费用)", "code": "工资薪酬(管理费用)", "description": "工资薪酬(管理费用)", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "stmnote_others_7627"}, {"name": "差旅费(管理费用)", "code": "差旅费(管理费用)", "description": "差旅费(管理费用)", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "stmnote_others_7635"}, {"name": "折旧摊销(管理费用)", "code": "折旧摊销(管理费用)", "description": "折旧摊销(管理费用)", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "stmnote_others_7629"}, {"name": "研发费用(管理费用)", "code": "研发费用(管理费用)", "description": "研发费用(管理费用)", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "stmnote_others_7646"}, {"name": "业务招待费(管理费用)", "code": "业务招待费(管理费用)", "description": "业务招待费(管理费用)", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "stmnote_others_7634"}, {"name": "管理费用", "code": "管理费用", "description": "管理费用", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "gerl_admin_exp"}], "windcode": "stmnote_others_7631", "windcodes": ["stmnote_others_7631", "stmnote_others_7627", "stmnote_others_7635", "stmnote_others_7629", "stmnote_others_7646", "stmnote_others_7634", "gerl_admin_exp"], "windcodeDetails": [{"windcode": "stmnote_others_7631", "metricName": "租赁费(管理费用)", "metricCode": "租赁费(管理费用)", "description": "租赁费(管理费用)", "unit": "", "reportType": "利润表"}, {"windcode": "stmnote_others_7627", "metricName": "工资薪酬(管理费用)", "metricCode": "工资薪酬(管理费用)", "description": "工资薪酬(管理费用)", "unit": "", "reportType": "利润表"}, {"windcode": "stmnote_others_7635", "metricName": "差旅费(管理费用)", "metricCode": "差旅费(管理费用)", "description": "差旅费(管理费用)", "unit": "", "reportType": "利润表"}, {"windcode": "stmnote_others_7629", "metricName": "折旧摊销(管理费用)", "metricCode": "折旧摊销(管理费用)", "description": "折旧摊销(管理费用)", "unit": "", "reportType": "利润表"}, {"windcode": "stmnote_others_7646", "metricName": "研发费用(管理费用)", "metricCode": "研发费用(管理费用)", "description": "研发费用(管理费用)", "unit": "", "reportType": "利润表"}, {"windcode": "stmnote_others_7634", "metricName": "业务招待费(管理费用)", "metricCode": "业务招待费(管理费用)", "description": "业务招待费(管理费用)", "unit": "", "reportType": "利润表"}, {"windcode": "gerl_admin_exp", "metricName": "管理费用", "metricCode": "管理费用", "description": "管理费用", "unit": "", "reportType": "利润表"}], "matchCount": 7, "hasWindCode": true}, {"key": "18", "item": "研发费用", "currentAmount": "438,272,805.12", "previousAmount": "438,689,111.86", "level": 0, "rowType": "normal", "sheetName": "利润表", "reportType": "利润表", "originalCurrentAmount": "438272805.12", "originalPreviousAmount": "438689111.86", "matchedMetrics": [{"name": "研发费用-工资薪酬", "code": "研发费用-工资薪酬", "description": "研发费用-工资薪酬", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "stmnote_rdsalary"}, {"name": "研发费用占营业收入比例", "code": "研发费用占营业收入比例", "description": "研发费用占营业收入比例", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "stmnote_Rdexp_costtosales"}, {"name": "研发费用-其他", "code": "研发费用-其他", "description": "研发费用-其他", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "stmnote_rdothers"}, {"name": "研发费用", "code": "研发费用", "description": "研发费用", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "rd_exp"}, {"name": "研发费用-折旧摊销", "code": "研发费用-折旧摊销", "description": "研发费用-折旧摊销", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "stmnote_rdda"}, {"name": "研发费用-直接投入", "code": "研发费用-直接投入", "description": "研发费用-直接投入", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "stmnote_rdinv"}, {"name": "研发费用(管理费用)", "code": "研发费用(管理费用)", "description": "研发费用(管理费用)", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "stmnote_others_7646"}, {"name": "研发费用-租赁费", "code": "研发费用-租赁费", "description": "研发费用-租赁费", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "stmnote_rdlease"}], "windcode": "stmnote_rdsalary", "windcodes": ["stmnote_rdsalary", "stmnote_Rdexp_costtosales", "stmnote_rdothers", "rd_exp", "stmnote_rdda", "stmnote_rdinv", "stmnote_others_7646", "stmnote_rdlease"], "windcodeDetails": [{"windcode": "stmnote_rdsalary", "metricName": "研发费用-工资薪酬", "metricCode": "研发费用-工资薪酬", "description": "研发费用-工资薪酬", "unit": "", "reportType": "利润表"}, {"windcode": "stmnote_Rdexp_costtosales", "metricName": "研发费用占营业收入比例", "metricCode": "研发费用占营业收入比例", "description": "研发费用占营业收入比例", "unit": "", "reportType": "利润表"}, {"windcode": "stmnote_rdothers", "metricName": "研发费用-其他", "metricCode": "研发费用-其他", "description": "研发费用-其他", "unit": "", "reportType": "利润表"}, {"windcode": "rd_exp", "metricName": "研发费用", "metricCode": "研发费用", "description": "研发费用", "unit": "", "reportType": "利润表"}, {"windcode": "stmnote_rdda", "metricName": "研发费用-折旧摊销", "metricCode": "研发费用-折旧摊销", "description": "研发费用-折旧摊销", "unit": "", "reportType": "利润表"}, {"windcode": "stmnote_rdinv", "metricName": "研发费用-直接投入", "metricCode": "研发费用-直接投入", "description": "研发费用-直接投入", "unit": "", "reportType": "利润表"}, {"windcode": "stmnote_others_7646", "metricName": "研发费用(管理费用)", "metricCode": "研发费用(管理费用)", "description": "研发费用(管理费用)", "unit": "", "reportType": "利润表"}, {"windcode": "stmnote_rdlease", "metricName": "研发费用-租赁费", "metricCode": "研发费用-租赁费", "description": "研发费用-租赁费", "unit": "", "reportType": "利润表"}], "matchCount": 8, "hasWindCode": true}, {"key": "19", "item": "财务费用", "currentAmount": "5,507,308.21", "previousAmount": "-19,377,911.26", "level": 0, "rowType": "normal", "sheetName": "利润表", "reportType": "利润表", "originalCurrentAmount": "5507308.21", "originalPreviousAmount": "-19377911.26", "matchedMetrics": [{"name": "财务费用:利息收入", "code": "财务费用:利息收入", "description": "其中:利息收入", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "fin_int_inc"}, {"name": "财务费用:利息费用", "code": "财务费用:利息费用", "description": "其中:利息费用", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "fin_int_exp"}, {"name": "财务费用_CS", "code": "财务费用_CS", "description": "财务费用_CS", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "fin_exp_cs"}, {"name": "财务费用", "code": "财务费用", "description": "财务费用", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "fin_exp_is"}, {"name": "财务费用明细-利息资本化金额", "code": "财务费用明细-利息资本化金额", "description": "财务费用明细-利息资本化金额", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "stmnote_finexp_13"}], "windcode": "fin_int_inc", "windcodes": ["fin_int_inc", "fin_int_exp", "fin_exp_cs", "fin_exp_is", "stmnote_finexp_13"], "windcodeDetails": [{"windcode": "fin_int_inc", "metricName": "财务费用:利息收入", "metricCode": "财务费用:利息收入", "description": "其中:利息收入", "unit": "", "reportType": "利润表"}, {"windcode": "fin_int_exp", "metricName": "财务费用:利息费用", "metricCode": "财务费用:利息费用", "description": "其中:利息费用", "unit": "", "reportType": "利润表"}, {"windcode": "fin_exp_cs", "metricName": "财务费用_CS", "metricCode": "财务费用_CS", "description": "财务费用_CS", "unit": "", "reportType": "利润表"}, {"windcode": "fin_exp_is", "metricName": "财务费用", "metricCode": "财务费用", "description": "财务费用", "unit": "", "reportType": "利润表"}, {"windcode": "stmnote_finexp_13", "metricName": "财务费用明细-利息资本化金额", "metricCode": "财务费用明细-利息资本化金额", "description": "财务费用明细-利息资本化金额", "unit": "", "reportType": "利润表"}], "matchCount": 5, "hasWindCode": true}, {"key": "20", "item": "其中：利息费用", "currentAmount": "29,299,691.22", "previousAmount": "16,132,918.02", "level": 0, "rowType": "normal", "sheetName": "利润表", "reportType": "利润表", "originalCurrentAmount": "29299691.22", "originalPreviousAmount": "16132918.02", "matchedMetrics": [], "windcode": null, "windcodes": [], "windcodeDetails": [], "matchCount": 0, "hasWindCode": false}, {"key": "21", "item": "利息收入", "currentAmount": "25,573,171.44", "previousAmount": "33,804,703.40", "level": 0, "rowType": "normal", "sheetName": "利润表", "reportType": "利润表", "originalCurrentAmount": "25573171.44", "originalPreviousAmount": "33804703.4", "matchedMetrics": [{"name": "财务费用:利息收入", "code": "财务费用:利息收入", "description": "其中:利息收入", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "fin_int_inc"}, {"name": "利息收入", "code": "利息收入", "description": "利息收入", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "int_inc"}], "windcode": "fin_int_inc", "windcodes": ["fin_int_inc", "int_inc"], "windcodeDetails": [{"windcode": "fin_int_inc", "metricName": "财务费用:利息收入", "metricCode": "财务费用:利息收入", "description": "其中:利息收入", "unit": "", "reportType": "利润表"}, {"windcode": "int_inc", "metricName": "利息收入", "metricCode": "利息收入", "description": "利息收入", "unit": "", "reportType": "利润表"}], "matchCount": 2, "hasWindCode": true}, {"key": "22", "item": "加：其他收益", "currentAmount": "50,650,569.64", "previousAmount": "40,774,427.76", "level": 0, "rowType": "normal", "sheetName": "利润表", "reportType": "利润表", "originalCurrentAmount": "50650569.64", "originalPreviousAmount": "40774427.76", "matchedMetrics": [{"name": "其他", "code": "其他", "description": "其他", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "others"}, {"name": "其他收益", "code": "其他收益", "description": "其他收益", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "other_grants_inc"}], "windcode": "others", "windcodes": ["others", "other_grants_inc"], "windcodeDetails": [{"windcode": "others", "metricName": "其他", "metricCode": "其他", "description": "其他", "unit": "", "reportType": "利润表"}, {"windcode": "other_grants_inc", "metricName": "其他收益", "metricCode": "其他收益", "description": "其他收益", "unit": "", "reportType": "利润表"}], "matchCount": 2, "hasWindCode": true}, {"key": "23", "item": "投资收益（损失以“－”号填列）", "currentAmount": "747,669.24", "previousAmount": "-2,459,130.61", "level": 0, "rowType": "normal", "sheetName": "利润表", "reportType": "利润表", "originalCurrentAmount": "747669.24", "originalPreviousAmount": "-2459130.61", "matchedMetrics": [], "windcode": null, "windcodes": [], "windcodeDetails": [], "matchCount": 0, "hasWindCode": false}, {"key": "24", "item": "其中：对联营企业和合营企业的投资收益", "currentAmount": "857,048.67", "previousAmount": "1,934,886.58", "level": 0, "rowType": "normal", "sheetName": "利润表", "reportType": "利润表", "originalCurrentAmount": "857048.67", "originalPreviousAmount": "1934886.58", "matchedMetrics": [{"name": "对联营企业和合营企业的投资收益", "code": "对联营企业和合营企业的投资收益", "description": "对联营企业和合营企业的投资收益", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "inc_invest_assoc_jv_entp"}], "windcode": "inc_invest_assoc_jv_entp", "windcodes": ["inc_invest_assoc_jv_entp"], "windcodeDetails": [{"windcode": "inc_invest_assoc_jv_entp", "metricName": "对联营企业和合营企业的投资收益", "metricCode": "对联营企业和合营企业的投资收益", "description": "对联营企业和合营企业的投资收益", "unit": "", "reportType": "利润表"}], "matchCount": 1, "hasWindCode": true}, {"key": "25", "item": "以摊余成本计量的金融资产终止确认收 益", "currentAmount": "", "previousAmount": "", "level": 0, "rowType": "normal", "sheetName": "利润表", "reportType": "利润表", "originalCurrentAmount": "", "originalPreviousAmount": "", "matchedMetrics": [{"name": "以摊余成本计量的金融资产终止确认收益", "code": "以摊余成本计量的金融资产终止确认收益", "description": "以摊余成本计量的金融资产终止确认收益", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "ter_fin_ass_income"}, {"name": "以摊余成本计量的金融资产", "code": "以摊余成本计量的金融资产", "description": "以摊余成本计量的金融资产", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "fin_assets_amortizedcost"}], "windcode": "ter_fin_ass_income", "windcodes": ["ter_fin_ass_income", "fin_assets_amortizedcost"], "windcodeDetails": [{"windcode": "ter_fin_ass_income", "metricName": "以摊余成本计量的金融资产终止确认收益", "metricCode": "以摊余成本计量的金融资产终止确认收益", "description": "以摊余成本计量的金融资产终止确认收益", "unit": "", "reportType": "利润表"}, {"windcode": "fin_assets_amortizedcost", "metricName": "以摊余成本计量的金融资产", "metricCode": "以摊余成本计量的金融资产", "description": "以摊余成本计量的金融资产", "unit": "", "reportType": "利润表"}], "matchCount": 2, "hasWindCode": true}, {"key": "26", "item": "汇兑收益（损失以“－”号填列）", "currentAmount": "", "previousAmount": "", "level": 0, "rowType": "normal", "sheetName": "利润表", "reportType": "利润表", "originalCurrentAmount": "", "originalPreviousAmount": "", "matchedMetrics": [], "windcode": null, "windcodes": [], "windcodeDetails": [], "matchCount": 0, "hasWindCode": false}, {"key": "27", "item": "净敞口套期收益（损失以“- ”号填列）", "currentAmount": "", "previousAmount": "", "level": 0, "rowType": "normal", "sheetName": "利润表", "reportType": "利润表", "originalCurrentAmount": "", "originalPreviousAmount": "", "matchedMetrics": [{"name": "净敞口套期收益", "code": "净敞口套期收益", "description": "净敞口套期收益", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "net_exposure_hedge_ben"}], "windcode": "net_exposure_hedge_ben", "windcodes": ["net_exposure_hedge_ben"], "windcodeDetails": [{"windcode": "net_exposure_hedge_ben", "metricName": "净敞口套期收益", "metricCode": "净敞口套期收益", "description": "净敞口套期收益", "unit": "", "reportType": "利润表"}], "matchCount": 1, "hasWindCode": true}, {"key": "28", "item": "公允价值变动收益（损失以“－”号填列）", "currentAmount": "", "previousAmount": "", "level": 0, "rowType": "normal", "sheetName": "利润表", "reportType": "利润表", "originalCurrentAmount": "", "originalPreviousAmount": "", "matchedMetrics": [], "windcode": null, "windcodes": [], "windcodeDetails": [], "matchCount": 0, "hasWindCode": false}, {"key": "29", "item": "信用减值损失（损失以“- ”号填列）", "currentAmount": "-42,423,501.23", "previousAmount": "-35,485,058.70", "level": 0, "rowType": "normal", "sheetName": "利润表", "reportType": "利润表", "originalCurrentAmount": "-42423501.23", "originalPreviousAmount": "-35485058.7", "matchedMetrics": [{"name": "信用减值损失", "code": "信用减值损失", "description": "信用减值损失", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "credit_impair_loss"}], "windcode": "credit_impair_loss", "windcodes": ["credit_impair_loss"], "windcodeDetails": [{"windcode": "credit_impair_loss", "metricName": "信用减值损失", "metricCode": "信用减值损失", "description": "信用减值损失", "unit": "", "reportType": "利润表"}], "matchCount": 1, "hasWindCode": true}, {"key": "30", "item": "资产减值损失（损失以“- ”号填列）", "currentAmount": "-9,218,712.54", "previousAmount": "-13,177,303.60", "level": 0, "rowType": "normal", "sheetName": "利润表", "reportType": "利润表", "originalCurrentAmount": "-9218712.54", "originalPreviousAmount": "-13177303.6", "matchedMetrics": [{"name": "资产减值损失", "code": "资产减值损失", "description": "资产减值损失(新)", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "impair_loss_assets"}], "windcode": "impair_loss_assets", "windcodes": ["impair_loss_assets"], "windcodeDetails": [{"windcode": "impair_loss_assets", "metricName": "资产减值损失", "metricCode": "资产减值损失", "description": "资产减值损失(新)", "unit": "", "reportType": "利润表"}], "matchCount": 1, "hasWindCode": true}, {"key": "31", "item": "资产处置收益（损失以“－”号填列）", "currentAmount": "91,562.30", "previousAmount": "3,792,143.91", "level": 0, "rowType": "normal", "sheetName": "利润表", "reportType": "利润表", "originalCurrentAmount": "91562.3", "originalPreviousAmount": "3792143.91", "matchedMetrics": [{"name": "资产处置收益", "code": "资产处置收益", "description": "资产处置收益", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "gain_asset_dispositions"}], "windcode": "gain_asset_dispositions", "windcodes": ["gain_asset_dispositions"], "windcodeDetails": [{"windcode": "gain_asset_dispositions", "metricName": "资产处置收益", "metricCode": "资产处置收益", "description": "资产处置收益", "unit": "", "reportType": "利润表"}], "matchCount": 1, "hasWindCode": true}, {"key": "32", "item": "三、营业利润（亏损以“－”号填列）", "currentAmount": "590,206,817.70", "previousAmount": "568,285,742.46", "level": 0, "rowType": "normal", "sheetName": "利润表", "reportType": "利润表", "originalCurrentAmount": "590206817.7", "originalPreviousAmount": "568285742.46", "matchedMetrics": [{"name": "营业利润", "code": "营业利润", "description": "营业利润", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "opprofit"}], "windcode": "opprofit", "windcodes": ["opprofit"], "windcodeDetails": [{"windcode": "opprofit", "metricName": "营业利润", "metricCode": "营业利润", "description": "营业利润", "unit": "", "reportType": "利润表"}], "matchCount": 1, "hasWindCode": true}, {"key": "33", "item": "加：营业外收入", "currentAmount": "2,117,571.95", "previousAmount": "3,267,892.89", "level": 0, "rowType": "normal", "sheetName": "利润表", "reportType": "利润表", "originalCurrentAmount": "2117571.95", "originalPreviousAmount": "3267892.89", "matchedMetrics": [{"name": "营业外收入", "code": "营业外收入", "description": "营业外收入", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "non_oper_rev"}], "windcode": "non_oper_rev", "windcodes": ["non_oper_rev"], "windcodeDetails": [{"windcode": "non_oper_rev", "metricName": "营业外收入", "metricCode": "营业外收入", "description": "营业外收入", "unit": "", "reportType": "利润表"}], "matchCount": 1, "hasWindCode": true}, {"key": "34", "item": "减：营业外支出", "currentAmount": "2,924,488.55", "previousAmount": "928,271.64", "level": 0, "rowType": "normal", "sheetName": "利润表", "reportType": "利润表", "originalCurrentAmount": "2924488.55", "originalPreviousAmount": "928271.64", "matchedMetrics": [{"name": "营业外支出", "code": "营业外支出", "description": "营业外支出", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "non_oper_exp"}], "windcode": "non_oper_exp", "windcodes": ["non_oper_exp"], "windcodeDetails": [{"windcode": "non_oper_exp", "metricName": "营业外支出", "metricCode": "营业外支出", "description": "营业外支出", "unit": "", "reportType": "利润表"}], "matchCount": 1, "hasWindCode": true}, {"key": "35", "item": "四、利润总额（亏损总额以“ － ”号填列）", "currentAmount": "589,399,901.10", "previousAmount": "570,625,363.71", "level": 0, "rowType": "normal", "sheetName": "利润表", "reportType": "利润表", "originalCurrentAmount": "589399901.1", "originalPreviousAmount": "570625363.71", "matchedMetrics": [{"name": "利润总额", "code": "利润总额", "description": "利润总额", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "tot_profit"}], "windcode": "tot_profit", "windcodes": ["tot_profit"], "windcodeDetails": [{"windcode": "tot_profit", "metricName": "利润总额", "metricCode": "利润总额", "description": "利润总额", "unit": "", "reportType": "利润表"}], "matchCount": 1, "hasWindCode": true}, {"key": "36", "item": "减：所得税费用", "currentAmount": "43,598,657.62", "previousAmount": "47,980,760.27", "level": 0, "rowType": "normal", "sheetName": "利润表", "reportType": "利润表", "originalCurrentAmount": "43598657.62", "originalPreviousAmount": "47980760.27", "matchedMetrics": [{"name": "所得税", "code": "所得税", "description": "所得税", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "tax"}], "windcode": "tax", "windcodes": ["tax"], "windcodeDetails": [{"windcode": "tax", "metricName": "所得税", "metricCode": "所得税", "description": "所得税", "unit": "", "reportType": "利润表"}], "matchCount": 1, "hasWindCode": true}, {"key": "37", "item": "五、净利润（净亏损以“ － ”号填列）", "currentAmount": "545,801,243.48", "previousAmount": "522,644,603.44", "level": 0, "rowType": "normal", "sheetName": "利润表", "reportType": "利润表", "originalCurrentAmount": "*********.48", "originalPreviousAmount": "*********.44", "matchedMetrics": [{"name": "净利润", "code": "净利润", "description": "净利润", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "net_profit_is"}], "windcode": "net_profit_is", "windcodes": ["net_profit_is"], "windcodeDetails": [{"windcode": "net_profit_is", "metricName": "净利润", "metricCode": "净利润", "description": "净利润", "unit": "", "reportType": "利润表"}], "matchCount": 1, "hasWindCode": true}, {"key": "38", "item": "（一）按经营持续性分类", "currentAmount": "", "previousAmount": "", "level": 0, "rowType": "normal", "sheetName": "利润表", "reportType": "利润表", "originalCurrentAmount": "", "originalPreviousAmount": "", "matchedMetrics": [], "windcode": null, "windcodes": [], "windcodeDetails": [], "matchCount": 0, "hasWindCode": false}, {"key": "39", "item": "1.持续经营净利润（净亏损以“ － ”号填列）", "currentAmount": "545,801,243.48", "previousAmount": "522,644,603.44", "level": 0, "rowType": "normal", "sheetName": "利润表", "reportType": "利润表", "originalCurrentAmount": "*********.48", "originalPreviousAmount": "*********.44", "matchedMetrics": [{"name": "净利润", "code": "净利润", "description": "净利润", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "net_profit_is"}, {"name": "持续经营净利润", "code": "持续经营净利润", "description": "持续经营净利润", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "net_profit_continued"}], "windcode": "net_profit_is", "windcodes": ["net_profit_is", "net_profit_continued"], "windcodeDetails": [{"windcode": "net_profit_is", "metricName": "净利润", "metricCode": "净利润", "description": "净利润", "unit": "", "reportType": "利润表"}, {"windcode": "net_profit_continued", "metricName": "持续经营净利润", "metricCode": "持续经营净利润", "description": "持续经营净利润", "unit": "", "reportType": "利润表"}], "matchCount": 2, "hasWindCode": true}, {"key": "40", "item": "2.终止经营净利润（净亏损以“ － ”号填列）", "currentAmount": "", "previousAmount": "", "level": 0, "rowType": "normal", "sheetName": "利润表", "reportType": "利润表", "originalCurrentAmount": "", "originalPreviousAmount": "", "matchedMetrics": [{"name": "净利润", "code": "净利润", "description": "净利润", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "net_profit_is"}, {"name": "终止经营净利润", "code": "终止经营净利润", "description": "终止经营净利润", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "net_profit_discontinued"}], "windcode": "net_profit_is", "windcodes": ["net_profit_is", "net_profit_discontinued"], "windcodeDetails": [{"windcode": "net_profit_is", "metricName": "净利润", "metricCode": "净利润", "description": "净利润", "unit": "", "reportType": "利润表"}, {"windcode": "net_profit_discontinued", "metricName": "终止经营净利润", "metricCode": "终止经营净利润", "description": "终止经营净利润", "unit": "", "reportType": "利润表"}], "matchCount": 2, "hasWindCode": true}, {"key": "41", "item": "（二）按所有权归属分类", "currentAmount": "", "previousAmount": "", "level": 0, "rowType": "normal", "sheetName": "利润表", "reportType": "利润表", "originalCurrentAmount": "", "originalPreviousAmount": "", "matchedMetrics": [], "windcode": null, "windcodes": [], "windcodeDetails": [], "matchCount": 0, "hasWindCode": false}, {"key": "42", "item": "1.归属于母公司股东的净利润（净亏损以“ - ” 号填列）", "currentAmount": "550,560,962.28", "previousAmount": "497,037,442.92", "level": 0, "rowType": "normal", "sheetName": "利润表", "reportType": "利润表", "originalCurrentAmount": "550560962.28", "originalPreviousAmount": "497037442.92", "matchedMetrics": [{"name": "净利润", "code": "净利润", "description": "净利润", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "net_profit_is"}], "windcode": "net_profit_is", "windcodes": ["net_profit_is"], "windcodeDetails": [{"windcode": "net_profit_is", "metricName": "净利润", "metricCode": "净利润", "description": "净利润", "unit": "", "reportType": "利润表"}], "matchCount": 1, "hasWindCode": true}, {"key": "43", "item": "2.少数股东损益（净亏损以“- ”号填列）", "currentAmount": "-4,759,718.80", "previousAmount": "25,607,160.52", "level": 0, "rowType": "normal", "sheetName": "利润表", "reportType": "利润表", "originalCurrentAmount": "-4759718.8", "originalPreviousAmount": "25607160.52", "matchedMetrics": [{"name": "少数股东损益", "code": "少数股东损益", "description": "少数股东损益", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "minority_int_inc"}], "windcode": "minority_int_inc", "windcodes": ["minority_int_inc"], "windcodeDetails": [{"windcode": "minority_int_inc", "metricName": "少数股东损益", "metricCode": "少数股东损益", "description": "少数股东损益", "unit": "", "reportType": "利润表"}], "matchCount": 1, "hasWindCode": true}, {"key": "44", "item": "六、其他综合收益的税后净额", "currentAmount": "-169,794.76", "previousAmount": "5,777,891.36", "level": 0, "rowType": "normal", "sheetName": "利润表", "reportType": "利润表", "originalCurrentAmount": "-169794.76", "originalPreviousAmount": "5777891.36", "matchedMetrics": [{"name": "其他", "code": "其他", "description": "其他", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "others"}, {"name": "其他综合收益", "code": "其他综合收益", "description": "归属于母公司股东的其他综合收益", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "other_compreh_inc"}], "windcode": "others", "windcodes": ["others", "other_compreh_inc"], "windcodeDetails": [{"windcode": "others", "metricName": "其他", "metricCode": "其他", "description": "其他", "unit": "", "reportType": "利润表"}, {"windcode": "other_compreh_inc", "metricName": "其他综合收益", "metricCode": "其他综合收益", "description": "归属于母公司股东的其他综合收益", "unit": "", "reportType": "利润表"}], "matchCount": 2, "hasWindCode": true}, {"key": "45", "item": "（一）归属母公司所有者的其他综合收益的税后净 额", "currentAmount": "-169,794.76", "previousAmount": "5,777,891.36", "level": 0, "rowType": "normal", "sheetName": "利润表", "reportType": "利润表", "originalCurrentAmount": "-169794.76", "originalPreviousAmount": "5777891.36", "matchedMetrics": [{"name": "其他", "code": "其他", "description": "其他", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "others"}, {"name": "其他综合收益", "code": "其他综合收益", "description": "归属于母公司股东的其他综合收益", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "other_compreh_inc"}], "windcode": "others", "windcodes": ["others", "other_compreh_inc"], "windcodeDetails": [{"windcode": "others", "metricName": "其他", "metricCode": "其他", "description": "其他", "unit": "", "reportType": "利润表"}, {"windcode": "other_compreh_inc", "metricName": "其他综合收益", "metricCode": "其他综合收益", "description": "归属于母公司股东的其他综合收益", "unit": "", "reportType": "利润表"}], "matchCount": 2, "hasWindCode": true}, {"key": "46", "item": "1．不能重分类进损益的其他综合收益", "currentAmount": "-5,270,000.00", "previousAmount": "-1,442,748.49", "level": 0, "rowType": "normal", "sheetName": "利润表", "reportType": "利润表", "originalCurrentAmount": "-5270000.0", "originalPreviousAmount": "-1442748.49", "matchedMetrics": [{"name": "其他", "code": "其他", "description": "其他", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "others"}, {"name": "其他综合收益", "code": "其他综合收益", "description": "归属于母公司股东的其他综合收益", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "other_compreh_inc"}], "windcode": "others", "windcodes": ["others", "other_compreh_inc"], "windcodeDetails": [{"windcode": "others", "metricName": "其他", "metricCode": "其他", "description": "其他", "unit": "", "reportType": "利润表"}, {"windcode": "other_compreh_inc", "metricName": "其他综合收益", "metricCode": "其他综合收益", "description": "归属于母公司股东的其他综合收益", "unit": "", "reportType": "利润表"}], "matchCount": 2, "hasWindCode": true}, {"key": "47", "item": "（1）重新计量设定受益计划变动额", "currentAmount": "", "previousAmount": "", "level": 0, "rowType": "normal", "sheetName": "利润表", "reportType": "利润表", "originalCurrentAmount": "", "originalPreviousAmount": "", "matchedMetrics": [], "windcode": null, "windcodes": [], "windcodeDetails": [], "matchCount": 0, "hasWindCode": false}, {"key": "48", "item": "（2）权益法下不能转损益的其他综合收益", "currentAmount": "", "previousAmount": "", "level": 0, "rowType": "normal", "sheetName": "利润表", "reportType": "利润表", "originalCurrentAmount": "", "originalPreviousAmount": "", "matchedMetrics": [{"name": "其他", "code": "其他", "description": "其他", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "others"}, {"name": "其他综合收益", "code": "其他综合收益", "description": "归属于母公司股东的其他综合收益", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "other_compreh_inc"}], "windcode": "others", "windcodes": ["others", "other_compreh_inc"], "windcodeDetails": [{"windcode": "others", "metricName": "其他", "metricCode": "其他", "description": "其他", "unit": "", "reportType": "利润表"}, {"windcode": "other_compreh_inc", "metricName": "其他综合收益", "metricCode": "其他综合收益", "description": "归属于母公司股东的其他综合收益", "unit": "", "reportType": "利润表"}], "matchCount": 2, "hasWindCode": true}, {"key": "49", "item": "（3）其他权益工具投资公允价值变动", "currentAmount": "-5,270,000.00", "previousAmount": "-1,442,748.49", "level": 0, "rowType": "normal", "sheetName": "利润表", "reportType": "利润表", "originalCurrentAmount": "-5270000.0", "originalPreviousAmount": "-1442748.49", "matchedMetrics": [{"name": "其他权益工具投资", "code": "其他权益工具投资", "description": "其他权益工具投资", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "oth_eqy_instruments_invest"}, {"name": "其他", "code": "其他", "description": "其他", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "others"}, {"name": "其他权益工具", "code": "其他权益工具", "description": "其他权益工具", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "other_equity_instruments"}], "windcode": "oth_eqy_instruments_invest", "windcodes": ["oth_eqy_instruments_invest", "others", "other_equity_instruments"], "windcodeDetails": [{"windcode": "oth_eqy_instruments_invest", "metricName": "其他权益工具投资", "metricCode": "其他权益工具投资", "description": "其他权益工具投资", "unit": "", "reportType": "利润表"}, {"windcode": "others", "metricName": "其他", "metricCode": "其他", "description": "其他", "unit": "", "reportType": "利润表"}, {"windcode": "other_equity_instruments", "metricName": "其他权益工具", "metricCode": "其他权益工具", "description": "其他权益工具", "unit": "", "reportType": "利润表"}], "matchCount": 3, "hasWindCode": true}, {"key": "50", "item": "（4）企业自身信用风险公允价值变动", "currentAmount": "", "previousAmount": "", "level": 0, "rowType": "normal", "sheetName": "利润表", "reportType": "利润表", "originalCurrentAmount": "", "originalPreviousAmount": "", "matchedMetrics": [], "windcode": null, "windcodes": [], "windcodeDetails": [], "matchCount": 0, "hasWindCode": false}, {"key": "51", "item": "2．将重分类进损益的其他综合收益", "currentAmount": "5,100,205.24", "previousAmount": "7,220,639.85", "level": 0, "rowType": "normal", "sheetName": "利润表", "reportType": "利润表", "originalCurrentAmount": "5100205.24", "originalPreviousAmount": "7220639.85", "matchedMetrics": [{"name": "其他", "code": "其他", "description": "其他", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "others"}, {"name": "其他综合收益", "code": "其他综合收益", "description": "归属于母公司股东的其他综合收益", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "other_compreh_inc"}], "windcode": "others", "windcodes": ["others", "other_compreh_inc"], "windcodeDetails": [{"windcode": "others", "metricName": "其他", "metricCode": "其他", "description": "其他", "unit": "", "reportType": "利润表"}, {"windcode": "other_compreh_inc", "metricName": "其他综合收益", "metricCode": "其他综合收益", "description": "归属于母公司股东的其他综合收益", "unit": "", "reportType": "利润表"}], "matchCount": 2, "hasWindCode": true}, {"key": "52", "item": "（1）权益法下可转损益的其他综合收益", "currentAmount": "", "previousAmount": "", "level": 0, "rowType": "normal", "sheetName": "利润表", "reportType": "利润表", "originalCurrentAmount": "", "originalPreviousAmount": "", "matchedMetrics": [{"name": "其他", "code": "其他", "description": "其他", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "others"}, {"name": "其他综合收益", "code": "其他综合收益", "description": "归属于母公司股东的其他综合收益", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "other_compreh_inc"}], "windcode": "others", "windcodes": ["others", "other_compreh_inc"], "windcodeDetails": [{"windcode": "others", "metricName": "其他", "metricCode": "其他", "description": "其他", "unit": "", "reportType": "利润表"}, {"windcode": "other_compreh_inc", "metricName": "其他综合收益", "metricCode": "其他综合收益", "description": "归属于母公司股东的其他综合收益", "unit": "", "reportType": "利润表"}], "matchCount": 2, "hasWindCode": true}, {"key": "53", "item": "（2）其他债权投资公允价值变动", "currentAmount": "", "previousAmount": "", "level": 0, "rowType": "normal", "sheetName": "利润表", "reportType": "利润表", "originalCurrentAmount": "", "originalPreviousAmount": "", "matchedMetrics": [{"name": "其他", "code": "其他", "description": "其他", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "others"}, {"name": "其他债权投资", "code": "其他债权投资", "description": "其他债权投资", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "oth_debt_invest"}, {"name": "债权投资", "code": "债权投资", "description": "债权投资", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "debt_invest"}], "windcode": "others", "windcodes": ["others", "oth_debt_invest", "debt_invest"], "windcodeDetails": [{"windcode": "others", "metricName": "其他", "metricCode": "其他", "description": "其他", "unit": "", "reportType": "利润表"}, {"windcode": "oth_debt_invest", "metricName": "其他债权投资", "metricCode": "其他债权投资", "description": "其他债权投资", "unit": "", "reportType": "利润表"}, {"windcode": "debt_invest", "metricName": "债权投资", "metricCode": "债权投资", "description": "债权投资", "unit": "", "reportType": "利润表"}], "matchCount": 3, "hasWindCode": true}, {"key": "54", "item": "（3）金融资产重分类计入其他综合收益的金额", "currentAmount": "", "previousAmount": "", "level": 0, "rowType": "normal", "sheetName": "利润表", "reportType": "利润表", "originalCurrentAmount": "", "originalPreviousAmount": "", "matchedMetrics": [{"name": "其他", "code": "其他", "description": "其他", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "others"}, {"name": "其他综合收益", "code": "其他综合收益", "description": "归属于母公司股东的其他综合收益", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "other_compreh_inc"}], "windcode": "others", "windcodes": ["others", "other_compreh_inc"], "windcodeDetails": [{"windcode": "others", "metricName": "其他", "metricCode": "其他", "description": "其他", "unit": "", "reportType": "利润表"}, {"windcode": "other_compreh_inc", "metricName": "其他综合收益", "metricCode": "其他综合收益", "description": "归属于母公司股东的其他综合收益", "unit": "", "reportType": "利润表"}], "matchCount": 2, "hasWindCode": true}, {"key": "55", "item": "（4）其他债权投资信用减值准备", "currentAmount": "", "previousAmount": "", "level": 0, "rowType": "normal", "sheetName": "利润表", "reportType": "利润表", "originalCurrentAmount": "", "originalPreviousAmount": "", "matchedMetrics": [{"name": "其他", "code": "其他", "description": "其他", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "others"}, {"name": "其他债权投资", "code": "其他债权投资", "description": "其他债权投资", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "oth_debt_invest"}, {"name": "债权投资", "code": "债权投资", "description": "债权投资", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "debt_invest"}], "windcode": "others", "windcodes": ["others", "oth_debt_invest", "debt_invest"], "windcodeDetails": [{"windcode": "others", "metricName": "其他", "metricCode": "其他", "description": "其他", "unit": "", "reportType": "利润表"}, {"windcode": "oth_debt_invest", "metricName": "其他债权投资", "metricCode": "其他债权投资", "description": "其他债权投资", "unit": "", "reportType": "利润表"}, {"windcode": "debt_invest", "metricName": "债权投资", "metricCode": "债权投资", "description": "债权投资", "unit": "", "reportType": "利润表"}], "matchCount": 3, "hasWindCode": true}, {"key": "56", "item": "（5）现金流量套期储备", "currentAmount": "", "previousAmount": "", "level": 0, "rowType": "normal", "sheetName": "利润表", "reportType": "利润表", "originalCurrentAmount": "", "originalPreviousAmount": "", "matchedMetrics": [], "windcode": null, "windcodes": [], "windcodeDetails": [], "matchCount": 0, "hasWindCode": false}, {"key": "57", "item": "（6）外币财务报表折算差额", "currentAmount": "5,100,205.24", "previousAmount": "7,220,639.85", "level": 0, "rowType": "normal", "sheetName": "利润表", "reportType": "利润表", "originalCurrentAmount": "5100205.24", "originalPreviousAmount": "7220639.85", "matchedMetrics": [], "windcode": null, "windcodes": [], "windcodeDetails": [], "matchCount": 0, "hasWindCode": false}, {"key": "58", "item": "（7）其他", "currentAmount": "", "previousAmount": "", "level": 0, "rowType": "normal", "sheetName": "利润表", "reportType": "利润表", "originalCurrentAmount": "", "originalPreviousAmount": "", "matchedMetrics": [{"name": "其他", "code": "其他", "description": "其他", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "others"}], "windcode": "others", "windcodes": ["others"], "windcodeDetails": [{"windcode": "others", "metricName": "其他", "metricCode": "其他", "description": "其他", "unit": "", "reportType": "利润表"}], "matchCount": 1, "hasWindCode": true}, {"key": "59", "item": "（二）归属于少数股东的其他综合收益的税后净额", "currentAmount": "", "previousAmount": "", "level": 0, "rowType": "normal", "sheetName": "利润表", "reportType": "利润表", "originalCurrentAmount": "", "originalPreviousAmount": "", "matchedMetrics": [{"name": "其他", "code": "其他", "description": "其他", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "others"}, {"name": "其他综合收益", "code": "其他综合收益", "description": "归属于母公司股东的其他综合收益", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "other_compreh_inc"}], "windcode": "others", "windcodes": ["others", "other_compreh_inc"], "windcodeDetails": [{"windcode": "others", "metricName": "其他", "metricCode": "其他", "description": "其他", "unit": "", "reportType": "利润表"}, {"windcode": "other_compreh_inc", "metricName": "其他综合收益", "metricCode": "其他综合收益", "description": "归属于母公司股东的其他综合收益", "unit": "", "reportType": "利润表"}], "matchCount": 2, "hasWindCode": true}, {"key": "60", "item": "七、综合收益总额", "currentAmount": "545,631,448.72", "previousAmount": "528,422,494.80", "level": 0, "rowType": "normal", "sheetName": "利润表", "reportType": "利润表", "originalCurrentAmount": "545631448.72", "originalPreviousAmount": "528422494.8", "matchedMetrics": [{"name": "综合收益总额", "code": "综合收益总额", "description": "综合收益总额", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "tot_compreh_inc"}], "windcode": "tot_compreh_inc", "windcodes": ["tot_compreh_inc"], "windcodeDetails": [{"windcode": "tot_compreh_inc", "metricName": "综合收益总额", "metricCode": "综合收益总额", "description": "综合收益总额", "unit": "", "reportType": "利润表"}], "matchCount": 1, "hasWindCode": true}, {"key": "61", "item": "（一）归属于母公司所有者的综合收益总额", "currentAmount": "550,391,167.52", "previousAmount": "502,815,334.28", "level": 0, "rowType": "normal", "sheetName": "利润表", "reportType": "利润表", "originalCurrentAmount": "550391167.52", "originalPreviousAmount": "502815334.28", "matchedMetrics": [{"name": "综合收益总额", "code": "综合收益总额", "description": "综合收益总额", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "tot_compreh_inc"}], "windcode": "tot_compreh_inc", "windcodes": ["tot_compreh_inc"], "windcodeDetails": [{"windcode": "tot_compreh_inc", "metricName": "综合收益总额", "metricCode": "综合收益总额", "description": "综合收益总额", "unit": "", "reportType": "利润表"}], "matchCount": 1, "hasWindCode": true}, {"key": "62", "item": "（二）归属于少数股东的综合收益总额", "currentAmount": "-4,759,718.80", "previousAmount": "25,607,160.52", "level": 0, "rowType": "normal", "sheetName": "利润表", "reportType": "利润表", "originalCurrentAmount": "-4759718.8", "originalPreviousAmount": "25607160.52", "matchedMetrics": [{"name": "归属于少数股东的综合收益总额", "code": "归属于少数股东的综合收益总额", "description": "归属于少数股东的综合收益总额", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "tot_compreh_inc_min_shrhldr"}, {"name": "综合收益总额", "code": "综合收益总额", "description": "综合收益总额", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "tot_compreh_inc"}], "windcode": "tot_compreh_inc_min_shrhldr", "windcodes": ["tot_compreh_inc_min_shrhldr", "tot_compreh_inc"], "windcodeDetails": [{"windcode": "tot_compreh_inc_min_shrhldr", "metricName": "归属于少数股东的综合收益总额", "metricCode": "归属于少数股东的综合收益总额", "description": "归属于少数股东的综合收益总额", "unit": "", "reportType": "利润表"}, {"windcode": "tot_compreh_inc", "metricName": "综合收益总额", "metricCode": "综合收益总额", "description": "综合收益总额", "unit": "", "reportType": "利润表"}], "matchCount": 2, "hasWindCode": true}, {"key": "63", "item": "八、每股收益：", "currentAmount": "", "previousAmount": "", "level": 0, "rowType": "normal", "sheetName": "利润表", "reportType": "利润表", "originalCurrentAmount": "", "originalPreviousAmount": "", "matchedMetrics": [], "windcode": null, "windcodes": [], "windcodeDetails": [], "matchCount": 0, "hasWindCode": false}, {"key": "64", "item": "（一）基本每股收益(元/股)", "currentAmount": "0.80", "previousAmount": "0.73", "level": 0, "rowType": "normal", "sheetName": "利润表", "reportType": "利润表", "originalCurrentAmount": "0.8001", "originalPreviousAmount": "0.7252", "matchedMetrics": [{"name": "基本每股收益", "code": "基本每股收益", "description": "基本每股收益", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "eps_basic_is"}], "windcode": "eps_basic_is", "windcodes": ["eps_basic_is"], "windcodeDetails": [{"windcode": "eps_basic_is", "metricName": "基本每股收益", "metricCode": "基本每股收益", "description": "基本每股收益", "unit": "", "reportType": "利润表"}], "matchCount": 1, "hasWindCode": true}, {"key": "65", "item": "（二）稀释每股收益(元/股)", "currentAmount": "0.80", "previousAmount": "0.72", "level": 0, "rowType": "normal", "sheetName": "利润表", "reportType": "利润表", "originalCurrentAmount": "0.8", "originalPreviousAmount": "0.7242", "matchedMetrics": [{"name": "稀释每股收益", "code": "稀释每股收益", "description": "稀释每股收益", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "eps_diluted_is"}], "windcode": "eps_diluted_is", "windcodes": ["eps_diluted_is"], "windcodeDetails": [{"windcode": "eps_diluted_is", "metricName": "稀释每股收益", "metricCode": "稀释每股收益", "description": "稀释每股收益", "unit": "", "reportType": "利润表"}], "matchCount": 1, "hasWindCode": true}, {"key": "1", "item": "流动资产：", "currentAmount": "", "previousAmount": "", "level": 0, "rowType": "normal", "sheetName": "资产负债表", "reportType": "资产负债表", "originalCurrentAmount": "", "originalPreviousAmount": "", "matchedMetrics": [{"name": "非流动资产合计", "code": "非流动资产合计", "description": "非流动资产合计", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "tot_non_cur_assets"}, {"name": "流动资产合计", "code": "流动资产合计", "description": "流动资产合计", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "tot_cur_assets"}, {"name": "非流动资产差额(合计平衡项目)", "code": "非流动资产差额(合计平衡项目)", "description": "非流动资产差额(合计平衡项目)", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "non_cur_assets_netting"}, {"name": "一年内到期的非流动资产", "code": "一年内到期的非流动资产", "description": "一年内到期的非流动资产", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "non_cur_assets_due_within_1y"}, {"name": "非流动资产差额(特殊报表科目)", "code": "非流动资产差额(特殊报表科目)", "description": "非流动资产差额(特殊报表科目)", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "non_cur_assets_gap"}, {"name": "其他非流动资产", "code": "其他非流动资产", "description": "其他非流动资产", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "oth_non_cur_assets"}, {"name": "其他流动资产", "code": "其他流动资产", "description": "其他流动资产", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "oth_cur_assets"}, {"name": "流动资产差额(合计平衡项目)", "code": "流动资产差额(合计平衡项目)", "description": "流动资产差额(合计平衡项目)", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "cur_assets_netting"}, {"name": "流动资产差额说明(特殊报表科目)", "code": "流动资产差额说明(特殊报表科目)", "description": "流动资产差额说明(特殊报表科目)", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "cur_assets_gap_detail"}, {"name": "非流动资产差额说明(特殊报表科目)", "code": "非流动资产差额说明(特殊报表科目)", "description": "非流动资产差额说明(特殊报表科目)", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "non_cur_assets_gap_detail"}, {"name": "流动资产差额(特殊报表科目)", "code": "流动资产差额(特殊报表科目)", "description": "流动资产差额(特殊报表科目)", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "cur_assets_gap"}, {"name": "非流动资产处置净损失", "code": "非流动资产处置净损失", "description": "非流动资产处置净损失", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "net_loss_disp_noncur_asset"}], "windcode": "tot_non_cur_assets", "windcodes": ["tot_non_cur_assets", "tot_cur_assets", "non_cur_assets_netting", "non_cur_assets_due_within_1y", "non_cur_assets_gap", "oth_non_cur_assets", "oth_cur_assets", "cur_assets_netting", "cur_assets_gap_detail", "non_cur_assets_gap_detail", "cur_assets_gap", "net_loss_disp_noncur_asset"], "windcodeDetails": [{"windcode": "tot_non_cur_assets", "metricName": "非流动资产合计", "metricCode": "非流动资产合计", "description": "非流动资产合计", "unit": "", "reportType": "资产负债表"}, {"windcode": "tot_cur_assets", "metricName": "流动资产合计", "metricCode": "流动资产合计", "description": "流动资产合计", "unit": "", "reportType": "资产负债表"}, {"windcode": "non_cur_assets_netting", "metricName": "非流动资产差额(合计平衡项目)", "metricCode": "非流动资产差额(合计平衡项目)", "description": "非流动资产差额(合计平衡项目)", "unit": "", "reportType": "资产负债表"}, {"windcode": "non_cur_assets_due_within_1y", "metricName": "一年内到期的非流动资产", "metricCode": "一年内到期的非流动资产", "description": "一年内到期的非流动资产", "unit": "", "reportType": "资产负债表"}, {"windcode": "non_cur_assets_gap", "metricName": "非流动资产差额(特殊报表科目)", "metricCode": "非流动资产差额(特殊报表科目)", "description": "非流动资产差额(特殊报表科目)", "unit": "", "reportType": "资产负债表"}, {"windcode": "oth_non_cur_assets", "metricName": "其他非流动资产", "metricCode": "其他非流动资产", "description": "其他非流动资产", "unit": "", "reportType": "资产负债表"}, {"windcode": "oth_cur_assets", "metricName": "其他流动资产", "metricCode": "其他流动资产", "description": "其他流动资产", "unit": "", "reportType": "资产负债表"}, {"windcode": "cur_assets_netting", "metricName": "流动资产差额(合计平衡项目)", "metricCode": "流动资产差额(合计平衡项目)", "description": "流动资产差额(合计平衡项目)", "unit": "", "reportType": "资产负债表"}, {"windcode": "cur_assets_gap_detail", "metricName": "流动资产差额说明(特殊报表科目)", "metricCode": "流动资产差额说明(特殊报表科目)", "description": "流动资产差额说明(特殊报表科目)", "unit": "", "reportType": "资产负债表"}, {"windcode": "non_cur_assets_gap_detail", "metricName": "非流动资产差额说明(特殊报表科目)", "metricCode": "非流动资产差额说明(特殊报表科目)", "description": "非流动资产差额说明(特殊报表科目)", "unit": "", "reportType": "资产负债表"}, {"windcode": "cur_assets_gap", "metricName": "流动资产差额(特殊报表科目)", "metricCode": "流动资产差额(特殊报表科目)", "description": "流动资产差额(特殊报表科目)", "unit": "", "reportType": "资产负债表"}, {"windcode": "net_loss_disp_noncur_asset", "metricName": "非流动资产处置净损失", "metricCode": "非流动资产处置净损失", "description": "非流动资产处置净损失", "unit": "", "reportType": "资产负债表"}], "matchCount": 12, "hasWindCode": true}, {"key": "2", "item": "货币资金", "currentAmount": "2,190,697,534.13", "previousAmount": "2,721,778,066.21", "level": 0, "rowType": "normal", "sheetName": "资产负债表", "reportType": "资产负债表", "originalCurrentAmount": "2190697534.13", "originalPreviousAmount": "2721778066.21", "matchedMetrics": [{"name": "货币资金", "code": "货币资金", "description": "货币资金", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "monetary_cap"}], "windcode": "monetary_cap", "windcodes": ["monetary_cap"], "windcodeDetails": [{"windcode": "monetary_cap", "metricName": "货币资金", "metricCode": "货币资金", "description": "货币资金", "unit": "", "reportType": "资产负债表"}], "matchCount": 1, "hasWindCode": true}, {"key": "3", "item": "结算备付金", "currentAmount": "", "previousAmount": "", "level": 0, "rowType": "normal", "sheetName": "资产负债表", "reportType": "资产负债表", "originalCurrentAmount": "", "originalPreviousAmount": "", "matchedMetrics": [{"name": "结算备付金", "code": "结算备付金", "description": "结算备付金", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "settle_rsrv"}], "windcode": "settle_rsrv", "windcodes": ["settle_rsrv"], "windcodeDetails": [{"windcode": "settle_rsrv", "metricName": "结算备付金", "metricCode": "结算备付金", "description": "结算备付金", "unit": "", "reportType": "资产负债表"}], "matchCount": 1, "hasWindCode": true}, {"key": "4", "item": "拆出资金", "currentAmount": "", "previousAmount": "", "level": 0, "rowType": "normal", "sheetName": "资产负债表", "reportType": "资产负债表", "originalCurrentAmount": "", "originalPreviousAmount": "", "matchedMetrics": [{"name": "拆出资金", "code": "拆出资金", "description": "拆出资金", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "loans_to_oth_banks"}, {"name": "拆入/拆出资金净减少额", "code": "拆入/拆出资金净减少额", "description": "拆入/拆出资金净减少额", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "net_decr_loans_other_bank"}], "windcode": "loans_to_oth_banks", "windcodes": ["loans_to_oth_banks", "net_decr_loans_other_bank"], "windcodeDetails": [{"windcode": "loans_to_oth_banks", "metricName": "拆出资金", "metricCode": "拆出资金", "description": "拆出资金", "unit": "", "reportType": "资产负债表"}, {"windcode": "net_decr_loans_other_bank", "metricName": "拆入/拆出资金净减少额", "metricCode": "拆入/拆出资金净减少额", "description": "拆入/拆出资金净减少额", "unit": "", "reportType": "资产负债表"}], "matchCount": 2, "hasWindCode": true}, {"key": "5", "item": "交易性金融资产", "currentAmount": "", "previousAmount": "", "level": 0, "rowType": "normal", "sheetName": "资产负债表", "reportType": "资产负债表", "originalCurrentAmount": "", "originalPreviousAmount": "", "matchedMetrics": [{"name": "交易性金融资产(合计)", "code": "交易性金融资产(合计)", "description": "交易性金融资产(合计)", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "wgsd_invest_trading"}, {"name": "处置交易性金融资产净增加额", "code": "处置交易性金融资产净增加额", "description": "处置交易性金融资产净增加额", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "net_incr_disp_tfa"}], "windcode": "wgsd_invest_trading", "windcodes": ["wgsd_invest_trading", "net_incr_disp_tfa"], "windcodeDetails": [{"windcode": "wgsd_invest_trading", "metricName": "交易性金融资产(合计)", "metricCode": "交易性金融资产(合计)", "description": "交易性金融资产(合计)", "unit": "", "reportType": "资产负债表"}, {"windcode": "net_incr_disp_tfa", "metricName": "处置交易性金融资产净增加额", "metricCode": "处置交易性金融资产净增加额", "description": "处置交易性金融资产净增加额", "unit": "", "reportType": "资产负债表"}], "matchCount": 2, "hasWindCode": true}, {"key": "6", "item": "衍生金融资产", "currentAmount": "", "previousAmount": "", "level": 0, "rowType": "normal", "sheetName": "资产负债表", "reportType": "资产负债表", "originalCurrentAmount": "", "originalPreviousAmount": "", "matchedMetrics": [{"name": "衍生金融资产", "code": "衍生金融资产", "description": "衍生金融资产", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "derivative_fin_assets"}], "windcode": "derivative_fin_assets", "windcodes": ["derivative_fin_assets"], "windcodeDetails": [{"windcode": "derivative_fin_assets", "metricName": "衍生金融资产", "metricCode": "衍生金融资产", "description": "衍生金融资产", "unit": "", "reportType": "资产负债表"}], "matchCount": 1, "hasWindCode": true}, {"key": "7", "item": "应收票据", "currentAmount": "320,540,023.17", "previousAmount": "311,630,810.65", "level": 0, "rowType": "normal", "sheetName": "资产负债表", "reportType": "资产负债表", "originalCurrentAmount": "320540023.17", "originalPreviousAmount": "311630810.65", "matchedMetrics": [{"name": "应收票据及应收账款", "code": "应收票据及应收账款", "description": "应收票据及应收账款", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "acctandnotes_rcv"}, {"name": "应收票据", "code": "应收票据", "description": "应收票据", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "notes_rcv"}], "windcode": "acctandnotes_rcv", "windcodes": ["acctandnotes_rcv", "notes_rcv"], "windcodeDetails": [{"windcode": "acctandnotes_rcv", "metricName": "应收票据及应收账款", "metricCode": "应收票据及应收账款", "description": "应收票据及应收账款", "unit": "", "reportType": "资产负债表"}, {"windcode": "notes_rcv", "metricName": "应收票据", "metricCode": "应收票据", "description": "应收票据", "unit": "", "reportType": "资产负债表"}], "matchCount": 2, "hasWindCode": true}, {"key": "8", "item": "应收账款", "currentAmount": "2,394,281,272.89", "previousAmount": "1,823,191,449.46", "level": 0, "rowType": "normal", "sheetName": "资产负债表", "reportType": "资产负债表", "originalCurrentAmount": "2394281272.89", "originalPreviousAmount": "1823191449.46", "matchedMetrics": [{"name": "应收票据及应收账款", "code": "应收票据及应收账款", "description": "应收票据及应收账款", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "acctandnotes_rcv"}, {"name": "应收账款", "code": "应收账款", "description": "应收账款", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "acct_rcv"}], "windcode": "acctandnotes_rcv", "windcodes": ["acctandnotes_rcv", "acct_rcv"], "windcodeDetails": [{"windcode": "acctandnotes_rcv", "metricName": "应收票据及应收账款", "metricCode": "应收票据及应收账款", "description": "应收票据及应收账款", "unit": "", "reportType": "资产负债表"}, {"windcode": "acct_rcv", "metricName": "应收账款", "metricCode": "应收账款", "description": "应收账款", "unit": "", "reportType": "资产负债表"}], "matchCount": 2, "hasWindCode": true}, {"key": "9", "item": "应收款项融资", "currentAmount": "44,819,799.19", "previousAmount": "66,916,963.93", "level": 0, "rowType": "normal", "sheetName": "资产负债表", "reportType": "资产负债表", "originalCurrentAmount": "44819799.19", "originalPreviousAmount": "66916963.93", "matchedMetrics": [{"name": "应收款项", "code": "应收款项", "description": "应收款项", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "tot_acct_rcv"}, {"name": "应收款项融资", "code": "应收款项融资", "description": "应收款项融资", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "financing_a/r"}], "windcode": "tot_acct_rcv", "windcodes": ["tot_acct_rcv", "financing_a/r"], "windcodeDetails": [{"windcode": "tot_acct_rcv", "metricName": "应收款项", "metricCode": "应收款项", "description": "应收款项", "unit": "", "reportType": "资产负债表"}, {"windcode": "financing_a/r", "metricName": "应收款项融资", "metricCode": "应收款项融资", "description": "应收款项融资", "unit": "", "reportType": "资产负债表"}], "matchCount": 2, "hasWindCode": true}, {"key": "10", "item": "预付款项", "currentAmount": "841,878,803.36", "previousAmount": "938,775,205.48", "level": 0, "rowType": "normal", "sheetName": "资产负债表", "reportType": "资产负债表", "originalCurrentAmount": "841878803.36", "originalPreviousAmount": "938775205.48", "matchedMetrics": [{"name": "预付款项", "code": "预付款项", "description": "预付款项", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "prepay"}], "windcode": "prepay", "windcodes": ["prepay"], "windcodeDetails": [{"windcode": "prepay", "metricName": "预付款项", "metricCode": "预付款项", "description": "预付款项", "unit": "", "reportType": "资产负债表"}], "matchCount": 1, "hasWindCode": true}, {"key": "11", "item": "应收保费", "currentAmount": "", "previousAmount": "", "level": 0, "rowType": "normal", "sheetName": "资产负债表", "reportType": "资产负债表", "originalCurrentAmount": "", "originalPreviousAmount": "", "matchedMetrics": [{"name": "应收保费", "code": "应收保费", "description": "应收保费", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "prem_rcv"}], "windcode": "prem_rcv", "windcodes": ["prem_rcv"], "windcodeDetails": [{"windcode": "prem_rcv", "metricName": "应收保费", "metricCode": "应收保费", "description": "应收保费", "unit": "", "reportType": "资产负债表"}], "matchCount": 1, "hasWindCode": true}, {"key": "12", "item": "应收分保账款", "currentAmount": "", "previousAmount": "", "level": 0, "rowType": "normal", "sheetName": "资产负债表", "reportType": "资产负债表", "originalCurrentAmount": "", "originalPreviousAmount": "", "matchedMetrics": [{"name": "应收分保账款", "code": "应收分保账款", "description": "应收分保账款", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "rcv_from_reinsurer"}], "windcode": "rcv_from_reinsurer", "windcodes": ["rcv_from_reinsurer"], "windcodeDetails": [{"windcode": "rcv_from_reinsurer", "metricName": "应收分保账款", "metricCode": "应收分保账款", "description": "应收分保账款", "unit": "", "reportType": "资产负债表"}], "matchCount": 1, "hasWindCode": true}, {"key": "13", "item": "应收分保合同准备金", "currentAmount": "", "previousAmount": "", "level": 0, "rowType": "normal", "sheetName": "资产负债表", "reportType": "资产负债表", "originalCurrentAmount": "", "originalPreviousAmount": "", "matchedMetrics": [{"name": "应收分保合同准备金", "code": "应收分保合同准备金", "description": "应收分保合同准备金", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "rcv_from_ceded_insur_cont_rsrv"}], "windcode": "rcv_from_ceded_insur_cont_rsrv", "windcodes": ["rcv_from_ceded_insur_cont_rsrv"], "windcodeDetails": [{"windcode": "rcv_from_ceded_insur_cont_rsrv", "metricName": "应收分保合同准备金", "metricCode": "应收分保合同准备金", "description": "应收分保合同准备金", "unit": "", "reportType": "资产负债表"}], "matchCount": 1, "hasWindCode": true}, {"key": "14", "item": "其他应收款", "currentAmount": "141,404,775.21", "previousAmount": "139,694,664.65", "level": 0, "rowType": "normal", "sheetName": "资产负债表", "reportType": "资产负债表", "originalCurrentAmount": "141404775.21", "originalPreviousAmount": "139694664.65", "matchedMetrics": [{"name": "其他", "code": "其他", "description": "其他", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "others"}, {"name": "其他应收款(合计)", "code": "其他应收款(合计)", "description": "其他应收款合计", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "oth_rcv_tot"}, {"name": "其他应收款", "code": "其他应收款", "description": "其中:其他应收款", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "oth_rcv"}], "windcode": "others", "windcodes": ["others", "oth_rcv_tot", "oth_rcv"], "windcodeDetails": [{"windcode": "others", "metricName": "其他", "metricCode": "其他", "description": "其他", "unit": "", "reportType": "资产负债表"}, {"windcode": "oth_rcv_tot", "metricName": "其他应收款(合计)", "metricCode": "其他应收款(合计)", "description": "其他应收款合计", "unit": "", "reportType": "资产负债表"}, {"windcode": "oth_rcv", "metricName": "其他应收款", "metricCode": "其他应收款", "description": "其中:其他应收款", "unit": "", "reportType": "资产负债表"}], "matchCount": 3, "hasWindCode": true}, {"key": "15", "item": "其中：应收利息", "currentAmount": "", "previousAmount": "", "level": 0, "rowType": "normal", "sheetName": "资产负债表", "reportType": "资产负债表", "originalCurrentAmount": "", "originalPreviousAmount": "", "matchedMetrics": [{"name": "应收利息", "code": "应收利息", "description": "应收利息", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "int_rcv"}], "windcode": "int_rcv", "windcodes": ["int_rcv"], "windcodeDetails": [{"windcode": "int_rcv", "metricName": "应收利息", "metricCode": "应收利息", "description": "应收利息", "unit": "", "reportType": "资产负债表"}], "matchCount": 1, "hasWindCode": true}, {"key": "16", "item": "应收股利", "currentAmount": "", "previousAmount": "", "level": 0, "rowType": "normal", "sheetName": "资产负债表", "reportType": "资产负债表", "originalCurrentAmount": "", "originalPreviousAmount": "", "matchedMetrics": [{"name": "应收股利", "code": "应收股利", "description": "应收股利", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "dvd_rcv"}], "windcode": "dvd_rcv", "windcodes": ["dvd_rcv"], "windcodeDetails": [{"windcode": "dvd_rcv", "metricName": "应收股利", "metricCode": "应收股利", "description": "应收股利", "unit": "", "reportType": "资产负债表"}], "matchCount": 1, "hasWindCode": true}, {"key": "17", "item": "买入返售金融资产", "currentAmount": "", "previousAmount": "", "level": 0, "rowType": "normal", "sheetName": "资产负债表", "reportType": "资产负债表", "originalCurrentAmount": "", "originalPreviousAmount": "", "matchedMetrics": [{"name": "买入返售金融资产", "code": "买入返售金融资产", "description": "买入返售金融资产", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "red_monetary_cap_for_sale"}], "windcode": "red_monetary_cap_for_sale", "windcodes": ["red_monetary_cap_for_sale"], "windcodeDetails": [{"windcode": "red_monetary_cap_for_sale", "metricName": "买入返售金融资产", "metricCode": "买入返售金融资产", "description": "买入返售金融资产", "unit": "", "reportType": "资产负债表"}], "matchCount": 1, "hasWindCode": true}, {"key": "18", "item": "存货", "currentAmount": "3,979,098,596.07", "previousAmount": "3,613,317,111.30", "level": 0, "rowType": "normal", "sheetName": "资产负债表", "reportType": "资产负债表", "originalCurrentAmount": "3979098596.07", "originalPreviousAmount": "3613317111.3", "matchedMetrics": [{"name": "数据资源(存货)", "code": "数据资源(存货)", "description": "数据资源(存货)", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "data_inventories"}, {"name": "存货", "code": "存货", "description": "存货", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "inventories"}, {"name": "存货的减少", "code": "存货的减少", "description": "存货的减少", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "decr_inventories"}], "windcode": "data_inventories", "windcodes": ["data_inventories", "inventories", "decr_inventories"], "windcodeDetails": [{"windcode": "data_inventories", "metricName": "数据资源(存货)", "metricCode": "数据资源(存货)", "description": "数据资源(存货)", "unit": "", "reportType": "资产负债表"}, {"windcode": "inventories", "metricName": "存货", "metricCode": "存货", "description": "存货", "unit": "", "reportType": "资产负债表"}, {"windcode": "decr_inventories", "metricName": "存货的减少", "metricCode": "存货的减少", "description": "存货的减少", "unit": "", "reportType": "资产负债表"}], "matchCount": 3, "hasWindCode": true}, {"key": "19", "item": "其中：数据资源", "currentAmount": "", "previousAmount": "", "level": 0, "rowType": "normal", "sheetName": "资产负债表", "reportType": "资产负债表", "originalCurrentAmount": "", "originalPreviousAmount": "", "matchedMetrics": [], "windcode": null, "windcodes": [], "windcodeDetails": [], "matchCount": 0, "hasWindCode": false}, {"key": "20", "item": "合同资产", "currentAmount": "798,737,663.44", "previousAmount": "753,617,732.78", "level": 0, "rowType": "normal", "sheetName": "资产负债表", "reportType": "资产负债表", "originalCurrentAmount": "798737663.44", "originalPreviousAmount": "753617732.78", "matchedMetrics": [{"name": "合同资产", "code": "合同资产", "description": "合同资产", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "cont_assets"}], "windcode": "cont_assets", "windcodes": ["cont_assets"], "windcodeDetails": [{"windcode": "cont_assets", "metricName": "合同资产", "metricCode": "合同资产", "description": "合同资产", "unit": "", "reportType": "资产负债表"}], "matchCount": 1, "hasWindCode": true}, {"key": "21", "item": "持有待售资产", "currentAmount": "", "previousAmount": "", "level": 0, "rowType": "normal", "sheetName": "资产负债表", "reportType": "资产负债表", "originalCurrentAmount": "", "originalPreviousAmount": "", "matchedMetrics": [], "windcode": null, "windcodes": [], "windcodeDetails": [], "matchCount": 0, "hasWindCode": false}, {"key": "22", "item": "一年内到期的非流动资产", "currentAmount": "6,102,388.20", "previousAmount": "5,634,613.53", "level": 0, "rowType": "normal", "sheetName": "资产负债表", "reportType": "资产负债表", "originalCurrentAmount": "6102388.2", "originalPreviousAmount": "5634613.53", "matchedMetrics": [{"name": "一年内到期的非流动资产", "code": "一年内到期的非流动资产", "description": "一年内到期的非流动资产", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "non_cur_assets_due_within_1y"}], "windcode": "non_cur_assets_due_within_1y", "windcodes": ["non_cur_assets_due_within_1y"], "windcodeDetails": [{"windcode": "non_cur_assets_due_within_1y", "metricName": "一年内到期的非流动资产", "metricCode": "一年内到期的非流动资产", "description": "一年内到期的非流动资产", "unit": "", "reportType": "资产负债表"}], "matchCount": 1, "hasWindCode": true}, {"key": "23", "item": "其他流动资产", "currentAmount": "100,086,736.97", "previousAmount": "50,229,295.85", "level": 0, "rowType": "normal", "sheetName": "资产负债表", "reportType": "资产负债表", "originalCurrentAmount": "100086736.97", "originalPreviousAmount": "50229295.85", "matchedMetrics": [{"name": "其他", "code": "其他", "description": "其他", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "others"}, {"name": "其他流动资产", "code": "其他流动资产", "description": "其他流动资产", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "oth_cur_assets"}], "windcode": "others", "windcodes": ["others", "oth_cur_assets"], "windcodeDetails": [{"windcode": "others", "metricName": "其他", "metricCode": "其他", "description": "其他", "unit": "", "reportType": "资产负债表"}, {"windcode": "oth_cur_assets", "metricName": "其他流动资产", "metricCode": "其他流动资产", "description": "其他流动资产", "unit": "", "reportType": "资产负债表"}], "matchCount": 2, "hasWindCode": true}, {"key": "24", "item": "流动资产合计", "currentAmount": "10,817,647,592.63", "previousAmount": "10,424,785,913.84", "level": 0, "rowType": "normal", "sheetName": "资产负债表", "reportType": "资产负债表", "originalCurrentAmount": "10817647592.63", "originalPreviousAmount": "10424785913.84", "matchedMetrics": [{"name": "非流动资产合计", "code": "非流动资产合计", "description": "非流动资产合计", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "tot_non_cur_assets"}, {"name": "流动资产合计", "code": "流动资产合计", "description": "流动资产合计", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "tot_cur_assets"}], "windcode": "tot_non_cur_assets", "windcodes": ["tot_non_cur_assets", "tot_cur_assets"], "windcodeDetails": [{"windcode": "tot_non_cur_assets", "metricName": "非流动资产合计", "metricCode": "非流动资产合计", "description": "非流动资产合计", "unit": "", "reportType": "资产负债表"}, {"windcode": "tot_cur_assets", "metricName": "流动资产合计", "metricCode": "流动资产合计", "description": "流动资产合计", "unit": "", "reportType": "资产负债表"}], "matchCount": 2, "hasWindCode": true}, {"key": "25", "item": "非流动资产：", "currentAmount": "", "previousAmount": "", "level": 0, "rowType": "normal", "sheetName": "资产负债表", "reportType": "资产负债表", "originalCurrentAmount": "", "originalPreviousAmount": "", "matchedMetrics": [{"name": "非流动资产合计", "code": "非流动资产合计", "description": "非流动资产合计", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "tot_non_cur_assets"}, {"name": "非流动资产差额(合计平衡项目)", "code": "非流动资产差额(合计平衡项目)", "description": "非流动资产差额(合计平衡项目)", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "non_cur_assets_netting"}, {"name": "一年内到期的非流动资产", "code": "一年内到期的非流动资产", "description": "一年内到期的非流动资产", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "non_cur_assets_due_within_1y"}, {"name": "非流动资产差额(特殊报表科目)", "code": "非流动资产差额(特殊报表科目)", "description": "非流动资产差额(特殊报表科目)", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "non_cur_assets_gap"}, {"name": "其他非流动资产", "code": "其他非流动资产", "description": "其他非流动资产", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "oth_non_cur_assets"}, {"name": "非流动资产差额说明(特殊报表科目)", "code": "非流动资产差额说明(特殊报表科目)", "description": "非流动资产差额说明(特殊报表科目)", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "non_cur_assets_gap_detail"}, {"name": "非流动资产处置净损失", "code": "非流动资产处置净损失", "description": "非流动资产处置净损失", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "net_loss_disp_noncur_asset"}], "windcode": "tot_non_cur_assets", "windcodes": ["tot_non_cur_assets", "non_cur_assets_netting", "non_cur_assets_due_within_1y", "non_cur_assets_gap", "oth_non_cur_assets", "non_cur_assets_gap_detail", "net_loss_disp_noncur_asset"], "windcodeDetails": [{"windcode": "tot_non_cur_assets", "metricName": "非流动资产合计", "metricCode": "非流动资产合计", "description": "非流动资产合计", "unit": "", "reportType": "资产负债表"}, {"windcode": "non_cur_assets_netting", "metricName": "非流动资产差额(合计平衡项目)", "metricCode": "非流动资产差额(合计平衡项目)", "description": "非流动资产差额(合计平衡项目)", "unit": "", "reportType": "资产负债表"}, {"windcode": "non_cur_assets_due_within_1y", "metricName": "一年内到期的非流动资产", "metricCode": "一年内到期的非流动资产", "description": "一年内到期的非流动资产", "unit": "", "reportType": "资产负债表"}, {"windcode": "non_cur_assets_gap", "metricName": "非流动资产差额(特殊报表科目)", "metricCode": "非流动资产差额(特殊报表科目)", "description": "非流动资产差额(特殊报表科目)", "unit": "", "reportType": "资产负债表"}, {"windcode": "oth_non_cur_assets", "metricName": "其他非流动资产", "metricCode": "其他非流动资产", "description": "其他非流动资产", "unit": "", "reportType": "资产负债表"}, {"windcode": "non_cur_assets_gap_detail", "metricName": "非流动资产差额说明(特殊报表科目)", "metricCode": "非流动资产差额说明(特殊报表科目)", "description": "非流动资产差额说明(特殊报表科目)", "unit": "", "reportType": "资产负债表"}, {"windcode": "net_loss_disp_noncur_asset", "metricName": "非流动资产处置净损失", "metricCode": "非流动资产处置净损失", "description": "非流动资产处置净损失", "unit": "", "reportType": "资产负债表"}], "matchCount": 7, "hasWindCode": true}, {"key": "26", "item": "发放贷款和垫款", "currentAmount": "", "previousAmount": "", "level": 0, "rowType": "normal", "sheetName": "资产负债表", "reportType": "资产负债表", "originalCurrentAmount": "", "originalPreviousAmount": "", "matchedMetrics": [], "windcode": null, "windcodes": [], "windcodeDetails": [], "matchCount": 0, "hasWindCode": false}, {"key": "27", "item": "债权投资", "currentAmount": "", "previousAmount": "", "level": 0, "rowType": "normal", "sheetName": "资产负债表", "reportType": "资产负债表", "originalCurrentAmount": "", "originalPreviousAmount": "", "matchedMetrics": [{"name": "其他债权投资", "code": "其他债权投资", "description": "其他债权投资", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "oth_debt_invest"}, {"name": "债权投资", "code": "债权投资", "description": "债权投资", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "debt_invest"}], "windcode": "oth_debt_invest", "windcodes": ["oth_debt_invest", "debt_invest"], "windcodeDetails": [{"windcode": "oth_debt_invest", "metricName": "其他债权投资", "metricCode": "其他债权投资", "description": "其他债权投资", "unit": "", "reportType": "资产负债表"}, {"windcode": "debt_invest", "metricName": "债权投资", "metricCode": "债权投资", "description": "债权投资", "unit": "", "reportType": "资产负债表"}], "matchCount": 2, "hasWindCode": true}, {"key": "28", "item": "其他债权投资", "currentAmount": "", "previousAmount": "", "level": 0, "rowType": "normal", "sheetName": "资产负债表", "reportType": "资产负债表", "originalCurrentAmount": "", "originalPreviousAmount": "", "matchedMetrics": [{"name": "其他", "code": "其他", "description": "其他", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "others"}, {"name": "其他债权投资", "code": "其他债权投资", "description": "其他债权投资", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "oth_debt_invest"}, {"name": "债权投资", "code": "债权投资", "description": "债权投资", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "debt_invest"}], "windcode": "others", "windcodes": ["others", "oth_debt_invest", "debt_invest"], "windcodeDetails": [{"windcode": "others", "metricName": "其他", "metricCode": "其他", "description": "其他", "unit": "", "reportType": "资产负债表"}, {"windcode": "oth_debt_invest", "metricName": "其他债权投资", "metricCode": "其他债权投资", "description": "其他债权投资", "unit": "", "reportType": "资产负债表"}, {"windcode": "debt_invest", "metricName": "债权投资", "metricCode": "债权投资", "description": "债权投资", "unit": "", "reportType": "资产负债表"}], "matchCount": 3, "hasWindCode": true}, {"key": "29", "item": "长期应收款", "currentAmount": "248,014,697.17", "previousAmount": "259,619,964.09", "level": 0, "rowType": "normal", "sheetName": "资产负债表", "reportType": "资产负债表", "originalCurrentAmount": "248014697.17", "originalPreviousAmount": "259619964.09", "matchedMetrics": [{"name": "长期应收款", "code": "长期应收款", "description": "长期应收款", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "long_term_rec"}], "windcode": "long_term_rec", "windcodes": ["long_term_rec"], "windcodeDetails": [{"windcode": "long_term_rec", "metricName": "长期应收款", "metricCode": "长期应收款", "description": "长期应收款", "unit": "", "reportType": "资产负债表"}], "matchCount": 1, "hasWindCode": true}, {"key": "30", "item": "长期股权投资", "currentAmount": "9,328,727.54", "previousAmount": "8,471,678.87", "level": 0, "rowType": "normal", "sheetName": "资产负债表", "reportType": "资产负债表", "originalCurrentAmount": "9328727.54", "originalPreviousAmount": "8471678.87", "matchedMetrics": [{"name": "长期股权投资", "code": "长期股权投资", "description": "长期股权投资", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "long_term_eqy_invest"}], "windcode": "long_term_eqy_invest", "windcodes": ["long_term_eqy_invest"], "windcodeDetails": [{"windcode": "long_term_eqy_invest", "metricName": "长期股权投资", "metricCode": "长期股权投资", "description": "长期股权投资", "unit": "", "reportType": "资产负债表"}], "matchCount": 1, "hasWindCode": true}, {"key": "31", "item": "其他权益工具投资", "currentAmount": "", "previousAmount": "", "level": 0, "rowType": "normal", "sheetName": "资产负债表", "reportType": "资产负债表", "originalCurrentAmount": "", "originalPreviousAmount": "", "matchedMetrics": [{"name": "其他权益工具投资", "code": "其他权益工具投资", "description": "其他权益工具投资", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "oth_eqy_instruments_invest"}, {"name": "其他", "code": "其他", "description": "其他", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "others"}, {"name": "其他权益工具", "code": "其他权益工具", "description": "其他权益工具", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "other_equity_instruments"}], "windcode": "oth_eqy_instruments_invest", "windcodes": ["oth_eqy_instruments_invest", "others", "other_equity_instruments"], "windcodeDetails": [{"windcode": "oth_eqy_instruments_invest", "metricName": "其他权益工具投资", "metricCode": "其他权益工具投资", "description": "其他权益工具投资", "unit": "", "reportType": "资产负债表"}, {"windcode": "others", "metricName": "其他", "metricCode": "其他", "description": "其他", "unit": "", "reportType": "资产负债表"}, {"windcode": "other_equity_instruments", "metricName": "其他权益工具", "metricCode": "其他权益工具", "description": "其他权益工具", "unit": "", "reportType": "资产负债表"}], "matchCount": 3, "hasWindCode": true}, {"key": "32", "item": "其他非流动金融资产", "currentAmount": "", "previousAmount": "", "level": 0, "rowType": "normal", "sheetName": "资产负债表", "reportType": "资产负债表", "originalCurrentAmount": "", "originalPreviousAmount": "", "matchedMetrics": [{"name": "其他非流动金融资产", "code": "其他非流动金融资产", "description": "其他非流动金融资产", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "oth_non_cur_fina_asset"}, {"name": "其他", "code": "其他", "description": "其他", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "others"}], "windcode": "oth_non_cur_fina_asset", "windcodes": ["oth_non_cur_fina_asset", "others"], "windcodeDetails": [{"windcode": "oth_non_cur_fina_asset", "metricName": "其他非流动金融资产", "metricCode": "其他非流动金融资产", "description": "其他非流动金融资产", "unit": "", "reportType": "资产负债表"}, {"windcode": "others", "metricName": "其他", "metricCode": "其他", "description": "其他", "unit": "", "reportType": "资产负债表"}], "matchCount": 2, "hasWindCode": true}, {"key": "33", "item": "投资性房地产", "currentAmount": "433,319.54", "previousAmount": "451,117.46", "level": 0, "rowType": "normal", "sheetName": "资产负债表", "reportType": "资产负债表", "originalCurrentAmount": "433319.54", "originalPreviousAmount": "451117.46", "matchedMetrics": [{"name": "投资性房地产", "code": "投资性房地产", "description": "投资性房地产", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "invest_real_estate"}], "windcode": "invest_real_estate", "windcodes": ["invest_real_estate"], "windcodeDetails": [{"windcode": "invest_real_estate", "metricName": "投资性房地产", "metricCode": "投资性房地产", "description": "投资性房地产", "unit": "", "reportType": "资产负债表"}], "matchCount": 1, "hasWindCode": true}, {"key": "34", "item": "固定资产", "currentAmount": "166,749,909.87", "previousAmount": "169,454,202.94", "level": 0, "rowType": "normal", "sheetName": "资产负债表", "reportType": "资产负债表", "originalCurrentAmount": "166749909.87", "originalPreviousAmount": "169454202.94", "matchedMetrics": [{"name": "处置固定资产、无形资产和其他长期资产的损失", "code": "处置固定资产、无形资产和其他长期资产的损失", "description": "处置固定资产、无形资产和其他长期资产的损失", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "loss_disp_fiolta"}, {"name": "处置固定资产、无形资产和其他长期资产收回的现金净额", "code": "处置固定资产、无形资产和其他长期资产收回的现金净额", "description": "处置固定资产、无形资产和其他长期资产收回的现金净额", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "net_cash_recp_disp_fiolta"}, {"name": "融资租入固定资产", "code": "融资租入固定资产", "description": "融资租入固定资产", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "fa_fnc_leases"}, {"name": "购建固定资产、无形资产和其他长期资产支付的现金", "code": "购建固定资产、无形资产和其他长期资产支付的现金", "description": "购建固定资产、无形资产和其他长期资产支付的现金", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "cash_pay_acq_const_fiolta"}, {"name": "固定资产报废损失", "code": "固定资产报废损失", "description": "固定资产报废损失", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "loss_scr_fa"}, {"name": "固定资产(合计)", "code": "固定资产(合计)", "description": "固定资产(合计)", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "fix_assets_tot"}, {"name": "固定资产清理", "code": "固定资产清理", "description": "固定资产清理", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "fix_assets_disp"}, {"name": "固定资产", "code": "固定资产", "description": "固定资产", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "fix_assets"}, {"name": "固定资产折旧、油气资产折耗、生产性生物资产折旧", "code": "固定资产折旧、油气资产折耗、生产性生物资产折旧", "description": "其中:固定资产折旧、油气资产折耗、生产性生物资产折旧", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "depr_fa_coga_dpba"}], "windcode": "loss_disp_fiolta", "windcodes": ["loss_disp_fiolta", "net_cash_recp_disp_fiolta", "fa_fnc_leases", "cash_pay_acq_const_fiolta", "loss_scr_fa", "fix_assets_tot", "fix_assets_disp", "fix_assets", "depr_fa_coga_dpba"], "windcodeDetails": [{"windcode": "loss_disp_fiolta", "metricName": "处置固定资产、无形资产和其他长期资产的损失", "metricCode": "处置固定资产、无形资产和其他长期资产的损失", "description": "处置固定资产、无形资产和其他长期资产的损失", "unit": "", "reportType": "资产负债表"}, {"windcode": "net_cash_recp_disp_fiolta", "metricName": "处置固定资产、无形资产和其他长期资产收回的现金净额", "metricCode": "处置固定资产、无形资产和其他长期资产收回的现金净额", "description": "处置固定资产、无形资产和其他长期资产收回的现金净额", "unit": "", "reportType": "资产负债表"}, {"windcode": "fa_fnc_leases", "metricName": "融资租入固定资产", "metricCode": "融资租入固定资产", "description": "融资租入固定资产", "unit": "", "reportType": "资产负债表"}, {"windcode": "cash_pay_acq_const_fiolta", "metricName": "购建固定资产、无形资产和其他长期资产支付的现金", "metricCode": "购建固定资产、无形资产和其他长期资产支付的现金", "description": "购建固定资产、无形资产和其他长期资产支付的现金", "unit": "", "reportType": "资产负债表"}, {"windcode": "loss_scr_fa", "metricName": "固定资产报废损失", "metricCode": "固定资产报废损失", "description": "固定资产报废损失", "unit": "", "reportType": "资产负债表"}, {"windcode": "fix_assets_tot", "metricName": "固定资产(合计)", "metricCode": "固定资产(合计)", "description": "固定资产(合计)", "unit": "", "reportType": "资产负债表"}, {"windcode": "fix_assets_disp", "metricName": "固定资产清理", "metricCode": "固定资产清理", "description": "固定资产清理", "unit": "", "reportType": "资产负债表"}, {"windcode": "fix_assets", "metricName": "固定资产", "metricCode": "固定资产", "description": "固定资产", "unit": "", "reportType": "资产负债表"}, {"windcode": "depr_fa_coga_dpba", "metricName": "固定资产折旧、油气资产折耗、生产性生物资产折旧", "metricCode": "固定资产折旧、油气资产折耗、生产性生物资产折旧", "description": "其中:固定资产折旧、油气资产折耗、生产性生物资产折旧", "unit": "", "reportType": "资产负债表"}], "matchCount": 9, "hasWindCode": true}, {"key": "35", "item": "在建工程", "currentAmount": "", "previousAmount": "", "level": 0, "rowType": "normal", "sheetName": "资产负债表", "reportType": "资产负债表", "originalCurrentAmount": "", "originalPreviousAmount": "", "matchedMetrics": [{"name": "在建工程", "code": "在建工程", "description": "在建工程", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "const_in_prog"}, {"name": "在建工程(合计)", "code": "在建工程(合计)", "description": "在建工程(合计)", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "const_in_prog_tot"}], "windcode": "const_in_prog", "windcodes": ["const_in_prog", "const_in_prog_tot"], "windcodeDetails": [{"windcode": "const_in_prog", "metricName": "在建工程", "metricCode": "在建工程", "description": "在建工程", "unit": "", "reportType": "资产负债表"}, {"windcode": "const_in_prog_tot", "metricName": "在建工程(合计)", "metricCode": "在建工程(合计)", "description": "在建工程(合计)", "unit": "", "reportType": "资产负债表"}], "matchCount": 2, "hasWindCode": true}, {"key": "36", "item": "生产性生物资产", "currentAmount": "", "previousAmount": "", "level": 0, "rowType": "normal", "sheetName": "资产负债表", "reportType": "资产负债表", "originalCurrentAmount": "", "originalPreviousAmount": "", "matchedMetrics": [{"name": "固定资产折旧、油气资产折耗、生产性生物资产折旧", "code": "固定资产折旧、油气资产折耗、生产性生物资产折旧", "description": "其中:固定资产折旧、油气资产折耗、生产性生物资产折旧", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "depr_fa_coga_dpba"}, {"name": "生产性生物资产", "code": "生产性生物资产", "description": "生产性生物资产", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "productive_bio_assets"}], "windcode": "depr_fa_coga_dpba", "windcodes": ["depr_fa_coga_dpba", "productive_bio_assets"], "windcodeDetails": [{"windcode": "depr_fa_coga_dpba", "metricName": "固定资产折旧、油气资产折耗、生产性生物资产折旧", "metricCode": "固定资产折旧、油气资产折耗、生产性生物资产折旧", "description": "其中:固定资产折旧、油气资产折耗、生产性生物资产折旧", "unit": "", "reportType": "资产负债表"}, {"windcode": "productive_bio_assets", "metricName": "生产性生物资产", "metricCode": "生产性生物资产", "description": "生产性生物资产", "unit": "", "reportType": "资产负债表"}], "matchCount": 2, "hasWindCode": true}, {"key": "37", "item": "油气资产", "currentAmount": "", "previousAmount": "", "level": 0, "rowType": "normal", "sheetName": "资产负债表", "reportType": "资产负债表", "originalCurrentAmount": "", "originalPreviousAmount": "", "matchedMetrics": [{"name": "油气资产", "code": "油气资产", "description": "油气资产", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "oil_and_natural_gas_assets"}, {"name": "固定资产折旧、油气资产折耗、生产性生物资产折旧", "code": "固定资产折旧、油气资产折耗、生产性生物资产折旧", "description": "其中:固定资产折旧、油气资产折耗、生产性生物资产折旧", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "depr_fa_coga_dpba"}], "windcode": "oil_and_natural_gas_assets", "windcodes": ["oil_and_natural_gas_assets", "depr_fa_coga_dpba"], "windcodeDetails": [{"windcode": "oil_and_natural_gas_assets", "metricName": "油气资产", "metricCode": "油气资产", "description": "油气资产", "unit": "", "reportType": "资产负债表"}, {"windcode": "depr_fa_coga_dpba", "metricName": "固定资产折旧、油气资产折耗、生产性生物资产折旧", "metricCode": "固定资产折旧、油气资产折耗、生产性生物资产折旧", "description": "其中:固定资产折旧、油气资产折耗、生产性生物资产折旧", "unit": "", "reportType": "资产负债表"}], "matchCount": 2, "hasWindCode": true}, {"key": "38", "item": "使用权资产", "currentAmount": "230,706,101.30", "previousAmount": "189,230,435.30", "level": 0, "rowType": "normal", "sheetName": "资产负债表", "reportType": "资产负债表", "originalCurrentAmount": "230706101.3", "originalPreviousAmount": "189230435.3", "matchedMetrics": [{"name": "使用权资产", "code": "使用权资产", "description": "使用权资产", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "prop_right_use"}, {"name": "使用权资产折旧", "code": "使用权资产折旧", "description": "使用权资产折旧", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "depre_prop_right_use"}], "windcode": "prop_right_use", "windcodes": ["prop_right_use", "depre_prop_right_use"], "windcodeDetails": [{"windcode": "prop_right_use", "metricName": "使用权资产", "metricCode": "使用权资产", "description": "使用权资产", "unit": "", "reportType": "资产负债表"}, {"windcode": "depre_prop_right_use", "metricName": "使用权资产折旧", "metricCode": "使用权资产折旧", "description": "使用权资产折旧", "unit": "", "reportType": "资产负债表"}], "matchCount": 2, "hasWindCode": true}, {"key": "39", "item": "无形资产", "currentAmount": "196,121,325.79", "previousAmount": "147,328,607.88", "level": 0, "rowType": "normal", "sheetName": "资产负债表", "reportType": "资产负债表", "originalCurrentAmount": "196121325.79", "originalPreviousAmount": "147328607.88", "matchedMetrics": [{"name": "处置固定资产、无形资产和其他长期资产的损失", "code": "处置固定资产、无形资产和其他长期资产的损失", "description": "处置固定资产、无形资产和其他长期资产的损失", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "loss_disp_fiolta"}, {"name": "处置固定资产、无形资产和其他长期资产收回的现金净额", "code": "处置固定资产、无形资产和其他长期资产收回的现金净额", "description": "处置固定资产、无形资产和其他长期资产收回的现金净额", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "net_cash_recp_disp_fiolta"}, {"name": "购建固定资产、无形资产和其他长期资产支付的现金", "code": "购建固定资产、无形资产和其他长期资产支付的现金", "description": "购建固定资产、无形资产和其他长期资产支付的现金", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "cash_pay_acq_const_fiolta"}, {"name": "无形资产摊销", "code": "无形资产摊销", "description": "无形资产摊销", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "amort_intang_assets"}, {"name": "数据资源(无形资产)", "code": "数据资源(无形资产)", "description": "数据资源(无形资产)", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "data_intang_assets"}, {"name": "无形资产", "code": "无形资产", "description": "无形资产", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "intang_assets"}], "windcode": "loss_disp_fiolta", "windcodes": ["loss_disp_fiolta", "net_cash_recp_disp_fiolta", "cash_pay_acq_const_fiolta", "amort_intang_assets", "data_intang_assets", "intang_assets"], "windcodeDetails": [{"windcode": "loss_disp_fiolta", "metricName": "处置固定资产、无形资产和其他长期资产的损失", "metricCode": "处置固定资产、无形资产和其他长期资产的损失", "description": "处置固定资产、无形资产和其他长期资产的损失", "unit": "", "reportType": "资产负债表"}, {"windcode": "net_cash_recp_disp_fiolta", "metricName": "处置固定资产、无形资产和其他长期资产收回的现金净额", "metricCode": "处置固定资产、无形资产和其他长期资产收回的现金净额", "description": "处置固定资产、无形资产和其他长期资产收回的现金净额", "unit": "", "reportType": "资产负债表"}, {"windcode": "cash_pay_acq_const_fiolta", "metricName": "购建固定资产、无形资产和其他长期资产支付的现金", "metricCode": "购建固定资产、无形资产和其他长期资产支付的现金", "description": "购建固定资产、无形资产和其他长期资产支付的现金", "unit": "", "reportType": "资产负债表"}, {"windcode": "amort_intang_assets", "metricName": "无形资产摊销", "metricCode": "无形资产摊销", "description": "无形资产摊销", "unit": "", "reportType": "资产负债表"}, {"windcode": "data_intang_assets", "metricName": "数据资源(无形资产)", "metricCode": "数据资源(无形资产)", "description": "数据资源(无形资产)", "unit": "", "reportType": "资产负债表"}, {"windcode": "intang_assets", "metricName": "无形资产", "metricCode": "无形资产", "description": "无形资产", "unit": "", "reportType": "资产负债表"}], "matchCount": 6, "hasWindCode": true}, {"key": "40", "item": "其中：数据资源", "currentAmount": "", "previousAmount": "", "level": 0, "rowType": "normal", "sheetName": "资产负债表", "reportType": "资产负债表", "originalCurrentAmount": "", "originalPreviousAmount": "", "matchedMetrics": [], "windcode": null, "windcodes": [], "windcodeDetails": [], "matchCount": 0, "hasWindCode": false}, {"key": "41", "item": "开发支出", "currentAmount": "70,243,381.75", "previousAmount": "37,781,524.57", "level": 0, "rowType": "normal", "sheetName": "资产负债表", "reportType": "资产负债表", "originalCurrentAmount": "70243381.75", "originalPreviousAmount": "37781524.57", "matchedMetrics": [{"name": "数据资源(开发支出)", "code": "数据资源(开发支出)", "description": "数据资源(开发支出)", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "data_rs_costs"}, {"name": "开发支出", "code": "开发支出", "description": "开发支出", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "r_and_d_costs"}], "windcode": "data_rs_costs", "windcodes": ["data_rs_costs", "r_and_d_costs"], "windcodeDetails": [{"windcode": "data_rs_costs", "metricName": "数据资源(开发支出)", "metricCode": "数据资源(开发支出)", "description": "数据资源(开发支出)", "unit": "", "reportType": "资产负债表"}, {"windcode": "r_and_d_costs", "metricName": "开发支出", "metricCode": "开发支出", "description": "开发支出", "unit": "", "reportType": "资产负债表"}], "matchCount": 2, "hasWindCode": true}, {"key": "42", "item": "其中：数据资源", "currentAmount": "", "previousAmount": "", "level": 0, "rowType": "normal", "sheetName": "资产负债表", "reportType": "资产负债表", "originalCurrentAmount": "", "originalPreviousAmount": "", "matchedMetrics": [], "windcode": null, "windcodes": [], "windcodeDetails": [], "matchCount": 0, "hasWindCode": false}, {"key": "43", "item": "商誉", "currentAmount": "", "previousAmount": "", "level": 0, "rowType": "normal", "sheetName": "资产负债表", "reportType": "资产负债表", "originalCurrentAmount": "", "originalPreviousAmount": "", "matchedMetrics": [{"name": "商誉", "code": "商誉", "description": "商誉", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "goodwill"}], "windcode": "goodwill", "windcodes": ["goodwill"], "windcodeDetails": [{"windcode": "goodwill", "metricName": "商誉", "metricCode": "商誉", "description": "商誉", "unit": "", "reportType": "资产负债表"}], "matchCount": 1, "hasWindCode": true}, {"key": "44", "item": "长期待摊费用", "currentAmount": "26,751,714.53", "previousAmount": "18,009,690.16", "level": 0, "rowType": "normal", "sheetName": "资产负债表", "reportType": "资产负债表", "originalCurrentAmount": "26751714.53", "originalPreviousAmount": "18009690.16", "matchedMetrics": [{"name": "长期待摊费用", "code": "长期待摊费用", "description": "长期待摊费用", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "long_term_deferred_exp"}, {"name": "长期待摊费用摊销", "code": "长期待摊费用摊销", "description": "长期待摊费用摊销", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "amort_lt_deferred_exp"}, {"name": "待摊费用", "code": "待摊费用", "description": "待摊费用", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "deferred_exp"}], "windcode": "long_term_deferred_exp", "windcodes": ["long_term_deferred_exp", "amort_lt_deferred_exp", "deferred_exp"], "windcodeDetails": [{"windcode": "long_term_deferred_exp", "metricName": "长期待摊费用", "metricCode": "长期待摊费用", "description": "长期待摊费用", "unit": "", "reportType": "资产负债表"}, {"windcode": "amort_lt_deferred_exp", "metricName": "长期待摊费用摊销", "metricCode": "长期待摊费用摊销", "description": "长期待摊费用摊销", "unit": "", "reportType": "资产负债表"}, {"windcode": "deferred_exp", "metricName": "待摊费用", "metricCode": "待摊费用", "description": "待摊费用", "unit": "", "reportType": "资产负债表"}], "matchCount": 3, "hasWindCode": true}, {"key": "45", "item": "递延所得税资产", "currentAmount": "七、29", "previousAmount": "170,976,894.36", "level": 0, "rowType": "normal", "sheetName": "资产负债表", "reportType": "资产负债表", "originalCurrentAmount": "七、29", "originalPreviousAmount": "*********.36", "matchedMetrics": [{"name": "递延所得税资产减少", "code": "递延所得税资产减少", "description": "其中:递延所得税资产减少", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "decr_deferred_inc_tax_assets"}, {"name": "递延所得税资产", "code": "递延所得税资产", "description": "递延所得税资产", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "deferred_tax_assets"}, {"name": "所得税", "code": "所得税", "description": "所得税", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "tax"}, {"name": "递延所得税\r\n", "code": "递延所得税\r\n", "description": "递延所得税\r\n", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "stmnote_incometax_5"}], "windcode": "decr_deferred_inc_tax_assets", "windcodes": ["decr_deferred_inc_tax_assets", "deferred_tax_assets", "tax", "stmnote_incometax_5"], "windcodeDetails": [{"windcode": "decr_deferred_inc_tax_assets", "metricName": "递延所得税资产减少", "metricCode": "递延所得税资产减少", "description": "其中:递延所得税资产减少", "unit": "", "reportType": "资产负债表"}, {"windcode": "deferred_tax_assets", "metricName": "递延所得税资产", "metricCode": "递延所得税资产", "description": "递延所得税资产", "unit": "", "reportType": "资产负债表"}, {"windcode": "tax", "metricName": "所得税", "metricCode": "所得税", "description": "所得税", "unit": "", "reportType": "资产负债表"}, {"windcode": "stmnote_incometax_5", "metricName": "递延所得税", "metricCode": "递延所得税\r\n", "description": "递延所得税\r\n", "unit": "", "reportType": "资产负债表"}], "matchCount": 4, "hasWindCode": true}, {"key": "46", "item": "其他非流动资产", "currentAmount": "七、30", "previousAmount": "1,296,033.91", "level": 0, "rowType": "normal", "sheetName": "资产负债表", "reportType": "资产负债表", "originalCurrentAmount": "七、30", "originalPreviousAmount": "1296033.91", "matchedMetrics": [{"name": "其他", "code": "其他", "description": "其他", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "others"}, {"name": "其他非流动资产", "code": "其他非流动资产", "description": "其他非流动资产", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "oth_non_cur_assets"}], "windcode": "others", "windcodes": ["others", "oth_non_cur_assets"], "windcodeDetails": [{"windcode": "others", "metricName": "其他", "metricCode": "其他", "description": "其他", "unit": "", "reportType": "资产负债表"}, {"windcode": "oth_non_cur_assets", "metricName": "其他非流动资产", "metricCode": "其他非流动资产", "description": "其他非流动资产", "unit": "", "reportType": "资产负债表"}], "matchCount": 2, "hasWindCode": true}, {"key": "47", "item": "非流动资产合计", "currentAmount": "", "previousAmount": "1,120,622,105.76", "level": 0, "rowType": "normal", "sheetName": "资产负债表", "reportType": "资产负债表", "originalCurrentAmount": "", "originalPreviousAmount": "1120622105.76", "matchedMetrics": [{"name": "非流动资产合计", "code": "非流动资产合计", "description": "非流动资产合计", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "tot_non_cur_assets"}, {"name": "流动资产合计", "code": "流动资产合计", "description": "流动资产合计", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "tot_cur_assets"}], "windcode": "tot_non_cur_assets", "windcodes": ["tot_non_cur_assets", "tot_cur_assets"], "windcodeDetails": [{"windcode": "tot_non_cur_assets", "metricName": "非流动资产合计", "metricCode": "非流动资产合计", "description": "非流动资产合计", "unit": "", "reportType": "资产负债表"}, {"windcode": "tot_cur_assets", "metricName": "流动资产合计", "metricCode": "流动资产合计", "description": "流动资产合计", "unit": "", "reportType": "资产负债表"}], "matchCount": 2, "hasWindCode": true}, {"key": "48", "item": "资产总计", "currentAmount": "", "previousAmount": "11,938,269,698.39", "level": 0, "rowType": "normal", "sheetName": "资产负债表", "reportType": "资产负债表", "originalCurrentAmount": "", "originalPreviousAmount": "11938269698.39", "matchedMetrics": [{"name": "资产总计", "code": "资产总计", "description": "资产总计", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "tot_assets"}], "windcode": "tot_assets", "windcodes": ["tot_assets"], "windcodeDetails": [{"windcode": "tot_assets", "metricName": "资产总计", "metricCode": "资产总计", "description": "资产总计", "unit": "", "reportType": "资产负债表"}], "matchCount": 1, "hasWindCode": true}, {"key": "49", "item": "流动负债：", "currentAmount": "", "previousAmount": "", "level": 0, "rowType": "normal", "sheetName": "资产负债表", "reportType": "资产负债表", "originalCurrentAmount": "", "originalPreviousAmount": "", "matchedMetrics": [{"name": "流动负债差额说明(特殊报表科目)", "code": "流动负债差额说明(特殊报表科目)", "description": "流动负债差额说明(特殊报表科目)", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "cur_liab_gap_detail"}, {"name": "一年内到期的非流动负债", "code": "一年内到期的非流动负债", "description": "一年内到期的非流动负债", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "non_cur_liab_due_within_1y"}, {"name": "非流动负债差额说明(特殊报表科目)", "code": "非流动负债差额说明(特殊报表科目)", "description": "非流动负债差额说明(特殊报表科目)", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "non_cur_liab_gap_detail"}, {"name": "非流动负债合计", "code": "非流动负债合计", "description": "非流动负债合计", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "tot_non_cur_liab"}, {"name": "递延收益-非流动负债", "code": "递延收益-非流动负债", "description": "递延收益", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "deferred_inc_non_cur_liab"}, {"name": "其他流动负债", "code": "其他流动负债", "description": "其他流动负债", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "oth_cur_liab"}, {"name": "非流动负债差额(合计平衡项目)", "code": "非流动负债差额(合计平衡项目)", "description": "非流动负债差额(合计平衡项目)", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "non_cur_liab_netting"}, {"name": "其他非流动负债", "code": "其他非流动负债", "description": "其他非流动负债", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "oth_non_cur_liab"}, {"name": "递延收益-流动负债", "code": "递延收益-流动负债", "description": "递延收益-流动负债", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "deferred_inc_cur_liab"}, {"name": "流动负债差额(合计平衡项目)", "code": "流动负债差额(合计平衡项目)", "description": "流动负债差额(合计平衡项目)", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "cur_liab_netting"}, {"name": "流动负债差额(特殊报表科目)", "code": "流动负债差额(特殊报表科目)", "description": "流动负债差额(特殊报表科目)", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "cur_liab_gap"}, {"name": "流动负债合计", "code": "流动负债合计", "description": "流动负债合计", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "tot_cur_liab"}, {"name": "非流动负债差额(特殊报表科目)", "code": "非流动负债差额(特殊报表科目)", "description": "非流动负债差额(特殊报表科目)", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "non_cur_liab_gap"}], "windcode": "cur_liab_gap_detail", "windcodes": ["cur_liab_gap_detail", "non_cur_liab_due_within_1y", "non_cur_liab_gap_detail", "tot_non_cur_liab", "deferred_inc_non_cur_liab", "oth_cur_liab", "non_cur_liab_netting", "oth_non_cur_liab", "deferred_inc_cur_liab", "cur_liab_netting", "cur_liab_gap", "tot_cur_liab", "non_cur_liab_gap"], "windcodeDetails": [{"windcode": "cur_liab_gap_detail", "metricName": "流动负债差额说明(特殊报表科目)", "metricCode": "流动负债差额说明(特殊报表科目)", "description": "流动负债差额说明(特殊报表科目)", "unit": "", "reportType": "资产负债表"}, {"windcode": "non_cur_liab_due_within_1y", "metricName": "一年内到期的非流动负债", "metricCode": "一年内到期的非流动负债", "description": "一年内到期的非流动负债", "unit": "", "reportType": "资产负债表"}, {"windcode": "non_cur_liab_gap_detail", "metricName": "非流动负债差额说明(特殊报表科目)", "metricCode": "非流动负债差额说明(特殊报表科目)", "description": "非流动负债差额说明(特殊报表科目)", "unit": "", "reportType": "资产负债表"}, {"windcode": "tot_non_cur_liab", "metricName": "非流动负债合计", "metricCode": "非流动负债合计", "description": "非流动负债合计", "unit": "", "reportType": "资产负债表"}, {"windcode": "deferred_inc_non_cur_liab", "metricName": "递延收益-非流动负债", "metricCode": "递延收益-非流动负债", "description": "递延收益", "unit": "", "reportType": "资产负债表"}, {"windcode": "oth_cur_liab", "metricName": "其他流动负债", "metricCode": "其他流动负债", "description": "其他流动负债", "unit": "", "reportType": "资产负债表"}, {"windcode": "non_cur_liab_netting", "metricName": "非流动负债差额(合计平衡项目)", "metricCode": "非流动负债差额(合计平衡项目)", "description": "非流动负债差额(合计平衡项目)", "unit": "", "reportType": "资产负债表"}, {"windcode": "oth_non_cur_liab", "metricName": "其他非流动负债", "metricCode": "其他非流动负债", "description": "其他非流动负债", "unit": "", "reportType": "资产负债表"}, {"windcode": "deferred_inc_cur_liab", "metricName": "递延收益-流动负债", "metricCode": "递延收益-流动负债", "description": "递延收益-流动负债", "unit": "", "reportType": "资产负债表"}, {"windcode": "cur_liab_netting", "metricName": "流动负债差额(合计平衡项目)", "metricCode": "流动负债差额(合计平衡项目)", "description": "流动负债差额(合计平衡项目)", "unit": "", "reportType": "资产负债表"}, {"windcode": "cur_liab_gap", "metricName": "流动负债差额(特殊报表科目)", "metricCode": "流动负债差额(特殊报表科目)", "description": "流动负债差额(特殊报表科目)", "unit": "", "reportType": "资产负债表"}, {"windcode": "tot_cur_liab", "metricName": "流动负债合计", "metricCode": "流动负债合计", "description": "流动负债合计", "unit": "", "reportType": "资产负债表"}, {"windcode": "non_cur_liab_gap", "metricName": "非流动负债差额(特殊报表科目)", "metricCode": "非流动负债差额(特殊报表科目)", "description": "非流动负债差额(特殊报表科目)", "unit": "", "reportType": "资产负债表"}], "matchCount": 13, "hasWindCode": true}, {"key": "50", "item": "短期借款", "currentAmount": "458,480,341.00", "previousAmount": "230,832,753.44", "level": 0, "rowType": "normal", "sheetName": "资产负债表", "reportType": "资产负债表", "originalCurrentAmount": "458480341", "originalPreviousAmount": "230832753.44", "matchedMetrics": [{"name": "短期借款", "code": "短期借款", "description": "短期借款", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "st_borrow"}], "windcode": "st_borrow", "windcodes": ["st_borrow"], "windcodeDetails": [{"windcode": "st_borrow", "metricName": "短期借款", "metricCode": "短期借款", "description": "短期借款", "unit": "", "reportType": "资产负债表"}], "matchCount": 1, "hasWindCode": true}, {"key": "51", "item": "向中央银行借款", "currentAmount": "", "previousAmount": "", "level": 0, "rowType": "normal", "sheetName": "资产负债表", "reportType": "资产负债表", "originalCurrentAmount": "", "originalPreviousAmount": "", "matchedMetrics": [{"name": "向中央银行借款净增加额", "code": "向中央银行借款净增加额", "description": "向中央银行借款净增加额", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "net_incr_loans_central_bank"}, {"name": "向中央银行借款", "code": "向中央银行借款", "description": "向中央银行借款", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "borrow_central_bank"}], "windcode": "net_incr_loans_central_bank", "windcodes": ["net_incr_loans_central_bank", "borrow_central_bank"], "windcodeDetails": [{"windcode": "net_incr_loans_central_bank", "metricName": "向中央银行借款净增加额", "metricCode": "向中央银行借款净增加额", "description": "向中央银行借款净增加额", "unit": "", "reportType": "资产负债表"}, {"windcode": "borrow_central_bank", "metricName": "向中央银行借款", "metricCode": "向中央银行借款", "description": "向中央银行借款", "unit": "", "reportType": "资产负债表"}], "matchCount": 2, "hasWindCode": true}, {"key": "52", "item": "拆入资金", "currentAmount": "", "previousAmount": "", "level": 0, "rowType": "normal", "sheetName": "资产负债表", "reportType": "资产负债表", "originalCurrentAmount": "", "originalPreviousAmount": "", "matchedMetrics": [{"name": "拆入资金净增加额", "code": "拆入资金净增加额", "description": "拆入资金净增加额", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "net_incr_loans_other_bank"}, {"name": "向其他金融机构拆入资金净增加额", "code": "向其他金融机构拆入资金净增加额", "description": "向其他金融机构拆入资金净增加额", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "net_incr_fund_borr_ofi"}, {"name": "拆入资金", "code": "拆入资金", "description": "拆入资金", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "loans_oth_banks"}], "windcode": "net_incr_loans_other_bank", "windcodes": ["net_incr_loans_other_bank", "net_incr_fund_borr_ofi", "loans_oth_banks"], "windcodeDetails": [{"windcode": "net_incr_loans_other_bank", "metricName": "拆入资金净增加额", "metricCode": "拆入资金净增加额", "description": "拆入资金净增加额", "unit": "", "reportType": "资产负债表"}, {"windcode": "net_incr_fund_borr_ofi", "metricName": "向其他金融机构拆入资金净增加额", "metricCode": "向其他金融机构拆入资金净增加额", "description": "向其他金融机构拆入资金净增加额", "unit": "", "reportType": "资产负债表"}, {"windcode": "loans_oth_banks", "metricName": "拆入资金", "metricCode": "拆入资金", "description": "拆入资金", "unit": "", "reportType": "资产负债表"}], "matchCount": 3, "hasWindCode": true}, {"key": "53", "item": "交易性金融负债", "currentAmount": "", "previousAmount": "", "level": 0, "rowType": "normal", "sheetName": "资产负债表", "reportType": "资产负债表", "originalCurrentAmount": "", "originalPreviousAmount": "", "matchedMetrics": [{"name": " 交易性金融负债", "code": " 交易性金融负债", "description": "交易性金融负债", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "wgsd_liabs_trading"}], "windcode": "wgsd_liabs_trading", "windcodes": ["wgsd_liabs_trading"], "windcodeDetails": [{"windcode": "wgsd_liabs_trading", "metricName": "交易性金融负债", "metricCode": " 交易性金融负债", "description": "交易性金融负债", "unit": "", "reportType": "资产负债表"}], "matchCount": 1, "hasWindCode": true}, {"key": "54", "item": "衍生金融负债", "currentAmount": "", "previousAmount": "", "level": 0, "rowType": "normal", "sheetName": "资产负债表", "reportType": "资产负债表", "originalCurrentAmount": "", "originalPreviousAmount": "", "matchedMetrics": [{"name": "衍生金融负债", "code": "衍生金融负债", "description": "衍生金融负债", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "derivative_fin_liab"}], "windcode": "derivative_fin_liab", "windcodes": ["derivative_fin_liab"], "windcodeDetails": [{"windcode": "derivative_fin_liab", "metricName": "衍生金融负债", "metricCode": "衍生金融负债", "description": "衍生金融负债", "unit": "", "reportType": "资产负债表"}], "matchCount": 1, "hasWindCode": true}, {"key": "55", "item": "应付票据", "currentAmount": "857,897,444.19", "previousAmount": "138,259,882.09", "level": 0, "rowType": "normal", "sheetName": "资产负债表", "reportType": "资产负债表", "originalCurrentAmount": "857897444.19", "originalPreviousAmount": "138259882.09", "matchedMetrics": [{"name": "应付票据及应付账款", "code": "应付票据及应付账款", "description": "应付票据及应付账款", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "acctandnotes_payable"}, {"name": "应付票据", "code": "应付票据", "description": "应付票据", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "notes_payable"}], "windcode": "acctandnotes_payable", "windcodes": ["acctandnotes_payable", "notes_payable"], "windcodeDetails": [{"windcode": "acctandnotes_payable", "metricName": "应付票据及应付账款", "metricCode": "应付票据及应付账款", "description": "应付票据及应付账款", "unit": "", "reportType": "资产负债表"}, {"windcode": "notes_payable", "metricName": "应付票据", "metricCode": "应付票据", "description": "应付票据", "unit": "", "reportType": "资产负债表"}], "matchCount": 2, "hasWindCode": true}, {"key": "56", "item": "应付账款", "currentAmount": "1,936,628,910.26", "previousAmount": "1,981,529,153.44", "level": 0, "rowType": "normal", "sheetName": "资产负债表", "reportType": "资产负债表", "originalCurrentAmount": "1936628910.26", "originalPreviousAmount": "1981529153.44", "matchedMetrics": [{"name": "应付账款", "code": "应付账款", "description": "应付账款", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "acct_payable"}, {"name": "应付票据及应付账款", "code": "应付票据及应付账款", "description": "应付票据及应付账款", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "acctandnotes_payable"}], "windcode": "acct_payable", "windcodes": ["acct_payable", "acctandnotes_payable"], "windcodeDetails": [{"windcode": "acct_payable", "metricName": "应付账款", "metricCode": "应付账款", "description": "应付账款", "unit": "", "reportType": "资产负债表"}, {"windcode": "acctandnotes_payable", "metricName": "应付票据及应付账款", "metricCode": "应付票据及应付账款", "description": "应付票据及应付账款", "unit": "", "reportType": "资产负债表"}], "matchCount": 2, "hasWindCode": true}, {"key": "57", "item": "预收款项", "currentAmount": "", "previousAmount": "", "level": 0, "rowType": "normal", "sheetName": "资产负债表", "reportType": "资产负债表", "originalCurrentAmount": "", "originalPreviousAmount": "", "matchedMetrics": [], "windcode": null, "windcodes": [], "windcodeDetails": [], "matchCount": 0, "hasWindCode": false}, {"key": "58", "item": "合同负债", "currentAmount": "2,825,600,651.13", "previousAmount": "3,453,139,542.19", "level": 0, "rowType": "normal", "sheetName": "资产负债表", "reportType": "资产负债表", "originalCurrentAmount": "2825600651.13", "originalPreviousAmount": "3453139542.19", "matchedMetrics": [{"name": "合同负债", "code": "合同负债", "description": "合同负债", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "cont_liab"}], "windcode": "cont_liab", "windcodes": ["cont_liab"], "windcodeDetails": [{"windcode": "cont_liab", "metricName": "合同负债", "metricCode": "合同负债", "description": "合同负债", "unit": "", "reportType": "资产负债表"}], "matchCount": 1, "hasWindCode": true}, {"key": "59", "item": "卖出回购金融资产款", "currentAmount": "", "previousAmount": "", "level": 0, "rowType": "normal", "sheetName": "资产负债表", "reportType": "资产负债表", "originalCurrentAmount": "", "originalPreviousAmount": "", "matchedMetrics": [{"name": "卖出回购金融资产款", "code": "卖出回购金融资产款", "description": "卖出回购金融资产款", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "fund_sales_fin_assets_rp"}], "windcode": "fund_sales_fin_assets_rp", "windcodes": ["fund_sales_fin_assets_rp"], "windcodeDetails": [{"windcode": "fund_sales_fin_assets_rp", "metricName": "卖出回购金融资产款", "metricCode": "卖出回购金融资产款", "description": "卖出回购金融资产款", "unit": "", "reportType": "资产负债表"}], "matchCount": 1, "hasWindCode": true}, {"key": "60", "item": "吸收存款及同业存放", "currentAmount": "", "previousAmount": "", "level": 0, "rowType": "normal", "sheetName": "资产负债表", "reportType": "资产负债表", "originalCurrentAmount": "", "originalPreviousAmount": "", "matchedMetrics": [{"name": "吸收存款及同业存放", "code": "吸收存款及同业存放", "description": "吸收存款及同业存放", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "deposit_received_ib_deposits"}, {"name": "吸收存款", "code": "吸收存款", "description": "吸收存款", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "cust_bank_dep"}], "windcode": "deposit_received_ib_deposits", "windcodes": ["deposit_received_ib_deposits", "cust_bank_dep"], "windcodeDetails": [{"windcode": "deposit_received_ib_deposits", "metricName": "吸收存款及同业存放", "metricCode": "吸收存款及同业存放", "description": "吸收存款及同业存放", "unit": "", "reportType": "资产负债表"}, {"windcode": "cust_bank_dep", "metricName": "吸收存款", "metricCode": "吸收存款", "description": "吸收存款", "unit": "", "reportType": "资产负债表"}], "matchCount": 2, "hasWindCode": true}, {"key": "61", "item": "代理买卖证券款", "currentAmount": "", "previousAmount": "", "level": 0, "rowType": "normal", "sheetName": "资产负债表", "reportType": "资产负债表", "originalCurrentAmount": "", "originalPreviousAmount": "", "matchedMetrics": [{"name": "代理买卖证券款", "code": "代理买卖证券款", "description": "代理买卖证券款", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "acting_trading_sec"}], "windcode": "acting_trading_sec", "windcodes": ["acting_trading_sec"], "windcodeDetails": [{"windcode": "acting_trading_sec", "metricName": "代理买卖证券款", "metricCode": "代理买卖证券款", "description": "代理买卖证券款", "unit": "", "reportType": "资产负债表"}], "matchCount": 1, "hasWindCode": true}, {"key": "62", "item": "代理承销证券款", "currentAmount": "", "previousAmount": "", "level": 0, "rowType": "normal", "sheetName": "资产负债表", "reportType": "资产负债表", "originalCurrentAmount": "", "originalPreviousAmount": "", "matchedMetrics": [{"name": "代理承销证券款", "code": "代理承销证券款", "description": "代理承销证券款", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "acting_uw_sec"}], "windcode": "acting_uw_sec", "windcodes": ["acting_uw_sec"], "windcodeDetails": [{"windcode": "acting_uw_sec", "metricName": "代理承销证券款", "metricCode": "代理承销证券款", "description": "代理承销证券款", "unit": "", "reportType": "资产负债表"}], "matchCount": 1, "hasWindCode": true}, {"key": "63", "item": "应付职工薪酬", "currentAmount": "229,270,809.52", "previousAmount": "227,847,214.44", "level": 0, "rowType": "normal", "sheetName": "资产负债表", "reportType": "资产负债表", "originalCurrentAmount": "229270809.52", "originalPreviousAmount": "227847214.44", "matchedMetrics": [{"name": "应付职工薪酬", "code": "应付职工薪酬", "description": "应付职工薪酬", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "empl_ben_payable"}, {"name": "长期应付职工薪酬", "code": "长期应付职工薪酬", "description": "长期应付职工薪酬", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "lt_empl_ben_payable"}], "windcode": "empl_ben_payable", "windcodes": ["empl_ben_payable", "lt_empl_ben_payable"], "windcodeDetails": [{"windcode": "empl_ben_payable", "metricName": "应付职工薪酬", "metricCode": "应付职工薪酬", "description": "应付职工薪酬", "unit": "", "reportType": "资产负债表"}, {"windcode": "lt_empl_ben_payable", "metricName": "长期应付职工薪酬", "metricCode": "长期应付职工薪酬", "description": "长期应付职工薪酬", "unit": "", "reportType": "资产负债表"}], "matchCount": 2, "hasWindCode": true}, {"key": "64", "item": "应交税费", "currentAmount": "71,888,597.08", "previousAmount": "97,688,526.63", "level": 0, "rowType": "normal", "sheetName": "资产负债表", "reportType": "资产负债表", "originalCurrentAmount": "71888597.08", "originalPreviousAmount": "97688526.63", "matchedMetrics": [{"name": "应交税费", "code": "应交税费", "description": "应交税费", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "taxes_surcharges_payable"}], "windcode": "taxes_surcharges_payable", "windcodes": ["taxes_surcharges_payable"], "windcodeDetails": [{"windcode": "taxes_surcharges_payable", "metricName": "应交税费", "metricCode": "应交税费", "description": "应交税费", "unit": "", "reportType": "资产负债表"}], "matchCount": 1, "hasWindCode": true}, {"key": "65", "item": "其他应付款", "currentAmount": "61,250,253.28", "previousAmount": "61,397,647.30", "level": 0, "rowType": "normal", "sheetName": "资产负债表", "reportType": "资产负债表", "originalCurrentAmount": "61250253.28", "originalPreviousAmount": "61397647.3", "matchedMetrics": [{"name": "其他应付款(合计)", "code": "其他应付款(合计)", "description": "其他应付款合计", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "oth_payable_tot"}, {"name": "其他", "code": "其他", "description": "其他", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "others"}, {"name": "其他应付款", "code": "其他应付款", "description": "其中:其他应付款", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "oth_payable"}], "windcode": "oth_payable_tot", "windcodes": ["oth_payable_tot", "others", "oth_payable"], "windcodeDetails": [{"windcode": "oth_payable_tot", "metricName": "其他应付款(合计)", "metricCode": "其他应付款(合计)", "description": "其他应付款合计", "unit": "", "reportType": "资产负债表"}, {"windcode": "others", "metricName": "其他", "metricCode": "其他", "description": "其他", "unit": "", "reportType": "资产负债表"}, {"windcode": "oth_payable", "metricName": "其他应付款", "metricCode": "其他应付款", "description": "其中:其他应付款", "unit": "", "reportType": "资产负债表"}], "matchCount": 3, "hasWindCode": true}, {"key": "66", "item": "其中：应付利息", "currentAmount": "", "previousAmount": "", "level": 0, "rowType": "normal", "sheetName": "资产负债表", "reportType": "资产负债表", "originalCurrentAmount": "", "originalPreviousAmount": "", "matchedMetrics": [{"name": "应付利息", "code": "应付利息", "description": "应付利息", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "int_payable"}], "windcode": "int_payable", "windcodes": ["int_payable"], "windcodeDetails": [{"windcode": "int_payable", "metricName": "应付利息", "metricCode": "应付利息", "description": "应付利息", "unit": "", "reportType": "资产负债表"}], "matchCount": 1, "hasWindCode": true}, {"key": "67", "item": "应付股利", "currentAmount": "174,524.00", "previousAmount": "174,524.00", "level": 0, "rowType": "normal", "sheetName": "资产负债表", "reportType": "资产负债表", "originalCurrentAmount": "174524", "originalPreviousAmount": "174524.0", "matchedMetrics": [{"name": "应付股利", "code": "应付股利", "description": "其中:应付股利", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "dvd_payable"}], "windcode": "dvd_payable", "windcodes": ["dvd_payable"], "windcodeDetails": [{"windcode": "dvd_payable", "metricName": "应付股利", "metricCode": "应付股利", "description": "其中:应付股利", "unit": "", "reportType": "资产负债表"}], "matchCount": 1, "hasWindCode": true}, {"key": "68", "item": "应付手续费及佣金", "currentAmount": "", "previousAmount": "", "level": 0, "rowType": "normal", "sheetName": "资产负债表", "reportType": "资产负债表", "originalCurrentAmount": "", "originalPreviousAmount": "", "matchedMetrics": [{"name": "应付手续费及佣金", "code": "应付手续费及佣金", "description": "应付手续费及佣金", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "handling_charges_comm_payable"}], "windcode": "handling_charges_comm_payable", "windcodes": ["handling_charges_comm_payable"], "windcodeDetails": [{"windcode": "handling_charges_comm_payable", "metricName": "应付手续费及佣金", "metricCode": "应付手续费及佣金", "description": "应付手续费及佣金", "unit": "", "reportType": "资产负债表"}], "matchCount": 1, "hasWindCode": true}, {"key": "69", "item": "应付分保账款", "currentAmount": "", "previousAmount": "", "level": 0, "rowType": "normal", "sheetName": "资产负债表", "reportType": "资产负债表", "originalCurrentAmount": "", "originalPreviousAmount": "", "matchedMetrics": [{"name": "应付分保账款", "code": "应付分保账款", "description": "应付分保账款", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "payable_to_reinsurer"}], "windcode": "payable_to_reinsurer", "windcodes": ["payable_to_reinsurer"], "windcodeDetails": [{"windcode": "payable_to_reinsurer", "metricName": "应付分保账款", "metricCode": "应付分保账款", "description": "应付分保账款", "unit": "", "reportType": "资产负债表"}], "matchCount": 1, "hasWindCode": true}, {"key": "70", "item": "持有待售负债", "currentAmount": "", "previousAmount": "", "level": 0, "rowType": "normal", "sheetName": "资产负债表", "reportType": "资产负债表", "originalCurrentAmount": "", "originalPreviousAmount": "", "matchedMetrics": [], "windcode": null, "windcodes": [], "windcodeDetails": [], "matchCount": 0, "hasWindCode": false}, {"key": "71", "item": "一年内到期的非流动负债", "currentAmount": "66,834,657.04", "previousAmount": "60,060,838.60", "level": 0, "rowType": "normal", "sheetName": "资产负债表", "reportType": "资产负债表", "originalCurrentAmount": "66834657.04", "originalPreviousAmount": "60060838.6", "matchedMetrics": [{"name": "一年内到期的非流动负债", "code": "一年内到期的非流动负债", "description": "一年内到期的非流动负债", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "non_cur_liab_due_within_1y"}], "windcode": "non_cur_liab_due_within_1y", "windcodes": ["non_cur_liab_due_within_1y"], "windcodeDetails": [{"windcode": "non_cur_liab_due_within_1y", "metricName": "一年内到期的非流动负债", "metricCode": "一年内到期的非流动负债", "description": "一年内到期的非流动负债", "unit": "", "reportType": "资产负债表"}], "matchCount": 1, "hasWindCode": true}, {"key": "72", "item": "其他流动负债", "currentAmount": "7,986,003.05", "previousAmount": "3,166,189.54", "level": 0, "rowType": "normal", "sheetName": "资产负债表", "reportType": "资产负债表", "originalCurrentAmount": "7986003.05", "originalPreviousAmount": "3166189.54", "matchedMetrics": [{"name": "其他流动负债", "code": "其他流动负债", "description": "其他流动负债", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "oth_cur_liab"}, {"name": "其他", "code": "其他", "description": "其他", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "others"}], "windcode": "oth_cur_liab", "windcodes": ["oth_cur_liab", "others"], "windcodeDetails": [{"windcode": "oth_cur_liab", "metricName": "其他流动负债", "metricCode": "其他流动负债", "description": "其他流动负债", "unit": "", "reportType": "资产负债表"}, {"windcode": "others", "metricName": "其他", "metricCode": "其他", "description": "其他", "unit": "", "reportType": "资产负债表"}], "matchCount": 2, "hasWindCode": true}, {"key": "73", "item": "流动负债合计", "currentAmount": "6,515,837,666.55", "previousAmount": "6,253,921,747.67", "level": 0, "rowType": "normal", "sheetName": "资产负债表", "reportType": "资产负债表", "originalCurrentAmount": "6515837666.55", "originalPreviousAmount": "6253921747.67", "matchedMetrics": [{"name": "负债合计", "code": "负债合计", "description": "负债合计", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "tot_liab"}, {"name": "非流动负债合计", "code": "非流动负债合计", "description": "非流动负债合计", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "tot_non_cur_liab"}, {"name": "流动负债合计", "code": "流动负债合计", "description": "流动负债合计", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "tot_cur_liab"}], "windcode": "tot_liab", "windcodes": ["tot_liab", "tot_non_cur_liab", "tot_cur_liab"], "windcodeDetails": [{"windcode": "tot_liab", "metricName": "负债合计", "metricCode": "负债合计", "description": "负债合计", "unit": "", "reportType": "资产负债表"}, {"windcode": "tot_non_cur_liab", "metricName": "非流动负债合计", "metricCode": "非流动负债合计", "description": "非流动负债合计", "unit": "", "reportType": "资产负债表"}, {"windcode": "tot_cur_liab", "metricName": "流动负债合计", "metricCode": "流动负债合计", "description": "流动负债合计", "unit": "", "reportType": "资产负债表"}], "matchCount": 3, "hasWindCode": true}, {"key": "74", "item": "非流动负债：", "currentAmount": "", "previousAmount": "", "level": 0, "rowType": "normal", "sheetName": "资产负债表", "reportType": "资产负债表", "originalCurrentAmount": "", "originalPreviousAmount": "", "matchedMetrics": [{"name": "一年内到期的非流动负债", "code": "一年内到期的非流动负债", "description": "一年内到期的非流动负债", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "non_cur_liab_due_within_1y"}, {"name": "非流动负债差额说明(特殊报表科目)", "code": "非流动负债差额说明(特殊报表科目)", "description": "非流动负债差额说明(特殊报表科目)", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "non_cur_liab_gap_detail"}, {"name": "非流动负债合计", "code": "非流动负债合计", "description": "非流动负债合计", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "tot_non_cur_liab"}, {"name": "递延收益-非流动负债", "code": "递延收益-非流动负债", "description": "递延收益", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "deferred_inc_non_cur_liab"}, {"name": "非流动负债差额(合计平衡项目)", "code": "非流动负债差额(合计平衡项目)", "description": "非流动负债差额(合计平衡项目)", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "non_cur_liab_netting"}, {"name": "其他非流动负债", "code": "其他非流动负债", "description": "其他非流动负债", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "oth_non_cur_liab"}, {"name": "非流动负债差额(特殊报表科目)", "code": "非流动负债差额(特殊报表科目)", "description": "非流动负债差额(特殊报表科目)", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "non_cur_liab_gap"}], "windcode": "non_cur_liab_due_within_1y", "windcodes": ["non_cur_liab_due_within_1y", "non_cur_liab_gap_detail", "tot_non_cur_liab", "deferred_inc_non_cur_liab", "non_cur_liab_netting", "oth_non_cur_liab", "non_cur_liab_gap"], "windcodeDetails": [{"windcode": "non_cur_liab_due_within_1y", "metricName": "一年内到期的非流动负债", "metricCode": "一年内到期的非流动负债", "description": "一年内到期的非流动负债", "unit": "", "reportType": "资产负债表"}, {"windcode": "non_cur_liab_gap_detail", "metricName": "非流动负债差额说明(特殊报表科目)", "metricCode": "非流动负债差额说明(特殊报表科目)", "description": "非流动负债差额说明(特殊报表科目)", "unit": "", "reportType": "资产负债表"}, {"windcode": "tot_non_cur_liab", "metricName": "非流动负债合计", "metricCode": "非流动负债合计", "description": "非流动负债合计", "unit": "", "reportType": "资产负债表"}, {"windcode": "deferred_inc_non_cur_liab", "metricName": "递延收益-非流动负债", "metricCode": "递延收益-非流动负债", "description": "递延收益", "unit": "", "reportType": "资产负债表"}, {"windcode": "non_cur_liab_netting", "metricName": "非流动负债差额(合计平衡项目)", "metricCode": "非流动负债差额(合计平衡项目)", "description": "非流动负债差额(合计平衡项目)", "unit": "", "reportType": "资产负债表"}, {"windcode": "oth_non_cur_liab", "metricName": "其他非流动负债", "metricCode": "其他非流动负债", "description": "其他非流动负债", "unit": "", "reportType": "资产负债表"}, {"windcode": "non_cur_liab_gap", "metricName": "非流动负债差额(特殊报表科目)", "metricCode": "非流动负债差额(特殊报表科目)", "description": "非流动负债差额(特殊报表科目)", "unit": "", "reportType": "资产负债表"}], "matchCount": 7, "hasWindCode": true}, {"key": "75", "item": "保险合同准备金", "currentAmount": "", "previousAmount": "", "level": 0, "rowType": "normal", "sheetName": "资产负债表", "reportType": "资产负债表", "originalCurrentAmount": "", "originalPreviousAmount": "", "matchedMetrics": [{"name": "保险合同准备金", "code": "保险合同准备金", "description": "保险合同准备金", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "rsrv_insur_cont"}], "windcode": "rsrv_insur_cont", "windcodes": ["rsrv_insur_cont"], "windcodeDetails": [{"windcode": "rsrv_insur_cont", "metricName": "保险合同准备金", "metricCode": "保险合同准备金", "description": "保险合同准备金", "unit": "", "reportType": "资产负债表"}], "matchCount": 1, "hasWindCode": true}, {"key": "76", "item": "长期借款", "currentAmount": "", "previousAmount": "", "level": 0, "rowType": "normal", "sheetName": "资产负债表", "reportType": "资产负债表", "originalCurrentAmount": "", "originalPreviousAmount": "", "matchedMetrics": [{"name": "长期借款", "code": "长期借款", "description": "长期借款", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "lt_borrow"}], "windcode": "lt_borrow", "windcodes": ["lt_borrow"], "windcodeDetails": [{"windcode": "lt_borrow", "metricName": "长期借款", "metricCode": "长期借款", "description": "长期借款", "unit": "", "reportType": "资产负债表"}], "matchCount": 1, "hasWindCode": true}, {"key": "77", "item": "应付债券", "currentAmount": "", "previousAmount": "", "level": 0, "rowType": "normal", "sheetName": "资产负债表", "reportType": "资产负债表", "originalCurrentAmount": "", "originalPreviousAmount": "", "matchedMetrics": [{"name": "应付债券", "code": "应付债券", "description": "应付债券", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "bonds_payable"}], "windcode": "bonds_payable", "windcodes": ["bonds_payable"], "windcodeDetails": [{"windcode": "bonds_payable", "metricName": "应付债券", "metricCode": "应付债券", "description": "应付债券", "unit": "", "reportType": "资产负债表"}], "matchCount": 1, "hasWindCode": true}, {"key": "78", "item": "其中：优先股", "currentAmount": "", "previousAmount": "", "level": 0, "rowType": "normal", "sheetName": "资产负债表", "reportType": "资产负债表", "originalCurrentAmount": "", "originalPreviousAmount": "", "matchedMetrics": [], "windcode": null, "windcodes": [], "windcodeDetails": [], "matchCount": 0, "hasWindCode": false}, {"key": "79", "item": "永续债", "currentAmount": "", "previousAmount": "", "level": 0, "rowType": "normal", "sheetName": "资产负债表", "reportType": "资产负债表", "originalCurrentAmount": "", "originalPreviousAmount": "", "matchedMetrics": [{"name": "其他权益工具:永续债", "code": "其他权益工具:永续债", "description": "其他权益工具:永续债", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "perpetual_debt"}], "windcode": "perpetual_debt", "windcodes": ["perpetual_debt"], "windcodeDetails": [{"windcode": "perpetual_debt", "metricName": "其他权益工具:永续债", "metricCode": "其他权益工具:永续债", "description": "其他权益工具:永续债", "unit": "", "reportType": "资产负债表"}], "matchCount": 1, "hasWindCode": true}, {"key": "80", "item": "租赁负债", "currentAmount": "380,970,912.55", "previousAmount": "345,391,303.09", "level": 0, "rowType": "normal", "sheetName": "资产负债表", "reportType": "资产负债表", "originalCurrentAmount": "380970912.55", "originalPreviousAmount": "345391303.09", "matchedMetrics": [{"name": "租赁负债", "code": "租赁负债", "description": "租赁负债", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "lease_obligation"}], "windcode": "lease_obligation", "windcodes": ["lease_obligation"], "windcodeDetails": [{"windcode": "lease_obligation", "metricName": "租赁负债", "metricCode": "租赁负债", "description": "租赁负债", "unit": "", "reportType": "资产负债表"}], "matchCount": 1, "hasWindCode": true}, {"key": "81", "item": "长期应付款", "currentAmount": "", "previousAmount": "", "level": 0, "rowType": "normal", "sheetName": "资产负债表", "reportType": "资产负债表", "originalCurrentAmount": "", "originalPreviousAmount": "", "matchedMetrics": [{"name": "长期应付款(合计)", "code": "长期应付款(合计)", "description": "长期应付款(合计)", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "lt_payable_tot"}, {"name": "长期应付款", "code": "长期应付款", "description": "长期应付款", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "lt_payable"}], "windcode": "lt_payable_tot", "windcodes": ["lt_payable_tot", "lt_payable"], "windcodeDetails": [{"windcode": "lt_payable_tot", "metricName": "长期应付款(合计)", "metricCode": "长期应付款(合计)", "description": "长期应付款(合计)", "unit": "", "reportType": "资产负债表"}, {"windcode": "lt_payable", "metricName": "长期应付款", "metricCode": "长期应付款", "description": "长期应付款", "unit": "", "reportType": "资产负债表"}], "matchCount": 2, "hasWindCode": true}, {"key": "82", "item": "长期应付职工薪酬", "currentAmount": "", "previousAmount": "", "level": 0, "rowType": "normal", "sheetName": "资产负债表", "reportType": "资产负债表", "originalCurrentAmount": "", "originalPreviousAmount": "", "matchedMetrics": [{"name": "应付职工薪酬", "code": "应付职工薪酬", "description": "应付职工薪酬", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "empl_ben_payable"}, {"name": "长期应付职工薪酬", "code": "长期应付职工薪酬", "description": "长期应付职工薪酬", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "lt_empl_ben_payable"}], "windcode": "empl_ben_payable", "windcodes": ["empl_ben_payable", "lt_empl_ben_payable"], "windcodeDetails": [{"windcode": "empl_ben_payable", "metricName": "应付职工薪酬", "metricCode": "应付职工薪酬", "description": "应付职工薪酬", "unit": "", "reportType": "资产负债表"}, {"windcode": "lt_empl_ben_payable", "metricName": "长期应付职工薪酬", "metricCode": "长期应付职工薪酬", "description": "长期应付职工薪酬", "unit": "", "reportType": "资产负债表"}], "matchCount": 2, "hasWindCode": true}, {"key": "83", "item": "预计负债", "currentAmount": "15,630,925.34", "previousAmount": "17,083,091.42", "level": 0, "rowType": "normal", "sheetName": "资产负债表", "reportType": "资产负债表", "originalCurrentAmount": "15630925.34", "originalPreviousAmount": "17083091.42", "matchedMetrics": [{"name": "预计负债", "code": "预计负债", "description": "预计负债", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "provisions"}], "windcode": "provisions", "windcodes": ["provisions"], "windcodeDetails": [{"windcode": "provisions", "metricName": "预计负债", "metricCode": "预计负债", "description": "预计负债", "unit": "", "reportType": "资产负债表"}], "matchCount": 1, "hasWindCode": true}, {"key": "84", "item": "递延收益", "currentAmount": "8,603,602.73", "previousAmount": "4,053,530.52", "level": 0, "rowType": "normal", "sheetName": "资产负债表", "reportType": "资产负债表", "originalCurrentAmount": "8603602.73", "originalPreviousAmount": "4053530.52", "matchedMetrics": [{"name": "递延收益-非流动负债", "code": "递延收益-非流动负债", "description": "递延收益", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "deferred_inc_non_cur_liab"}, {"name": "递延收益-流动负债", "code": "递延收益-流动负债", "description": "递延收益-流动负债", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "deferred_inc_cur_liab"}], "windcode": "deferred_inc_non_cur_liab", "windcodes": ["deferred_inc_non_cur_liab", "deferred_inc_cur_liab"], "windcodeDetails": [{"windcode": "deferred_inc_non_cur_liab", "metricName": "递延收益-非流动负债", "metricCode": "递延收益-非流动负债", "description": "递延收益", "unit": "", "reportType": "资产负债表"}, {"windcode": "deferred_inc_cur_liab", "metricName": "递延收益-流动负债", "metricCode": "递延收益-流动负债", "description": "递延收益-流动负债", "unit": "", "reportType": "资产负债表"}], "matchCount": 2, "hasWindCode": true}, {"key": "85", "item": "递延所得税负债", "currentAmount": "68,559,302.34", "previousAmount": "63,502,373.05", "level": 0, "rowType": "normal", "sheetName": "资产负债表", "reportType": "资产负债表", "originalCurrentAmount": "68559302.34", "originalPreviousAmount": "63502373.05", "matchedMetrics": [{"name": "递延所得税负债", "code": "递延所得税负债", "description": "递延所得税负债", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "deferred_tax_liab"}, {"name": "所得税", "code": "所得税", "description": "所得税", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "tax"}, {"name": "递延所得税负债增加", "code": "递延所得税负债增加", "description": "其中:递延所得税负债增加", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "incr_deferred_inc_tax_liab"}, {"name": "递延所得税\r\n", "code": "递延所得税\r\n", "description": "递延所得税\r\n", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "stmnote_incometax_5"}], "windcode": "deferred_tax_liab", "windcodes": ["deferred_tax_liab", "tax", "incr_deferred_inc_tax_liab", "stmnote_incometax_5"], "windcodeDetails": [{"windcode": "deferred_tax_liab", "metricName": "递延所得税负债", "metricCode": "递延所得税负债", "description": "递延所得税负债", "unit": "", "reportType": "资产负债表"}, {"windcode": "tax", "metricName": "所得税", "metricCode": "所得税", "description": "所得税", "unit": "", "reportType": "资产负债表"}, {"windcode": "incr_deferred_inc_tax_liab", "metricName": "递延所得税负债增加", "metricCode": "递延所得税负债增加", "description": "其中:递延所得税负债增加", "unit": "", "reportType": "资产负债表"}, {"windcode": "stmnote_incometax_5", "metricName": "递延所得税", "metricCode": "递延所得税\r\n", "description": "递延所得税\r\n", "unit": "", "reportType": "资产负债表"}], "matchCount": 4, "hasWindCode": true}, {"key": "86", "item": "其他非流动负债", "currentAmount": "", "previousAmount": "", "level": 0, "rowType": "normal", "sheetName": "资产负债表", "reportType": "资产负债表", "originalCurrentAmount": "", "originalPreviousAmount": "", "matchedMetrics": [{"name": "其他", "code": "其他", "description": "其他", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "others"}, {"name": "其他非流动负债", "code": "其他非流动负债", "description": "其他非流动负债", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "oth_non_cur_liab"}], "windcode": "others", "windcodes": ["others", "oth_non_cur_liab"], "windcodeDetails": [{"windcode": "others", "metricName": "其他", "metricCode": "其他", "description": "其他", "unit": "", "reportType": "资产负债表"}, {"windcode": "oth_non_cur_liab", "metricName": "其他非流动负债", "metricCode": "其他非流动负债", "description": "其他非流动负债", "unit": "", "reportType": "资产负债表"}], "matchCount": 2, "hasWindCode": true}, {"key": "87", "item": "非流动负债合计", "currentAmount": "473,764,742.96", "previousAmount": "430,030,298.08", "level": 0, "rowType": "normal", "sheetName": "资产负债表", "reportType": "资产负债表", "originalCurrentAmount": "473764742.96", "originalPreviousAmount": "430030298.08", "matchedMetrics": [{"name": "负债合计", "code": "负债合计", "description": "负债合计", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "tot_liab"}, {"name": "非流动负债合计", "code": "非流动负债合计", "description": "非流动负债合计", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "tot_non_cur_liab"}, {"name": "流动负债合计", "code": "流动负债合计", "description": "流动负债合计", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "tot_cur_liab"}], "windcode": "tot_liab", "windcodes": ["tot_liab", "tot_non_cur_liab", "tot_cur_liab"], "windcodeDetails": [{"windcode": "tot_liab", "metricName": "负债合计", "metricCode": "负债合计", "description": "负债合计", "unit": "", "reportType": "资产负债表"}, {"windcode": "tot_non_cur_liab", "metricName": "非流动负债合计", "metricCode": "非流动负债合计", "description": "非流动负债合计", "unit": "", "reportType": "资产负债表"}, {"windcode": "tot_cur_liab", "metricName": "流动负债合计", "metricCode": "流动负债合计", "description": "流动负债合计", "unit": "", "reportType": "资产负债表"}], "matchCount": 3, "hasWindCode": true}, {"key": "88", "item": "负债合计", "currentAmount": "6,989,602,409.51", "previousAmount": "6,683,952,045.75", "level": 0, "rowType": "normal", "sheetName": "资产负债表", "reportType": "资产负债表", "originalCurrentAmount": "6989602409.51", "originalPreviousAmount": "6683952045.75", "matchedMetrics": [{"name": "负债合计", "code": "负债合计", "description": "负债合计", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "tot_liab"}, {"name": "非流动负债合计", "code": "非流动负债合计", "description": "非流动负债合计", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "tot_non_cur_liab"}, {"name": "流动负债合计", "code": "流动负债合计", "description": "流动负债合计", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "tot_cur_liab"}], "windcode": "tot_liab", "windcodes": ["tot_liab", "tot_non_cur_liab", "tot_cur_liab"], "windcodeDetails": [{"windcode": "tot_liab", "metricName": "负债合计", "metricCode": "负债合计", "description": "负债合计", "unit": "", "reportType": "资产负债表"}, {"windcode": "tot_non_cur_liab", "metricName": "非流动负债合计", "metricCode": "非流动负债合计", "description": "非流动负债合计", "unit": "", "reportType": "资产负债表"}, {"windcode": "tot_cur_liab", "metricName": "流动负债合计", "metricCode": "流动负债合计", "description": "流动负债合计", "unit": "", "reportType": "资产负债表"}], "matchCount": 3, "hasWindCode": true}, {"key": "89", "item": "所有者权益（或股东权益）：", "currentAmount": "", "previousAmount": "", "level": 0, "rowType": "normal", "sheetName": "资产负债表", "reportType": "资产负债表", "originalCurrentAmount": "", "originalPreviousAmount": "", "matchedMetrics": [], "windcode": null, "windcodes": [], "windcodeDetails": [], "matchCount": 0, "hasWindCode": false}, {"key": "90", "item": "实收资本（或股本）", "currentAmount": "685,620,517.00", "previousAmount": "689,142,343.00", "level": 0, "rowType": "normal", "sheetName": "资产负债表", "reportType": "资产负债表", "originalCurrentAmount": "685620517", "originalPreviousAmount": "689142343.0", "matchedMetrics": [], "windcode": null, "windcodes": [], "windcodeDetails": [], "matchCount": 0, "hasWindCode": false}, {"key": "91", "item": "其他权益工具", "currentAmount": "-6,125,564.00", "previousAmount": "-8,589,193.00", "level": 0, "rowType": "normal", "sheetName": "资产负债表", "reportType": "资产负债表", "originalCurrentAmount": "-6125564", "originalPreviousAmount": "-8589193.0", "matchedMetrics": [{"name": "其他权益工具投资", "code": "其他权益工具投资", "description": "其他权益工具投资", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "oth_eqy_instruments_invest"}, {"name": "其他", "code": "其他", "description": "其他", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "others"}, {"name": "其他权益工具", "code": "其他权益工具", "description": "其他权益工具", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "other_equity_instruments"}, {"name": "其他权益工具:优先股", "code": "其他权益工具:优先股", "description": "其他权益工具:优先股", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "other_equity_instruments_PRE"}, {"name": "其他权益工具:永续债", "code": "其他权益工具:永续债", "description": "其他权益工具:永续债", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "perpetual_debt"}], "windcode": "oth_eqy_instruments_invest", "windcodes": ["oth_eqy_instruments_invest", "others", "other_equity_instruments", "other_equity_instruments_PRE", "perpetual_debt"], "windcodeDetails": [{"windcode": "oth_eqy_instruments_invest", "metricName": "其他权益工具投资", "metricCode": "其他权益工具投资", "description": "其他权益工具投资", "unit": "", "reportType": "资产负债表"}, {"windcode": "others", "metricName": "其他", "metricCode": "其他", "description": "其他", "unit": "", "reportType": "资产负债表"}, {"windcode": "other_equity_instruments", "metricName": "其他权益工具", "metricCode": "其他权益工具", "description": "其他权益工具", "unit": "", "reportType": "资产负债表"}, {"windcode": "other_equity_instruments_PRE", "metricName": "其他权益工具:优先股", "metricCode": "其他权益工具:优先股", "description": "其他权益工具:优先股", "unit": "", "reportType": "资产负债表"}, {"windcode": "perpetual_debt", "metricName": "其他权益工具:永续债", "metricCode": "其他权益工具:永续债", "description": "其他权益工具:永续债", "unit": "", "reportType": "资产负债表"}], "matchCount": 5, "hasWindCode": true}, {"key": "92", "item": "其中：优先股", "currentAmount": "", "previousAmount": "", "level": 0, "rowType": "normal", "sheetName": "资产负债表", "reportType": "资产负债表", "originalCurrentAmount": "", "originalPreviousAmount": "", "matchedMetrics": [], "windcode": null, "windcodes": [], "windcodeDetails": [], "matchCount": 0, "hasWindCode": false}, {"key": "93", "item": "永续债", "currentAmount": "", "previousAmount": "", "level": 0, "rowType": "normal", "sheetName": "资产负债表", "reportType": "资产负债表", "originalCurrentAmount": "", "originalPreviousAmount": "", "matchedMetrics": [{"name": "其他权益工具:永续债", "code": "其他权益工具:永续债", "description": "其他权益工具:永续债", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "perpetual_debt"}], "windcode": "perpetual_debt", "windcodes": ["perpetual_debt"], "windcodeDetails": [{"windcode": "perpetual_debt", "metricName": "其他权益工具:永续债", "metricCode": "其他权益工具:永续债", "description": "其他权益工具:永续债", "unit": "", "reportType": "资产负债表"}], "matchCount": 1, "hasWindCode": true}, {"key": "94", "item": "资本公积", "currentAmount": "452,478,698.17", "previousAmount": "366,062,702.57", "level": 0, "rowType": "normal", "sheetName": "资产负债表", "reportType": "资产负债表", "originalCurrentAmount": "452478698.17", "originalPreviousAmount": "366062702.57", "matchedMetrics": [{"name": "资本公积金", "code": "资本公积金", "description": "资本公积", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "cap_rsrv"}], "windcode": "cap_rsrv", "windcodes": ["cap_rsrv"], "windcodeDetails": [{"windcode": "cap_rsrv", "metricName": "资本公积金", "metricCode": "资本公积金", "description": "资本公积", "unit": "", "reportType": "资产负债表"}], "matchCount": 1, "hasWindCode": true}, {"key": "95", "item": "减：库存股", "currentAmount": "", "previousAmount": "", "level": 0, "rowType": "normal", "sheetName": "资产负债表", "reportType": "资产负债表", "originalCurrentAmount": "", "originalPreviousAmount": "", "matchedMetrics": [{"name": "库存股", "code": "库存股", "description": "库存股", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "tsy_stk"}], "windcode": "tsy_stk", "windcodes": ["tsy_stk"], "windcodeDetails": [{"windcode": "tsy_stk", "metricName": "库存股", "metricCode": "库存股", "description": "库存股", "unit": "", "reportType": "资产负债表"}], "matchCount": 1, "hasWindCode": true}, {"key": "96", "item": "其他综合收益", "currentAmount": "-26,073,900.74", "previousAmount": "-25,904,105.98", "level": 0, "rowType": "normal", "sheetName": "资产负债表", "reportType": "资产负债表", "originalCurrentAmount": "-26073900.74", "originalPreviousAmount": "-25904105.98", "matchedMetrics": [{"name": "其他", "code": "其他", "description": "其他", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "others"}, {"name": "其他综合收益_BS", "code": "其他综合收益_BS", "description": "其他综合收益", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "other_compreh_inc_bs"}, {"name": "其他综合收益", "code": "其他综合收益", "description": "归属于母公司股东的其他综合收益", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "other_compreh_inc"}, {"name": "以公允价值计量且其变动计入其他综合收益的金融资产", "code": "以公允价值计量且其变动计入其他综合收益的金融资产", "description": "以公允价值计量且其变动计入其他综合收益的金融资产", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "fin_assets_chg_compreh_inc"}], "windcode": "others", "windcodes": ["others", "other_compreh_inc_bs", "other_compreh_inc", "fin_assets_chg_compreh_inc"], "windcodeDetails": [{"windcode": "others", "metricName": "其他", "metricCode": "其他", "description": "其他", "unit": "", "reportType": "资产负债表"}, {"windcode": "other_compreh_inc_bs", "metricName": "其他综合收益_BS", "metricCode": "其他综合收益_BS", "description": "其他综合收益", "unit": "", "reportType": "资产负债表"}, {"windcode": "other_compreh_inc", "metricName": "其他综合收益", "metricCode": "其他综合收益", "description": "归属于母公司股东的其他综合收益", "unit": "", "reportType": "资产负债表"}, {"windcode": "fin_assets_chg_compreh_inc", "metricName": "以公允价值计量且其变动计入其他综合收益的金融资产", "metricCode": "以公允价值计量且其变动计入其他综合收益的金融资产", "description": "以公允价值计量且其变动计入其他综合收益的金融资产", "unit": "", "reportType": "资产负债表"}], "matchCount": 4, "hasWindCode": true}, {"key": "97", "item": "专项储备", "currentAmount": "", "previousAmount": "", "level": 0, "rowType": "normal", "sheetName": "资产负债表", "reportType": "资产负债表", "originalCurrentAmount": "", "originalPreviousAmount": "", "matchedMetrics": [{"name": "专项储备", "code": "专项储备", "description": "专项储备", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "special_rsrv"}], "windcode": "special_rsrv", "windcodes": ["special_rsrv"], "windcodeDetails": [{"windcode": "special_rsrv", "metricName": "专项储备", "metricCode": "专项储备", "description": "专项储备", "unit": "", "reportType": "资产负债表"}], "matchCount": 1, "hasWindCode": true}, {"key": "98", "item": "盈余公积", "currentAmount": "213,117,422.25", "previousAmount": "183,604,183.56", "level": 0, "rowType": "normal", "sheetName": "资产负债表", "reportType": "资产负债表", "originalCurrentAmount": "213117422.25", "originalPreviousAmount": "183604183.56", "matchedMetrics": [{"name": "盈余公积金", "code": "盈余公积金", "description": "盈余公积", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "surplus_rsrv"}], "windcode": "surplus_rsrv", "windcodes": ["surplus_rsrv"], "windcodeDetails": [{"windcode": "surplus_rsrv", "metricName": "盈余公积金", "metricCode": "盈余公积金", "description": "盈余公积", "unit": "", "reportType": "资产负债表"}], "matchCount": 1, "hasWindCode": true}, {"key": "99", "item": "一般风险准备", "currentAmount": "", "previousAmount": "", "level": 0, "rowType": "normal", "sheetName": "资产负债表", "reportType": "资产负债表", "originalCurrentAmount": "", "originalPreviousAmount": "", "matchedMetrics": [{"name": "一般风险准备", "code": "一般风险准备", "description": "一般风险准备", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "prov_nom_risks"}], "windcode": "prov_nom_risks", "windcodes": ["prov_nom_risks"], "windcodeDetails": [{"windcode": "prov_nom_risks", "metricName": "一般风险准备", "metricCode": "一般风险准备", "description": "一般风险准备", "unit": "", "reportType": "资产负债表"}], "matchCount": 1, "hasWindCode": true}, {"key": "100", "item": "未分配利润", "currentAmount": "3,519,043,058.53", "previousAmount": "3,308,344,391.89", "level": 0, "rowType": "normal", "sheetName": "资产负债表", "reportType": "资产负债表", "originalCurrentAmount": "3519043058.53", "originalPreviousAmount": "3308344391.89", "matchedMetrics": [{"name": "未分配利润", "code": "未分配利润", "description": "未分配利润", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "undistributed_profit"}], "windcode": "undistributed_profit", "windcodes": ["undistributed_profit"], "windcodeDetails": [{"windcode": "undistributed_profit", "metricName": "未分配利润", "metricCode": "未分配利润", "description": "未分配利润", "unit": "", "reportType": "资产负债表"}], "matchCount": 1, "hasWindCode": true}, {"key": "101", "item": "归属于母公司所有者权益（或股东权益）合计", "currentAmount": "4,838,060,231.21", "previousAmount": "4,512,660,322.04", "level": 0, "rowType": "normal", "sheetName": "资产负债表", "reportType": "资产负债表", "originalCurrentAmount": "4838060231.21", "originalPreviousAmount": "4512660322.04", "matchedMetrics": [], "windcode": null, "windcodes": [], "windcodeDetails": [], "matchCount": 0, "hasWindCode": false}, {"key": "102", "item": "少数股东权益", "currentAmount": "110,607,057.67", "previousAmount": "228,046,776.47", "level": 0, "rowType": "normal", "sheetName": "资产负债表", "reportType": "资产负债表", "originalCurrentAmount": "110607057.67", "originalPreviousAmount": "228046776.47", "matchedMetrics": [{"name": "少数股东权益", "code": "少数股东权益", "description": "少数股东权益", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "minority_int"}], "windcode": "minority_int", "windcodes": ["minority_int"], "windcodeDetails": [{"windcode": "minority_int", "metricName": "少数股东权益", "metricCode": "少数股东权益", "description": "少数股东权益", "unit": "", "reportType": "资产负债表"}], "matchCount": 1, "hasWindCode": true}, {"key": "103", "item": "所有者权益（或股东权益）合计", "currentAmount": "4,948,667,288.88", "previousAmount": "4,740,707,098.51", "level": 0, "rowType": "normal", "sheetName": "资产负债表", "reportType": "资产负债表", "originalCurrentAmount": "4948667288.88", "originalPreviousAmount": "4740707098.51", "matchedMetrics": [], "windcode": null, "windcodes": [], "windcodeDetails": [], "matchCount": 0, "hasWindCode": false}, {"key": "104", "item": "负债和所有者权益（或股东权益）总计", "currentAmount": "11,938,269,698.39", "previousAmount": "11,424,659,144.26", "level": 0, "rowType": "normal", "sheetName": "资产负债表", "reportType": "资产负债表", "originalCurrentAmount": "11938269698.39", "originalPreviousAmount": "11424659144.26", "matchedMetrics": [], "windcode": null, "windcodes": [], "windcodeDetails": [], "matchCount": 0, "hasWindCode": false}, {"key": "1", "item": "一、经营活动产生的现金流量：", "currentAmount": "", "previousAmount": "", "level": 0, "rowType": "normal", "sheetName": "现金流量表", "reportType": "现金流量表", "originalCurrentAmount": "", "originalPreviousAmount": "", "matchedMetrics": [], "windcode": null, "windcodes": [], "windcodeDetails": [], "matchCount": 0, "hasWindCode": false}, {"key": "2", "item": "销售商品、提供劳务收到的现金", "currentAmount": "10,505,392,322.38", "previousAmount": "10,778,927,698.30", "level": 0, "rowType": "normal", "sheetName": "现金流量表", "reportType": "现金流量表", "originalCurrentAmount": "10505392322.38", "originalPreviousAmount": "10778927698.3", "matchedMetrics": [{"name": "销售商品、提供劳务收到的现金", "code": "销售商品、提供劳务收到的现金", "description": "销售商品、提供劳务收到的现金", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "cash_recp_sg_and_rs"}], "windcode": "cash_recp_sg_and_rs", "windcodes": ["cash_recp_sg_and_rs"], "windcodeDetails": [{"windcode": "cash_recp_sg_and_rs", "metricName": "销售商品、提供劳务收到的现金", "metricCode": "销售商品、提供劳务收到的现金", "description": "销售商品、提供劳务收到的现金", "unit": "", "reportType": "现金流量表"}], "matchCount": 1, "hasWindCode": true}, {"key": "3", "item": "客户存款和同业存放款项净增加额", "currentAmount": "", "previousAmount": "", "level": 0, "rowType": "normal", "sheetName": "现金流量表", "reportType": "现金流量表", "originalCurrentAmount": "", "originalPreviousAmount": "", "matchedMetrics": [{"name": "客户存款和同业存放款项净增加额", "code": "客户存款和同业存放款项净增加额", "description": "客户存款和同业存放款项净增加额", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "net_incr_dep_cob"}], "windcode": "net_incr_dep_cob", "windcodes": ["net_incr_dep_cob"], "windcodeDetails": [{"windcode": "net_incr_dep_cob", "metricName": "客户存款和同业存放款项净增加额", "metricCode": "客户存款和同业存放款项净增加额", "description": "客户存款和同业存放款项净增加额", "unit": "", "reportType": "现金流量表"}], "matchCount": 1, "hasWindCode": true}, {"key": "4", "item": "向中央银行借款净增加额", "currentAmount": "", "previousAmount": "", "level": 0, "rowType": "normal", "sheetName": "现金流量表", "reportType": "现金流量表", "originalCurrentAmount": "", "originalPreviousAmount": "", "matchedMetrics": [{"name": "向中央银行借款净增加额", "code": "向中央银行借款净增加额", "description": "向中央银行借款净增加额", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "net_incr_loans_central_bank"}, {"name": "向中央银行借款", "code": "向中央银行借款", "description": "向中央银行借款", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "borrow_central_bank"}], "windcode": "net_incr_loans_central_bank", "windcodes": ["net_incr_loans_central_bank", "borrow_central_bank"], "windcodeDetails": [{"windcode": "net_incr_loans_central_bank", "metricName": "向中央银行借款净增加额", "metricCode": "向中央银行借款净增加额", "description": "向中央银行借款净增加额", "unit": "", "reportType": "现金流量表"}, {"windcode": "borrow_central_bank", "metricName": "向中央银行借款", "metricCode": "向中央银行借款", "description": "向中央银行借款", "unit": "", "reportType": "现金流量表"}], "matchCount": 2, "hasWindCode": true}, {"key": "5", "item": "向其他金融机构拆入资金净增加额", "currentAmount": "", "previousAmount": "", "level": 0, "rowType": "normal", "sheetName": "现金流量表", "reportType": "现金流量表", "originalCurrentAmount": "", "originalPreviousAmount": "", "matchedMetrics": [{"name": "其他", "code": "其他", "description": "其他", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "others"}, {"name": "拆入资金净增加额", "code": "拆入资金净增加额", "description": "拆入资金净增加额", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "net_incr_loans_other_bank"}, {"name": "向其他金融机构拆入资金净增加额", "code": "向其他金融机构拆入资金净增加额", "description": "向其他金融机构拆入资金净增加额", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "net_incr_fund_borr_ofi"}, {"name": "拆入资金", "code": "拆入资金", "description": "拆入资金", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "loans_oth_banks"}], "windcode": "others", "windcodes": ["others", "net_incr_loans_other_bank", "net_incr_fund_borr_ofi", "loans_oth_banks"], "windcodeDetails": [{"windcode": "others", "metricName": "其他", "metricCode": "其他", "description": "其他", "unit": "", "reportType": "现金流量表"}, {"windcode": "net_incr_loans_other_bank", "metricName": "拆入资金净增加额", "metricCode": "拆入资金净增加额", "description": "拆入资金净增加额", "unit": "", "reportType": "现金流量表"}, {"windcode": "net_incr_fund_borr_ofi", "metricName": "向其他金融机构拆入资金净增加额", "metricCode": "向其他金融机构拆入资金净增加额", "description": "向其他金融机构拆入资金净增加额", "unit": "", "reportType": "现金流量表"}, {"windcode": "loans_oth_banks", "metricName": "拆入资金", "metricCode": "拆入资金", "description": "拆入资金", "unit": "", "reportType": "现金流量表"}], "matchCount": 4, "hasWindCode": true}, {"key": "6", "item": "收到原保险合同保费取得的现金", "currentAmount": "", "previousAmount": "", "level": 0, "rowType": "normal", "sheetName": "现金流量表", "reportType": "现金流量表", "originalCurrentAmount": "", "originalPreviousAmount": "", "matchedMetrics": [{"name": "收到原保险合同保费取得的现金", "code": "收到原保险合同保费取得的现金", "description": "收到原保险合同保费取得的现金", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "cash_recp_prem_orig_inco"}], "windcode": "cash_recp_prem_orig_inco", "windcodes": ["cash_recp_prem_orig_inco"], "windcodeDetails": [{"windcode": "cash_recp_prem_orig_inco", "metricName": "收到原保险合同保费取得的现金", "metricCode": "收到原保险合同保费取得的现金", "description": "收到原保险合同保费取得的现金", "unit": "", "reportType": "现金流量表"}], "matchCount": 1, "hasWindCode": true}, {"key": "7", "item": "收到再保业务现金净额", "currentAmount": "", "previousAmount": "", "level": 0, "rowType": "normal", "sheetName": "现金流量表", "reportType": "现金流量表", "originalCurrentAmount": "", "originalPreviousAmount": "", "matchedMetrics": [{"name": "收到再保业务现金净额", "code": "收到再保业务现金净额", "description": "收到再保业务现金净额", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "net_cash_received_reinsu_bus"}], "windcode": "net_cash_received_reinsu_bus", "windcodes": ["net_cash_received_reinsu_bus"], "windcodeDetails": [{"windcode": "net_cash_received_reinsu_bus", "metricName": "收到再保业务现金净额", "metricCode": "收到再保业务现金净额", "description": "收到再保业务现金净额", "unit": "", "reportType": "现金流量表"}], "matchCount": 1, "hasWindCode": true}, {"key": "8", "item": "保户储金及投资款净增加额", "currentAmount": "", "previousAmount": "", "level": 0, "rowType": "normal", "sheetName": "现金流量表", "reportType": "现金流量表", "originalCurrentAmount": "", "originalPreviousAmount": "", "matchedMetrics": [{"name": "保户储金及投资款", "code": "保户储金及投资款", "description": "保户储金及投资款", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "insured_deposit_invest"}], "windcode": "insured_deposit_invest", "windcodes": ["insured_deposit_invest"], "windcodeDetails": [{"windcode": "insured_deposit_invest", "metricName": "保户储金及投资款", "metricCode": "保户储金及投资款", "description": "保户储金及投资款", "unit": "", "reportType": "现金流量表"}], "matchCount": 1, "hasWindCode": true}, {"key": "9", "item": "收取利息、手续费及佣金的现金", "currentAmount": "", "previousAmount": "", "level": 0, "rowType": "normal", "sheetName": "现金流量表", "reportType": "现金流量表", "originalCurrentAmount": "", "originalPreviousAmount": "", "matchedMetrics": [], "windcode": null, "windcodes": [], "windcodeDetails": [], "matchCount": 0, "hasWindCode": false}, {"key": "10", "item": "拆入资金净增加额", "currentAmount": "", "previousAmount": "", "level": 0, "rowType": "normal", "sheetName": "现金流量表", "reportType": "现金流量表", "originalCurrentAmount": "", "originalPreviousAmount": "", "matchedMetrics": [{"name": "拆入资金净增加额", "code": "拆入资金净增加额", "description": "拆入资金净增加额", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "net_incr_loans_other_bank"}, {"name": "向其他金融机构拆入资金净增加额", "code": "向其他金融机构拆入资金净增加额", "description": "向其他金融机构拆入资金净增加额", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "net_incr_fund_borr_ofi"}, {"name": "拆入资金", "code": "拆入资金", "description": "拆入资金", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "loans_oth_banks"}], "windcode": "net_incr_loans_other_bank", "windcodes": ["net_incr_loans_other_bank", "net_incr_fund_borr_ofi", "loans_oth_banks"], "windcodeDetails": [{"windcode": "net_incr_loans_other_bank", "metricName": "拆入资金净增加额", "metricCode": "拆入资金净增加额", "description": "拆入资金净增加额", "unit": "", "reportType": "现金流量表"}, {"windcode": "net_incr_fund_borr_ofi", "metricName": "向其他金融机构拆入资金净增加额", "metricCode": "向其他金融机构拆入资金净增加额", "description": "向其他金融机构拆入资金净增加额", "unit": "", "reportType": "现金流量表"}, {"windcode": "loans_oth_banks", "metricName": "拆入资金", "metricCode": "拆入资金", "description": "拆入资金", "unit": "", "reportType": "现金流量表"}], "matchCount": 3, "hasWindCode": true}, {"key": "11", "item": "回购业务资金净增加额", "currentAmount": "", "previousAmount": "", "level": 0, "rowType": "normal", "sheetName": "现金流量表", "reportType": "现金流量表", "originalCurrentAmount": "", "originalPreviousAmount": "", "matchedMetrics": [{"name": "回购业务资金净增加额", "code": "回购业务资金净增加额", "description": "回购业务资金净增加额", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "net_incr_repurch_bus_fund"}], "windcode": "net_incr_repurch_bus_fund", "windcodes": ["net_incr_repurch_bus_fund"], "windcodeDetails": [{"windcode": "net_incr_repurch_bus_fund", "metricName": "回购业务资金净增加额", "metricCode": "回购业务资金净增加额", "description": "回购业务资金净增加额", "unit": "", "reportType": "现金流量表"}], "matchCount": 1, "hasWindCode": true}, {"key": "12", "item": "代理买卖证券收到的现金净额", "currentAmount": "", "previousAmount": "", "level": 0, "rowType": "normal", "sheetName": "现金流量表", "reportType": "现金流量表", "originalCurrentAmount": "", "originalPreviousAmount": "", "matchedMetrics": [{"name": "代理买卖证券收到的现金净额", "code": "代理买卖证券收到的现金净额", "description": "代理买卖证券收到的现金净额", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "net_cash_from_seurities"}], "windcode": "net_cash_from_seurities", "windcodes": ["net_cash_from_seurities"], "windcodeDetails": [{"windcode": "net_cash_from_seurities", "metricName": "代理买卖证券收到的现金净额", "metricCode": "代理买卖证券收到的现金净额", "description": "代理买卖证券收到的现金净额", "unit": "", "reportType": "现金流量表"}], "matchCount": 1, "hasWindCode": true}, {"key": "13", "item": "收到的税费返还", "currentAmount": "6,754,332.69", "previousAmount": "18,812,099.52", "level": 0, "rowType": "normal", "sheetName": "现金流量表", "reportType": "现金流量表", "originalCurrentAmount": "6754332.69", "originalPreviousAmount": "18812099.52", "matchedMetrics": [{"name": "收到的税费返还", "code": "收到的税费返还", "description": "收到的税费返还", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "recp_tax_rends"}], "windcode": "recp_tax_rends", "windcodes": ["recp_tax_rends"], "windcodeDetails": [{"windcode": "recp_tax_rends", "metricName": "收到的税费返还", "metricCode": "收到的税费返还", "description": "收到的税费返还", "unit": "", "reportType": "现金流量表"}], "matchCount": 1, "hasWindCode": true}, {"key": "14", "item": "收到其他与经营活动有关的现金", "currentAmount": "108,253,802.37", "previousAmount": "101,123,970.33", "level": 0, "rowType": "normal", "sheetName": "现金流量表", "reportType": "现金流量表", "originalCurrentAmount": "*********.37", "originalPreviousAmount": "*********.33", "matchedMetrics": [{"name": "其他", "code": "其他", "description": "其他", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "others"}, {"name": "收到其他与经营活动有关的现金", "code": "收到其他与经营活动有关的现金", "description": "收到其他与经营活动有关的现金", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "other_cash_recp_ral_oper_act"}], "windcode": "others", "windcodes": ["others", "other_cash_recp_ral_oper_act"], "windcodeDetails": [{"windcode": "others", "metricName": "其他", "metricCode": "其他", "description": "其他", "unit": "", "reportType": "现金流量表"}, {"windcode": "other_cash_recp_ral_oper_act", "metricName": "收到其他与经营活动有关的现金", "metricCode": "收到其他与经营活动有关的现金", "description": "收到其他与经营活动有关的现金", "unit": "", "reportType": "现金流量表"}], "matchCount": 2, "hasWindCode": true}, {"key": "15", "item": "经营活动现金流入小计", "currentAmount": "10,620,400,457.44", "previousAmount": "10,898,863,768.15", "level": 0, "rowType": "normal", "sheetName": "现金流量表", "reportType": "现金流量表", "originalCurrentAmount": "10620400457.44", "originalPreviousAmount": "10898863768.15", "matchedMetrics": [{"name": "经营活动现金流入小计", "code": "经营活动现金流入小计", "description": "经营活动现金流入小计", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "stot_cash_inflows_oper_act"}], "windcode": "stot_cash_inflows_oper_act", "windcodes": ["stot_cash_inflows_oper_act"], "windcodeDetails": [{"windcode": "stot_cash_inflows_oper_act", "metricName": "经营活动现金流入小计", "metricCode": "经营活动现金流入小计", "description": "经营活动现金流入小计", "unit": "", "reportType": "现金流量表"}], "matchCount": 1, "hasWindCode": true}, {"key": "16", "item": "购买商品、接受劳务支付的现金", "currentAmount": "9,065,323,605.61", "previousAmount": "8,930,451,412.46", "level": 0, "rowType": "normal", "sheetName": "现金流量表", "reportType": "现金流量表", "originalCurrentAmount": "9065323605.61", "originalPreviousAmount": "8930451412.46", "matchedMetrics": [{"name": "购买商品、接受劳务支付的现金", "code": "购买商品、接受劳务支付的现金", "description": "购买商品、接受劳务支付的现金", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "cash_pay_goods_purch_serv_rec"}], "windcode": "cash_pay_goods_purch_serv_rec", "windcodes": ["cash_pay_goods_purch_serv_rec"], "windcodeDetails": [{"windcode": "cash_pay_goods_purch_serv_rec", "metricName": "购买商品、接受劳务支付的现金", "metricCode": "购买商品、接受劳务支付的现金", "description": "购买商品、接受劳务支付的现金", "unit": "", "reportType": "现金流量表"}], "matchCount": 1, "hasWindCode": true}, {"key": "17", "item": "客户贷款及垫款净增加额", "currentAmount": "", "previousAmount": "", "level": 0, "rowType": "normal", "sheetName": "现金流量表", "reportType": "现金流量表", "originalCurrentAmount": "", "originalPreviousAmount": "", "matchedMetrics": [{"name": "客户贷款及垫款净增加额", "code": "客户贷款及垫款净增加额", "description": "客户贷款及垫款净增加额", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "net_incr_clients_loan_adv"}], "windcode": "net_incr_clients_loan_adv", "windcodes": ["net_incr_clients_loan_adv"], "windcodeDetails": [{"windcode": "net_incr_clients_loan_adv", "metricName": "客户贷款及垫款净增加额", "metricCode": "客户贷款及垫款净增加额", "description": "客户贷款及垫款净增加额", "unit": "", "reportType": "现金流量表"}], "matchCount": 1, "hasWindCode": true}, {"key": "18", "item": "存放中央银行和同业款项净增加额", "currentAmount": "", "previousAmount": "", "level": 0, "rowType": "normal", "sheetName": "现金流量表", "reportType": "现金流量表", "originalCurrentAmount": "", "originalPreviousAmount": "", "matchedMetrics": [], "windcode": null, "windcodes": [], "windcodeDetails": [], "matchCount": 0, "hasWindCode": false}, {"key": "19", "item": "支付原保险合同赔付款项的现金", "currentAmount": "", "previousAmount": "", "level": 0, "rowType": "normal", "sheetName": "现金流量表", "reportType": "现金流量表", "originalCurrentAmount": "", "originalPreviousAmount": "", "matchedMetrics": [{"name": "支付原保险合同赔付款项的现金", "code": "支付原保险合同赔付款项的现金", "description": "支付原保险合同赔付款项的现金", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "cash_pay_claims_orig_inco"}], "windcode": "cash_pay_claims_orig_inco", "windcodes": ["cash_pay_claims_orig_inco"], "windcodeDetails": [{"windcode": "cash_pay_claims_orig_inco", "metricName": "支付原保险合同赔付款项的现金", "metricCode": "支付原保险合同赔付款项的现金", "description": "支付原保险合同赔付款项的现金", "unit": "", "reportType": "现金流量表"}], "matchCount": 1, "hasWindCode": true}, {"key": "20", "item": "拆出资金净增加额", "currentAmount": "", "previousAmount": "", "level": 0, "rowType": "normal", "sheetName": "现金流量表", "reportType": "现金流量表", "originalCurrentAmount": "", "originalPreviousAmount": "", "matchedMetrics": [{"name": "拆出资金", "code": "拆出资金", "description": "拆出资金", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "loans_to_oth_banks"}], "windcode": "loans_to_oth_banks", "windcodes": ["loans_to_oth_banks"], "windcodeDetails": [{"windcode": "loans_to_oth_banks", "metricName": "拆出资金", "metricCode": "拆出资金", "description": "拆出资金", "unit": "", "reportType": "现金流量表"}], "matchCount": 1, "hasWindCode": true}, {"key": "21", "item": "支付利息、手续费及佣金的现金", "currentAmount": "", "previousAmount": "", "level": 0, "rowType": "normal", "sheetName": "现金流量表", "reportType": "现金流量表", "originalCurrentAmount": "", "originalPreviousAmount": "", "matchedMetrics": [], "windcode": null, "windcodes": [], "windcodeDetails": [], "matchCount": 0, "hasWindCode": false}, {"key": "22", "item": "支付保单红利的现金", "currentAmount": "", "previousAmount": "", "level": 0, "rowType": "normal", "sheetName": "现金流量表", "reportType": "现金流量表", "originalCurrentAmount": "", "originalPreviousAmount": "", "matchedMetrics": [{"name": "支付保单红利的现金", "code": "支付保单红利的现金", "description": "支付保单红利的现金", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "comm_insur_plcy_paid"}], "windcode": "comm_insur_plcy_paid", "windcodes": ["comm_insur_plcy_paid"], "windcodeDetails": [{"windcode": "comm_insur_plcy_paid", "metricName": "支付保单红利的现金", "metricCode": "支付保单红利的现金", "description": "支付保单红利的现金", "unit": "", "reportType": "现金流量表"}], "matchCount": 1, "hasWindCode": true}, {"key": "23", "item": "支付给职工及为职工支付的现金", "currentAmount": "1,197,256,511.08", "previousAmount": "1,153,689,763.09", "level": 0, "rowType": "normal", "sheetName": "现金流量表", "reportType": "现金流量表", "originalCurrentAmount": "1197256511.08", "originalPreviousAmount": "1153689763.09", "matchedMetrics": [], "windcode": null, "windcodes": [], "windcodeDetails": [], "matchCount": 0, "hasWindCode": false}, {"key": "24", "item": "支付的各项税费", "currentAmount": "303,834,937.16", "previousAmount": "355,132,280.08", "level": 0, "rowType": "normal", "sheetName": "现金流量表", "reportType": "现金流量表", "originalCurrentAmount": "*********.16", "originalPreviousAmount": "*********.08", "matchedMetrics": [{"name": "支付的各项税费", "code": "支付的各项税费", "description": "支付的各项税费", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "pay_all_typ_tax"}], "windcode": "pay_all_typ_tax", "windcodes": ["pay_all_typ_tax"], "windcodeDetails": [{"windcode": "pay_all_typ_tax", "metricName": "支付的各项税费", "metricCode": "支付的各项税费", "description": "支付的各项税费", "unit": "", "reportType": "现金流量表"}], "matchCount": 1, "hasWindCode": true}, {"key": "25", "item": "支付其他与经营活动有关的现金", "currentAmount": "300,978,770.32", "previousAmount": "315,658,718.10", "level": 0, "rowType": "normal", "sheetName": "现金流量表", "reportType": "现金流量表", "originalCurrentAmount": "*********.32", "originalPreviousAmount": "*********.1", "matchedMetrics": [{"name": "其他", "code": "其他", "description": "其他", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "others"}, {"name": "支付其他与经营活动有关的现金", "code": "支付其他与经营活动有关的现金", "description": "支付其他与经营活动有关的现金", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "other_cash_pay_ral_oper_act"}], "windcode": "others", "windcodes": ["others", "other_cash_pay_ral_oper_act"], "windcodeDetails": [{"windcode": "others", "metricName": "其他", "metricCode": "其他", "description": "其他", "unit": "", "reportType": "现金流量表"}, {"windcode": "other_cash_pay_ral_oper_act", "metricName": "支付其他与经营活动有关的现金", "metricCode": "支付其他与经营活动有关的现金", "description": "支付其他与经营活动有关的现金", "unit": "", "reportType": "现金流量表"}], "matchCount": 2, "hasWindCode": true}, {"key": "26", "item": "经营活动现金流出小计", "currentAmount": "10,867,393,824.17", "previousAmount": "10,754,932,173.73", "level": 0, "rowType": "normal", "sheetName": "现金流量表", "reportType": "现金流量表", "originalCurrentAmount": "10867393824.17", "originalPreviousAmount": "10754932173.73", "matchedMetrics": [{"name": "经营活动现金流出小计", "code": "经营活动现金流出小计", "description": "经营活动现金流出小计", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "stot_cash_outflows_oper_act"}], "windcode": "stot_cash_outflows_oper_act", "windcodes": ["stot_cash_outflows_oper_act"], "windcodeDetails": [{"windcode": "stot_cash_outflows_oper_act", "metricName": "经营活动现金流出小计", "metricCode": "经营活动现金流出小计", "description": "经营活动现金流出小计", "unit": "", "reportType": "现金流量表"}], "matchCount": 1, "hasWindCode": true}, {"key": "27", "item": "经营活动产生的现金流量净额", "currentAmount": "-246,993,366.73", "previousAmount": "143,931,594.42", "level": 0, "rowType": "normal", "sheetName": "现金流量表", "reportType": "现金流量表", "originalCurrentAmount": "-246993366.73", "originalPreviousAmount": "143931594.42", "matchedMetrics": [{"name": "经营活动产生的现金流量净额", "code": "经营活动产生的现金流量净额", "description": "经营活动产生的现金流量净额", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "net_cash_flows_oper_act"}, {"name": "间接法-经营活动产生的现金流量净额", "code": "间接法-经营活动产生的现金流量净额", "description": "间接法—经营活动产生的现金流量净额", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "im_net_cash_flows_oper_act"}, {"name": "经营活动产生的现金流量净额差额(合计平衡项目)", "code": "经营活动产生的现金流量净额差额(合计平衡项目)", "description": "经营活动产生的现金流量净额差额(合计平衡项目)", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "cf_oper_act_netting"}], "windcode": "net_cash_flows_oper_act", "windcodes": ["net_cash_flows_oper_act", "im_net_cash_flows_oper_act", "cf_oper_act_netting"], "windcodeDetails": [{"windcode": "net_cash_flows_oper_act", "metricName": "经营活动产生的现金流量净额", "metricCode": "经营活动产生的现金流量净额", "description": "经营活动产生的现金流量净额", "unit": "", "reportType": "现金流量表"}, {"windcode": "im_net_cash_flows_oper_act", "metricName": "间接法-经营活动产生的现金流量净额", "metricCode": "间接法-经营活动产生的现金流量净额", "description": "间接法—经营活动产生的现金流量净额", "unit": "", "reportType": "现金流量表"}, {"windcode": "cf_oper_act_netting", "metricName": "经营活动产生的现金流量净额差额(合计平衡项目)", "metricCode": "经营活动产生的现金流量净额差额(合计平衡项目)", "description": "经营活动产生的现金流量净额差额(合计平衡项目)", "unit": "", "reportType": "现金流量表"}], "matchCount": 3, "hasWindCode": true}, {"key": "28", "item": "二、投资活动产生的现金流量：", "currentAmount": "", "previousAmount": "", "level": 0, "rowType": "normal", "sheetName": "现金流量表", "reportType": "现金流量表", "originalCurrentAmount": "", "originalPreviousAmount": "", "matchedMetrics": [], "windcode": null, "windcodes": [], "windcodeDetails": [], "matchCount": 0, "hasWindCode": false}, {"key": "29", "item": "收回投资收到的现金", "currentAmount": "", "previousAmount": "", "level": 0, "rowType": "normal", "sheetName": "现金流量表", "reportType": "现金流量表", "originalCurrentAmount": "", "originalPreviousAmount": "", "matchedMetrics": [{"name": "收回投资收到的现金", "code": "收回投资收到的现金", "description": "收回投资收到的现金", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "cash_recp_disp_withdrwl_invest"}], "windcode": "cash_recp_disp_withdrwl_invest", "windcodes": ["cash_recp_disp_withdrwl_invest"], "windcodeDetails": [{"windcode": "cash_recp_disp_withdrwl_invest", "metricName": "收回投资收到的现金", "metricCode": "收回投资收到的现金", "description": "收回投资收到的现金", "unit": "", "reportType": "现金流量表"}], "matchCount": 1, "hasWindCode": true}, {"key": "30", "item": "取得投资收益收到的现金", "currentAmount": "", "previousAmount": "", "level": 0, "rowType": "normal", "sheetName": "现金流量表", "reportType": "现金流量表", "originalCurrentAmount": "", "originalPreviousAmount": "", "matchedMetrics": [{"name": "取得投资收益收到的现金", "code": "取得投资收益收到的现金", "description": "取得投资收益收到的现金", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "cash_recp_return_invest"}], "windcode": "cash_recp_return_invest", "windcodes": ["cash_recp_return_invest"], "windcodeDetails": [{"windcode": "cash_recp_return_invest", "metricName": "取得投资收益收到的现金", "metricCode": "取得投资收益收到的现金", "description": "取得投资收益收到的现金", "unit": "", "reportType": "现金流量表"}], "matchCount": 1, "hasWindCode": true}, {"key": "31", "item": "处置固定资产、无形资产和其他长期资产收回的现金 净额", "currentAmount": "402,363.18", "previousAmount": "69,299.43", "level": 0, "rowType": "normal", "sheetName": "现金流量表", "reportType": "现金流量表", "originalCurrentAmount": "402363.18", "originalPreviousAmount": "69299.43", "matchedMetrics": [{"name": "处置固定资产、无形资产和其他长期资产收回的现金净额", "code": "处置固定资产、无形资产和其他长期资产收回的现金净额", "description": "处置固定资产、无形资产和其他长期资产收回的现金净额", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "net_cash_recp_disp_fiolta"}, {"name": "其他", "code": "其他", "description": "其他", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "others"}, {"name": "固定资产", "code": "固定资产", "description": "固定资产", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "fix_assets"}, {"name": "无形资产", "code": "无形资产", "description": "无形资产", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "intang_assets"}], "windcode": "net_cash_recp_disp_fiolta", "windcodes": ["net_cash_recp_disp_fiolta", "others", "fix_assets", "intang_assets"], "windcodeDetails": [{"windcode": "net_cash_recp_disp_fiolta", "metricName": "处置固定资产、无形资产和其他长期资产收回的现金净额", "metricCode": "处置固定资产、无形资产和其他长期资产收回的现金净额", "description": "处置固定资产、无形资产和其他长期资产收回的现金净额", "unit": "", "reportType": "现金流量表"}, {"windcode": "others", "metricName": "其他", "metricCode": "其他", "description": "其他", "unit": "", "reportType": "现金流量表"}, {"windcode": "fix_assets", "metricName": "固定资产", "metricCode": "固定资产", "description": "固定资产", "unit": "", "reportType": "现金流量表"}, {"windcode": "intang_assets", "metricName": "无形资产", "metricCode": "无形资产", "description": "无形资产", "unit": "", "reportType": "现金流量表"}], "matchCount": 4, "hasWindCode": true}, {"key": "32", "item": "处置子公司及其他营业单位收到的现金净额", "currentAmount": "", "previousAmount": "", "level": 0, "rowType": "normal", "sheetName": "现金流量表", "reportType": "现金流量表", "originalCurrentAmount": "", "originalPreviousAmount": "", "matchedMetrics": [{"name": "其他", "code": "其他", "description": "其他", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "others"}, {"name": "处置子公司及其他营业单位收到的现金净额", "code": "处置子公司及其他营业单位收到的现金净额", "description": "处置子公司及其他营业单位收到的现金净额", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "net_cash_recp_disp_sobu"}], "windcode": "others", "windcodes": ["others", "net_cash_recp_disp_sobu"], "windcodeDetails": [{"windcode": "others", "metricName": "其他", "metricCode": "其他", "description": "其他", "unit": "", "reportType": "现金流量表"}, {"windcode": "net_cash_recp_disp_sobu", "metricName": "处置子公司及其他营业单位收到的现金净额", "metricCode": "处置子公司及其他营业单位收到的现金净额", "description": "处置子公司及其他营业单位收到的现金净额", "unit": "", "reportType": "现金流量表"}], "matchCount": 2, "hasWindCode": true}, {"key": "33", "item": "收到其他与投资活动有关的现金", "currentAmount": "", "previousAmount": "", "level": 0, "rowType": "normal", "sheetName": "现金流量表", "reportType": "现金流量表", "originalCurrentAmount": "", "originalPreviousAmount": "", "matchedMetrics": [{"name": "收到其他与投资活动有关的现金", "code": "收到其他与投资活动有关的现金", "description": "收到其他与投资活动有关的现金", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "other_cash_recp_ral_inv_act"}, {"name": "其他", "code": "其他", "description": "其他", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "others"}], "windcode": "other_cash_recp_ral_inv_act", "windcodes": ["other_cash_recp_ral_inv_act", "others"], "windcodeDetails": [{"windcode": "other_cash_recp_ral_inv_act", "metricName": "收到其他与投资活动有关的现金", "metricCode": "收到其他与投资活动有关的现金", "description": "收到其他与投资活动有关的现金", "unit": "", "reportType": "现金流量表"}, {"windcode": "others", "metricName": "其他", "metricCode": "其他", "description": "其他", "unit": "", "reportType": "现金流量表"}], "matchCount": 2, "hasWindCode": true}, {"key": "34", "item": "投资活动现金流入小计", "currentAmount": "402,363.18", "previousAmount": "69,299.43", "level": 0, "rowType": "normal", "sheetName": "现金流量表", "reportType": "现金流量表", "originalCurrentAmount": "402363.18", "originalPreviousAmount": "69299.43", "matchedMetrics": [{"name": "投资活动现金流入小计", "code": "投资活动现金流入小计", "description": "投资活动现金流入小计", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "stot_cash_inflows_inv_act"}], "windcode": "stot_cash_inflows_inv_act", "windcodes": ["stot_cash_inflows_inv_act"], "windcodeDetails": [{"windcode": "stot_cash_inflows_inv_act", "metricName": "投资活动现金流入小计", "metricCode": "投资活动现金流入小计", "description": "投资活动现金流入小计", "unit": "", "reportType": "现金流量表"}], "matchCount": 1, "hasWindCode": true}, {"key": "35", "item": "购建固定资产、无形资产和其他长期资产支付的现金", "currentAmount": "134,857,197.50", "previousAmount": "118,380,066.99", "level": 0, "rowType": "normal", "sheetName": "现金流量表", "reportType": "现金流量表", "originalCurrentAmount": "134857197.5", "originalPreviousAmount": "118380066.99", "matchedMetrics": [{"name": "购建固定资产、无形资产和其他长期资产支付的现金", "code": "购建固定资产、无形资产和其他长期资产支付的现金", "description": "购建固定资产、无形资产和其他长期资产支付的现金", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "cash_pay_acq_const_fiolta"}, {"name": "其他", "code": "其他", "description": "其他", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "others"}, {"name": "固定资产", "code": "固定资产", "description": "固定资产", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "fix_assets"}, {"name": "无形资产", "code": "无形资产", "description": "无形资产", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "intang_assets"}], "windcode": "cash_pay_acq_const_fiolta", "windcodes": ["cash_pay_acq_const_fiolta", "others", "fix_assets", "intang_assets"], "windcodeDetails": [{"windcode": "cash_pay_acq_const_fiolta", "metricName": "购建固定资产、无形资产和其他长期资产支付的现金", "metricCode": "购建固定资产、无形资产和其他长期资产支付的现金", "description": "购建固定资产、无形资产和其他长期资产支付的现金", "unit": "", "reportType": "现金流量表"}, {"windcode": "others", "metricName": "其他", "metricCode": "其他", "description": "其他", "unit": "", "reportType": "现金流量表"}, {"windcode": "fix_assets", "metricName": "固定资产", "metricCode": "固定资产", "description": "固定资产", "unit": "", "reportType": "现金流量表"}, {"windcode": "intang_assets", "metricName": "无形资产", "metricCode": "无形资产", "description": "无形资产", "unit": "", "reportType": "现金流量表"}], "matchCount": 4, "hasWindCode": true}, {"key": "36", "item": "投资支付的现金", "currentAmount": "5,270,000.00", "previousAmount": "", "level": 0, "rowType": "normal", "sheetName": "现金流量表", "reportType": "现金流量表", "originalCurrentAmount": "5270000.0", "originalPreviousAmount": "", "matchedMetrics": [{"name": "投资支付的现金", "code": "投资支付的现金", "description": "投资支付的现金", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "cash_paid_invest"}], "windcode": "cash_paid_invest", "windcodes": ["cash_paid_invest"], "windcodeDetails": [{"windcode": "cash_paid_invest", "metricName": "投资支付的现金", "metricCode": "投资支付的现金", "description": "投资支付的现金", "unit": "", "reportType": "现金流量表"}], "matchCount": 1, "hasWindCode": true}, {"key": "37", "item": "质押贷款净增加额", "currentAmount": "", "previousAmount": "", "level": 0, "rowType": "normal", "sheetName": "现金流量表", "reportType": "现金流量表", "originalCurrentAmount": "", "originalPreviousAmount": "", "matchedMetrics": [{"name": "质押贷款净增加额", "code": "质押贷款净增加额", "description": "质押贷款净增加额", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "net_incr_pledge_loan"}], "windcode": "net_incr_pledge_loan", "windcodes": ["net_incr_pledge_loan"], "windcodeDetails": [{"windcode": "net_incr_pledge_loan", "metricName": "质押贷款净增加额", "metricCode": "质押贷款净增加额", "description": "质押贷款净增加额", "unit": "", "reportType": "现金流量表"}], "matchCount": 1, "hasWindCode": true}, {"key": "38", "item": "取得子公司及其他营业单位支付的现金净额", "currentAmount": "", "previousAmount": "", "level": 0, "rowType": "normal", "sheetName": "现金流量表", "reportType": "现金流量表", "originalCurrentAmount": "", "originalPreviousAmount": "", "matchedMetrics": [{"name": "其他", "code": "其他", "description": "其他", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "others"}, {"name": "取得子公司及其他营业单位支付的现金净额", "code": "取得子公司及其他营业单位支付的现金净额", "description": "取得子公司及其他营业单位支付的现金净额", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "net_cash_pay_aquis_sobu"}], "windcode": "others", "windcodes": ["others", "net_cash_pay_aquis_sobu"], "windcodeDetails": [{"windcode": "others", "metricName": "其他", "metricCode": "其他", "description": "其他", "unit": "", "reportType": "现金流量表"}, {"windcode": "net_cash_pay_aquis_sobu", "metricName": "取得子公司及其他营业单位支付的现金净额", "metricCode": "取得子公司及其他营业单位支付的现金净额", "description": "取得子公司及其他营业单位支付的现金净额", "unit": "", "reportType": "现金流量表"}], "matchCount": 2, "hasWindCode": true}, {"key": "39", "item": "支付其他与投资活动有关的现金", "currentAmount": "", "previousAmount": "", "level": 0, "rowType": "normal", "sheetName": "现金流量表", "reportType": "现金流量表", "originalCurrentAmount": "", "originalPreviousAmount": "", "matchedMetrics": [{"name": "其他", "code": "其他", "description": "其他", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "others"}, {"name": "支付其他与投资活动有关的现金", "code": "支付其他与投资活动有关的现金", "description": "支付其他与投资活动有关的现金", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "other_cash_pay_ral_inv_act"}], "windcode": "others", "windcodes": ["others", "other_cash_pay_ral_inv_act"], "windcodeDetails": [{"windcode": "others", "metricName": "其他", "metricCode": "其他", "description": "其他", "unit": "", "reportType": "现金流量表"}, {"windcode": "other_cash_pay_ral_inv_act", "metricName": "支付其他与投资活动有关的现金", "metricCode": "支付其他与投资活动有关的现金", "description": "支付其他与投资活动有关的现金", "unit": "", "reportType": "现金流量表"}], "matchCount": 2, "hasWindCode": true}, {"key": "40", "item": "投资活动现金流出小计", "currentAmount": "140,127,197.50", "previousAmount": "118,380,066.99", "level": 0, "rowType": "normal", "sheetName": "现金流量表", "reportType": "现金流量表", "originalCurrentAmount": "140127197.5", "originalPreviousAmount": "118380066.99", "matchedMetrics": [{"name": "投资活动现金流出小计", "code": "投资活动现金流出小计", "description": "投资活动现金流出小计", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "stot_cash_outflows_inv_act"}], "windcode": "stot_cash_outflows_inv_act", "windcodes": ["stot_cash_outflows_inv_act"], "windcodeDetails": [{"windcode": "stot_cash_outflows_inv_act", "metricName": "投资活动现金流出小计", "metricCode": "投资活动现金流出小计", "description": "投资活动现金流出小计", "unit": "", "reportType": "现金流量表"}], "matchCount": 1, "hasWindCode": true}, {"key": "41", "item": "投资活动产生的现金流量净额", "currentAmount": "-139,724,834.32", "previousAmount": "-118,310,767.56", "level": 0, "rowType": "normal", "sheetName": "现金流量表", "reportType": "现金流量表", "originalCurrentAmount": "-139724834.32", "originalPreviousAmount": "-118310767.56", "matchedMetrics": [{"name": "投资活动产生的现金流量净额", "code": "投资活动产生的现金流量净额", "description": "投资活动产生的现金流量净额", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "net_cash_flows_inv_act"}, {"name": "投资活动产生的现金流量净额差额(合计平衡项目)", "code": "投资活动产生的现金流量净额差额(合计平衡项目)", "description": "投资活动产生的现金流量净额差额(合计平衡项目)", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "cf_inv_act_netting"}], "windcode": "net_cash_flows_inv_act", "windcodes": ["net_cash_flows_inv_act", "cf_inv_act_netting"], "windcodeDetails": [{"windcode": "net_cash_flows_inv_act", "metricName": "投资活动产生的现金流量净额", "metricCode": "投资活动产生的现金流量净额", "description": "投资活动产生的现金流量净额", "unit": "", "reportType": "现金流量表"}, {"windcode": "cf_inv_act_netting", "metricName": "投资活动产生的现金流量净额差额(合计平衡项目)", "metricCode": "投资活动产生的现金流量净额差额(合计平衡项目)", "description": "投资活动产生的现金流量净额差额(合计平衡项目)", "unit": "", "reportType": "现金流量表"}], "matchCount": 2, "hasWindCode": true}, {"key": "42", "item": "三、筹资活动产生的现金流量：", "currentAmount": "", "previousAmount": "", "level": 0, "rowType": "normal", "sheetName": "现金流量表", "reportType": "现金流量表", "originalCurrentAmount": "", "originalPreviousAmount": "", "matchedMetrics": [], "windcode": null, "windcodes": [], "windcodeDetails": [], "matchCount": 0, "hasWindCode": false}, {"key": "43", "item": "吸收投资收到的现金", "currentAmount": "86,948,213.45", "previousAmount": "72,053,396.66", "level": 0, "rowType": "normal", "sheetName": "现金流量表", "reportType": "现金流量表", "originalCurrentAmount": "86948213.45", "originalPreviousAmount": "72053396.66", "matchedMetrics": [{"name": "吸收投资收到的现金", "code": "吸收投资收到的现金", "description": "吸收投资收到的现金", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "cash_recp_cap_contrib"}], "windcode": "cash_recp_cap_contrib", "windcodes": ["cash_recp_cap_contrib"], "windcodeDetails": [{"windcode": "cash_recp_cap_contrib", "metricName": "吸收投资收到的现金", "metricCode": "吸收投资收到的现金", "description": "吸收投资收到的现金", "unit": "", "reportType": "现金流量表"}], "matchCount": 1, "hasWindCode": true}, {"key": "44", "item": "其中：子公司吸收少数股东投资收到的现金", "currentAmount": "", "previousAmount": "", "level": 0, "rowType": "normal", "sheetName": "现金流量表", "reportType": "现金流量表", "originalCurrentAmount": "", "originalPreviousAmount": "", "matchedMetrics": [{"name": "子公司吸收少数股东投资收到的现金", "code": "子公司吸收少数股东投资收到的现金", "description": "子公司吸收少数股东投资收到的现金", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "cash_rec_saims"}], "windcode": "cash_rec_saims", "windcodes": ["cash_rec_saims"], "windcodeDetails": [{"windcode": "cash_rec_saims", "metricName": "子公司吸收少数股东投资收到的现金", "metricCode": "子公司吸收少数股东投资收到的现金", "description": "子公司吸收少数股东投资收到的现金", "unit": "", "reportType": "现金流量表"}], "matchCount": 1, "hasWindCode": true}, {"key": "45", "item": "取得借款收到的现金", "currentAmount": "562,500,000.00", "previousAmount": "259,446,132.17", "level": 0, "rowType": "normal", "sheetName": "现金流量表", "reportType": "现金流量表", "originalCurrentAmount": "562500000.0", "originalPreviousAmount": "259446132.17", "matchedMetrics": [{"name": "取得借款收到的现金", "code": "取得借款收到的现金", "description": "取得借款收到的现金", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "cash_recp_borrow"}], "windcode": "cash_recp_borrow", "windcodes": ["cash_recp_borrow"], "windcodeDetails": [{"windcode": "cash_recp_borrow", "metricName": "取得借款收到的现金", "metricCode": "取得借款收到的现金", "description": "取得借款收到的现金", "unit": "", "reportType": "现金流量表"}], "matchCount": 1, "hasWindCode": true}, {"key": "46", "item": "收到其他与筹资活动有关的现金", "currentAmount": "5,120,010.75", "previousAmount": "", "level": 0, "rowType": "normal", "sheetName": "现金流量表", "reportType": "现金流量表", "originalCurrentAmount": "5120010.75", "originalPreviousAmount": "", "matchedMetrics": [{"name": "收到其他与筹资活动有关的现金", "code": "收到其他与筹资活动有关的现金", "description": "收到其他与筹资活动有关的现金", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "other_cash_recp_ral_fnc_act"}, {"name": "其他", "code": "其他", "description": "其他", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "others"}], "windcode": "other_cash_recp_ral_fnc_act", "windcodes": ["other_cash_recp_ral_fnc_act", "others"], "windcodeDetails": [{"windcode": "other_cash_recp_ral_fnc_act", "metricName": "收到其他与筹资活动有关的现金", "metricCode": "收到其他与筹资活动有关的现金", "description": "收到其他与筹资活动有关的现金", "unit": "", "reportType": "现金流量表"}, {"windcode": "others", "metricName": "其他", "metricCode": "其他", "description": "其他", "unit": "", "reportType": "现金流量表"}], "matchCount": 2, "hasWindCode": true}, {"key": "47", "item": "筹资活动现金流入小计", "currentAmount": "654,568,224.20", "previousAmount": "331,499,528.83", "level": 0, "rowType": "normal", "sheetName": "现金流量表", "reportType": "现金流量表", "originalCurrentAmount": "654568224.2", "originalPreviousAmount": "331499528.83", "matchedMetrics": [{"name": "筹资活动现金流入小计", "code": "筹资活动现金流入小计", "description": "筹资活动现金流入小计", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "stot_cash_inflows_fnc_act"}], "windcode": "stot_cash_inflows_fnc_act", "windcodes": ["stot_cash_inflows_fnc_act"], "windcodeDetails": [{"windcode": "stot_cash_inflows_fnc_act", "metricName": "筹资活动现金流入小计", "metricCode": "筹资活动现金流入小计", "description": "筹资活动现金流入小计", "unit": "", "reportType": "现金流量表"}], "matchCount": 1, "hasWindCode": true}, {"key": "48", "item": "偿还债务支付的现金", "currentAmount": "281,000,000.00", "previousAmount": "253,129,120.64", "level": 0, "rowType": "normal", "sheetName": "现金流量表", "reportType": "现金流量表", "originalCurrentAmount": "281000000.0", "originalPreviousAmount": "253129120.64", "matchedMetrics": [{"name": "偿还债务支付的现金", "code": "偿还债务支付的现金", "description": "偿还债务支付的现金", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "cash_prepay_amt_borr"}], "windcode": "cash_prepay_amt_borr", "windcodes": ["cash_prepay_amt_borr"], "windcodeDetails": [{"windcode": "cash_prepay_amt_borr", "metricName": "偿还债务支付的现金", "metricCode": "偿还债务支付的现金", "description": "偿还债务支付的现金", "unit": "", "reportType": "现金流量表"}], "matchCount": 1, "hasWindCode": true}, {"key": "49", "item": "分配股利、利润或偿付利息支付的现金", "currentAmount": "430,019,391.10", "previousAmount": "211,102,164.52", "level": 0, "rowType": "normal", "sheetName": "现金流量表", "reportType": "现金流量表", "originalCurrentAmount": "430019391.1", "originalPreviousAmount": "211102164.52", "matchedMetrics": [{"name": "分配股利、利润或偿付利息支付的现金", "code": "分配股利、利润或偿付利息支付的现金", "description": "分配股利、利润或偿付利息支付的现金", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "cash_pay_dist_dpcp_int_exp"}], "windcode": "cash_pay_dist_dpcp_int_exp", "windcodes": ["cash_pay_dist_dpcp_int_exp"], "windcodeDetails": [{"windcode": "cash_pay_dist_dpcp_int_exp", "metricName": "分配股利、利润或偿付利息支付的现金", "metricCode": "分配股利、利润或偿付利息支付的现金", "description": "分配股利、利润或偿付利息支付的现金", "unit": "", "reportType": "现金流量表"}], "matchCount": 1, "hasWindCode": true}, {"key": "50", "item": "其中：子公司支付给少数股东的股利、利润", "currentAmount": "112,680,000.00", "previousAmount": "", "level": 0, "rowType": "normal", "sheetName": "现金流量表", "reportType": "现金流量表", "originalCurrentAmount": "112680000.0", "originalPreviousAmount": "", "matchedMetrics": [{"name": "子公司支付给少数股东的股利、利润", "code": "子公司支付给少数股东的股利、利润", "description": "子公司支付给少数股东的股利、利润", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "dvd_profit_paid_sc_ms"}], "windcode": "dvd_profit_paid_sc_ms", "windcodes": ["dvd_profit_paid_sc_ms"], "windcodeDetails": [{"windcode": "dvd_profit_paid_sc_ms", "metricName": "子公司支付给少数股东的股利、利润", "metricCode": "子公司支付给少数股东的股利、利润", "description": "子公司支付给少数股东的股利、利润", "unit": "", "reportType": "现金流量表"}], "matchCount": 1, "hasWindCode": true}, {"key": "51", "item": "支付其他与筹资活动有关的现金", "currentAmount": "94,726,334.47", "previousAmount": "97,498,072.89", "level": 0, "rowType": "normal", "sheetName": "现金流量表", "reportType": "现金流量表", "originalCurrentAmount": "94726334.47", "originalPreviousAmount": "97498072.89", "matchedMetrics": [{"name": "其他", "code": "其他", "description": "其他", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "others"}, {"name": "支付其他与筹资活动有关的现金", "code": "支付其他与筹资活动有关的现金", "description": "支付其他与筹资活动有关的现金", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "other_cash_pay_ral_fnc_act"}], "windcode": "others", "windcodes": ["others", "other_cash_pay_ral_fnc_act"], "windcodeDetails": [{"windcode": "others", "metricName": "其他", "metricCode": "其他", "description": "其他", "unit": "", "reportType": "现金流量表"}, {"windcode": "other_cash_pay_ral_fnc_act", "metricName": "支付其他与筹资活动有关的现金", "metricCode": "支付其他与筹资活动有关的现金", "description": "支付其他与筹资活动有关的现金", "unit": "", "reportType": "现金流量表"}], "matchCount": 2, "hasWindCode": true}, {"key": "52", "item": "筹资活动现金流出小计", "currentAmount": "805,745,725.57", "previousAmount": "561,729,358.05", "level": 0, "rowType": "normal", "sheetName": "现金流量表", "reportType": "现金流量表", "originalCurrentAmount": "805745725.57", "originalPreviousAmount": "561729358.05", "matchedMetrics": [{"name": "筹资活动现金流出小计", "code": "筹资活动现金流出小计", "description": "筹资活动现金流出小计", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "stot_cash_outflows_fnc_act"}], "windcode": "stot_cash_outflows_fnc_act", "windcodes": ["stot_cash_outflows_fnc_act"], "windcodeDetails": [{"windcode": "stot_cash_outflows_fnc_act", "metricName": "筹资活动现金流出小计", "metricCode": "筹资活动现金流出小计", "description": "筹资活动现金流出小计", "unit": "", "reportType": "现金流量表"}], "matchCount": 1, "hasWindCode": true}, {"key": "53", "item": "筹资活动产生的现金流量净额", "currentAmount": "-151,177,501.37", "previousAmount": "-230,229,829.22", "level": 0, "rowType": "normal", "sheetName": "现金流量表", "reportType": "现金流量表", "originalCurrentAmount": "-151177501.37", "originalPreviousAmount": "-230229829.22", "matchedMetrics": [{"name": "筹资活动产生的现金流量净额差额(合计平衡项目)", "code": "筹资活动产生的现金流量净额差额(合计平衡项目)", "description": "筹资活动产生的现金流量净额差额(合计平衡项目)", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "cf_fnc_act_netting"}, {"name": "筹资活动产生的现金流量净额", "code": "筹资活动产生的现金流量净额", "description": "筹资活动产生的现金流量净额", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "net_cash_flows_fnc_act"}], "windcode": "cf_fnc_act_netting", "windcodes": ["cf_fnc_act_netting", "net_cash_flows_fnc_act"], "windcodeDetails": [{"windcode": "cf_fnc_act_netting", "metricName": "筹资活动产生的现金流量净额差额(合计平衡项目)", "metricCode": "筹资活动产生的现金流量净额差额(合计平衡项目)", "description": "筹资活动产生的现金流量净额差额(合计平衡项目)", "unit": "", "reportType": "现金流量表"}, {"windcode": "net_cash_flows_fnc_act", "metricName": "筹资活动产生的现金流量净额", "metricCode": "筹资活动产生的现金流量净额", "description": "筹资活动产生的现金流量净额", "unit": "", "reportType": "现金流量表"}], "matchCount": 2, "hasWindCode": true}, {"key": "54", "item": "四、汇率变动对现金及现金等价物的影响", "currentAmount": "6,745,757.84", "previousAmount": "7,189,085.36", "level": 0, "rowType": "normal", "sheetName": "现金流量表", "reportType": "现金流量表", "originalCurrentAmount": "6745757.84", "originalPreviousAmount": "7189085.36", "matchedMetrics": [], "windcode": null, "windcodes": [], "windcodeDetails": [], "matchCount": 0, "hasWindCode": false}, {"key": "55", "item": "五、现金及现金等价物净增加额", "currentAmount": "-531,149,944.58", "previousAmount": "-197,419,917.00", "level": 0, "rowType": "normal", "sheetName": "现金流量表", "reportType": "现金流量表", "originalCurrentAmount": "-531149944.58", "originalPreviousAmount": "-197419917.0", "matchedMetrics": [{"name": "现金及现金等价物净增加额", "code": "现金及现金等价物净增加额", "description": "现金及现金等价物净增加额", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "net_incr_cash_cash_equ_dm"}], "windcode": "net_incr_cash_cash_equ_dm", "windcodes": ["net_incr_cash_cash_equ_dm"], "windcodeDetails": [{"windcode": "net_incr_cash_cash_equ_dm", "metricName": "现金及现金等价物净增加额", "metricCode": "现金及现金等价物净增加额", "description": "现金及现金等价物净增加额", "unit": "", "reportType": "现金流量表"}], "matchCount": 1, "hasWindCode": true}, {"key": "56", "item": "加：期初现金及现金等价物余额", "currentAmount": "2,703,864,160.18", "previousAmount": "2,901,284,077.18", "level": 0, "rowType": "normal", "sheetName": "现金流量表", "reportType": "现金流量表", "originalCurrentAmount": "2703864160.18", "originalPreviousAmount": "2901284077.18", "matchedMetrics": [{"name": "期初现金及现金等价物余额", "code": "期初现金及现金等价物余额", "description": "期初现金及现金等价物余额", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "cash_cash_equ_beg_period"}], "windcode": "cash_cash_equ_beg_period", "windcodes": ["cash_cash_equ_beg_period"], "windcodeDetails": [{"windcode": "cash_cash_equ_beg_period", "metricName": "期初现金及现金等价物余额", "metricCode": "期初现金及现金等价物余额", "description": "期初现金及现金等价物余额", "unit": "", "reportType": "现金流量表"}], "matchCount": 1, "hasWindCode": true}, {"key": "57", "item": "六、期末现金及现金等价物余额", "currentAmount": "2,172,714,215.60", "previousAmount": "2,703,864,160.18", "level": 0, "rowType": "normal", "sheetName": "现金流量表", "reportType": "现金流量表", "originalCurrentAmount": "2172714215.6", "originalPreviousAmount": "2703864160.18", "matchedMetrics": [{"name": "期末现金及现金等价物余额", "code": "期末现金及现金等价物余额", "description": "期末现金及现金等价物余额", "formula": "", "formula_desc": "", "value_type": "", "unit": "", "WindCode": "cash_cash_equ_end_period"}], "windcode": "cash_cash_equ_end_period", "windcodes": ["cash_cash_equ_end_period"], "windcodeDetails": [{"windcode": "cash_cash_equ_end_period", "metricName": "期末现金及现金等价物余额", "metricCode": "期末现金及现金等价物余额", "description": "期末现金及现金等价物余额", "unit": "", "reportType": "现金流量表"}], "matchCount": 1, "hasWindCode": true}]