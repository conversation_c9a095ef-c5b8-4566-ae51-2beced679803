# error_handler.py
import logging
import traceback
from typing import Optional, Dict, Any
from flask import current_app

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('api_error.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

class APIError(Exception):
    """API错误基类"""
    def __init__(self, message: str, error_code: str = None, status_code: int = 500):
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.status_code = status_code

class ValidationError(APIError):
    """参数验证错误"""
    def __init__(self, message: str):
        super().__init__(message, "VALIDATION_ERROR", 400)

class NotFoundError(APIError):
    """资源未找到错误"""
    def __init__(self, message: str):
        super().__init__(message, "NOT_FOUND", 404)

class DatabaseError(APIError):
    """数据库错误"""
    def __init__(self, message: str):
        super().__init__(message, "DATABASE_ERROR", 500)

def handle_exception(e: Exception, context: str = "") -> Dict[str, Any]:
    """
    统一异常处理函数
    
    Args:
        e: 异常对象
        context: 错误上下文信息
    
    Returns:
        包含错误信息的字典
    """
    # 记录详细错误日志
    error_details = {
        'error_type': type(e).__name__,
        'error_message': str(e),
        'context': context,
        'traceback': traceback.format_exc()
    }
    
    # 使用Flask的logger或默认logger
    if current_app:
        current_app.logger.error(f"API Error in {context}: {str(e)}", exc_info=True)
    else:
        logger.error(f"API Error in {context}: {str(e)}", exc_info=True)
    
    # 根据异常类型返回不同的错误信息
    if isinstance(e, APIError):
        return {
            'success': False,
            'error': e.message,
            'error_code': e.error_code
        }, e.status_code
    
    # 对于其他异常，返回通用错误信息
    return {
        'success': False,
        'error': '系统异常，请稍后重试'
    }, 500

def log_error(e: Exception, context: str = "", extra_info: Dict = None):
    """
    记录错误日志但不抛出异常
    
    Args:
        e: 异常对象
        context: 错误上下文
        extra_info: 额外信息
    """
    error_info = {
        'error_type': type(e).__name__,
        'error_message': str(e),
        'context': context,
        'traceback': traceback.format_exc()
    }
    
    if extra_info:
        error_info.update(extra_info)
    
    if current_app:
        current_app.logger.error(f"Error in {context}: {str(e)}", exc_info=True)
    else:
        logger.error(f"Error in {context}: {str(e)}", exc_info=True)

def safe_execute(func, *args, context: str = "", **kwargs):
    """
    安全执行函数，自动处理异常
    
    Args:
        func: 要执行的函数
        context: 错误上下文
        *args, **kwargs: 函数参数
    
    Returns:
        函数执行结果或错误信息
    """
    try:
        return func(*args, **kwargs)
    except Exception as e:
        log_error(e, context)
        return handle_exception(e, context)[0] 