import os
import sys

# 尝试从 api 包导入 create_app 和从 config 包导入 config
# 注意：在 api/__init__.py 中，我们是从 .config import config
# 这意味着 api 包内部会查找同级的 config 包/模块
# 为了让 app.py 能正确加载配置，我们需要确保 config.settings.py 中的 config 字典被正确使用
# 我们将直接从 api 包创建 app，它内部会处理配置加载

# Determine how to import create_app based on execution context
if __name__ == "__main__" and (__package__ is None or __package__ == ''):
    # Script executed directly (e.g. python app.py from api folder, or python api/app.py from project root)
    # and not as a module (e.g. python -m api.app).
    # Add project root to sys.path for absolute import.
    # current_file_path is d:\StudyCode\fin\财务数据分析网站\api\app.py (example)
    current_file_path = os.path.abspath(__file__)
    # api_dir is d:\StudyCode\fin\财务数据分析网站\api
    api_dir = os.path.dirname(current_file_path)
    # project_root is d:\StudyCode\fin\财务数据分析网站
    project_root = os.path.dirname(api_dir)
    if project_root not in sys.path:
        sys.path.insert(0, project_root)
    # Perform an absolute import assuming 'api' is a package in project_root
    from api import create_app
else:
    # Script executed as part of a package (e.g., python -m api.app or by Gunicorn if it were loading api.app)
    # Or if __package__ is set (e.g. when imported as 'api.app')
    from . import create_app # Relative import

try:
    # 'create_app' is now defined from the logic above

    # config_name 通常从环境变量获取，例如 FLASK_CONFIG
    config_name = os.environ.get('FLASK_CONFIG') or 'default'
    app = create_app(config_name)
    print(f"Flask app created with '{config_name}' configuration.")

    # 打印加载的数据库配置以供调试ddddddddfasfdsafdsafdsafdsafdsafdsafdsafdsafdsafdsafdsafdsaf
    # with app.app_context():ddddddd
    #     from flask import current_app
    #     db_conf = current_app.config.get('DB_CONFIG')
    #     if db_conf:
    #         print(f"DB Config loaded: Server - {db_conf.get('server')}, DB - {db_conf.get('database')}")
    #     else:
    #         print("DB_CONFIG not found in app.config")

except ImportError as e:
    print(f"Error importing create_app or during app creation: {e}")
    # 提供一个回退或退出，因为没有应用实例无法继续
    sys.exit(f"Failed to initialize the Flask application due to import error: {e}")
except KeyError as e:
    print(f"Error: Configuration '{config_name}' not found in config.py. Error: {e}")
    sys.exit(f"Failed to initialize the Flask application due to configuration error: {e}")

if __name__ == '__main__':
    # 端口号可以从环境变量或配置文件中读取
    port = int(os.environ.get('PORT', 5000))
    # 生产环境中不应使用 debug=True
    # debug_mode = app.config.get('DEBUG', False)
    # app.run(host='0.0.0.0', port=port, debug=debug_mode)
    # 为了简单起见，暂时硬编码 debug 模式，后续可以根据 FLASK_ENV 环境变量调整
    print(f"Starting Flask development server on http://127.0.0.1:{port}/")
    app.run(host='0.0.0.0', port=port, debug=True) # Debug True for development
