import React, { useEffect, useRef } from 'react';
import { Chart } from '@antv/g2';

const CashHorizontalBar = ({ 
  data, 
  title = '', 
  height = 260, 
  style,
  isFullscreen = false,
  isIndividualFullscreen = false
}) => {
  const container = useRef();
  
  // 解析style.height，优先使用style.height，否则用props.height
  let computedHeight = height;
  if (isIndividualFullscreen) {
    // 单个全屏时，使用视口高度的90%
    computedHeight = window.innerHeight * 0.9;
  } else if (isFullscreen) {
    // 整体全屏时，直接使用传入的 height（已经通过 scale 计算过）
    computedHeight = height;
  } else if (style && style.height) {
    if (typeof style.height === 'number') {
      computedHeight = style.height;
    } else if (typeof style.height === 'string' && style.height.endsWith('px')) {
      computedHeight = parseInt(style.height);
    } else if (typeof style.height === 'string') {
      computedHeight = parseInt(style.height);
    }
  }

  useEffect(() => {
    if (!container.current) return;
    container.current.innerHTML = '';
    if (!Array.isArray(data) || data.length === 0) return;

    const chart = new Chart({
      container: container.current,
      autoFit: true,
      height: computedHeight,
    });

    chart.options({
      type: 'interval',
      data: data,
      coordinate: { transform: [{ type: 'transpose' }] },
      encode: {
        x: 'year',
        y: 'value',
        color: 'company',
        series: 'company', // 让同一年不同公司并排
      },
      axis: {
        x: { title: false,labelFill:'#000000',labelFillOpacity:2 },
        y: false
      },
      legend: {
        color: { 
          position:'top',
          maxRows:1,
        }
      },
      labels:[ {
        text: (d) => {
          const total =d.displayValue ?? d.value;
          if (total === 0) return '';
          return  (total);
        },  style: {
          fontSize: 14, 
        },
      }],interaction: {
        tooltip: {
          css: {

            '.g2-tooltip-list-item': {
              'font-size': '18px',
            },

          },

        }
      },
      tooltip: {
        items: [
          (datum) => ({
            name: '年份',
            value: datum.year,
          }), (datum) => ({
            name: datum.company,
            value: datum.displayValue ?? datum.value ?? '',
          }),
        ]
      }
    });
    chart
      .labelTransform({ type: 'overlapHide' })
      .labelTransform({ type: 'contrastReverse' });
    chart.render();

    // 添加 resize 监听器，当容器尺寸变化时重新渲染
    const resizeObserver = new ResizeObserver(() => {
      if (chart && !chart.destroyed) {
        chart.render();
      }
    });
    
    if (container.current) {
      resizeObserver.observe(container.current);
    }

    return () => {
      resizeObserver.disconnect();
      chart.destroy();
    };
  }, [data, computedHeight, title, isIndividualFullscreen]);

  return (
    <div style={{ width: '100%', height: '100%' }}>
      {title && <div style={{ textAlign: 'center', fontWeight: 'bold' }}>{title}</div>}
      <div ref={container} style={{ width: '100%', height: '100%' }} />
    </div>
  );
};

export default CashHorizontalBar; 