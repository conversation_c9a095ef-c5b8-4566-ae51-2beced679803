# decorators.py
from functools import wraps
from flask import jsonify
from .error_handler import handle_exception

def api_error_handler(context: str = ""):
    """
    API错误处理装饰器
    
    Args:
        context: 错误上下文信息
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            try:
                return f(*args, **kwargs)
            except Exception as e:
                error_response, status_code = handle_exception(e, context)
                return jsonify(error_response), status_code
        return decorated_function
    return decorator

def validate_required_params(*required_params):
    """
    验证必需参数的装饰器
    
    Args:
        *required_params: 必需参数名列表
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            from flask import request
            from .error_handler import ValidationError
            
            # 获取请求数据
            if request.method == 'GET':
                data = request.args
            else:
                data = request.get_json() or {}
            
            # 检查必需参数
            missing_params = []
            for param in required_params:
                if param not in data or not data[param]:
                    missing_params.append(param)
            
            if missing_params:
                raise ValidationError(f"缺少必需参数: {', '.join(missing_params)}")
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator 