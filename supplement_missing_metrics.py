from decimal import Decimal, InvalidOperation
import pandas as pd
from WindPy import w
import uuid
from datetime import datetime
from database.db_handler import DatabaseHandler
from logging_config import setup_logging
import os

logger = setup_logging()

def get_wind_data(TickerSymbol, fields_str, rpt_date, rpt_type):
    """从Wind获取数据"""
    result = w.wss(
        TickerSymbol,
        fields_str,
        f"unit=1;rptDate={rpt_date};rptType={rpt_type};tradeDate={rpt_date};year={rpt_date[:4]}"
    )
    if result.ErrorCode != 0:
        logger.error(f'Wind API error for {TickerSymbol} on {rpt_date}. Code: {result.ErrorCode}, Msg: {result.Data[0][0]}')
        return None
    
    data = {}
    for i, field in enumerate(result.Fields):
        value = result.Data[i][0]
        if pd.isna(value):
            value = None
        data[field] = value
    
    if all(v is None for v in data.values()):
        logger.warning(f"No data returned for: {TickerSymbol} {rpt_date} for fields {fields_str}")
        return None
    return data

def get_yoy_data(symbol, fields, rpt_date):
    """获取同比数据"""
    last_year_date = str(int(rpt_date[:4]) - 1) + rpt_date[4:]
    return get_wind_data(symbol, fields, last_year_date, 3) or {}

def safe_convert_decimal(value, precision=(18, 6)):
    if value is None:
        return None, True
    try:
        dec_value = Decimal(str(value))
    except (TypeError, InvalidOperation):
        return None, False
    return dec_value, True

def convert_large_number(value):
    if value is None:
        return None
    try:
        if isinstance(value, str) and 'e' in value.lower():
            d = Decimal(value.lower().replace('E', 'E'))
            return format(d, 'f').rstrip('0').rstrip('.')
        elif isinstance(value, (float, int)):
            return format(Decimal(str(value)), 'f').rstrip('0').rstrip('.')
        else:
            return str(value)
    except:
        return str(value)[:500]

def insert_metrics_data(cursor, wind_data, yoy_data, date_obj, symbol, company_id):
    valid_data = []
    for code in wind_data.keys():
        raw_value = wind_data.get(code)
        value_dec, is_value_valid = safe_convert_decimal(raw_value)
        
        raw_yoy = yoy_data.get(code)
        yoy_dec, is_yoy_valid = safe_convert_decimal(raw_yoy)
        
        if not is_value_valid:
             logger.warning(f"Invalid value for {symbol} {code}: {raw_value}")
             continue

        ori_value_str = convert_large_number(raw_value)
        valid_data.append((
            str(uuid.uuid4()),
            code,
            value_dec,
            date_obj,
            yoy_dec,
            datetime.now(),
            symbol,
            ori_value_str,
            company_id
        ))
    
    if valid_data:
        cursor.executemany("""
            INSERT INTO WindMetrics 
            (ID, WindCode, Value, Date, YoYValue, CreateDate, TickerSymbol, OriValue, CompanyId)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, valid_data)
        logger.info(f"Successfully inserted {len(valid_data)} records for {symbol} on {date_obj.strftime('%Y-%m-%d')}")
    else:
        logger.warning(f"No valid data to insert for {symbol} on {date_obj.strftime('%Y-%m-%d')}")

def supplement_missing_metrics():
    db = DatabaseHandler()
    w.start()

    try:
        with db as conn:
            cursor = conn.connection.cursor()
            
            # 1. 获取所有启用的指标定义
            cursor.execute("SELECT WindCode FROM MetricsDefinition WHERE Enabled=1")
            all_defined_metrics = {row[0] for row in cursor.fetchall()}
            logger.info(f"Loaded {len(all_defined_metrics)} enabled metrics from MetricsDefinition.")

            # 2. 获取公司信息
            cursor.execute("SELECT TickerSymbol, CompanyId FROM CompanyInfo")
            company_dict = {row[0]: row[1] for row in cursor.fetchall()}
            logger.info(f"Loaded {len(company_dict)} company mappings.")

            # 3. 查询需要补充数据的目标（从数据库计算得出）
            cursor.execute("""
                SELECT DISTINCT TickerSymbol, Date 
                FROM WindMetrics 
                WHERE TickerSymbol IN (SELECT TickerSymbol FROM CompanyInfo)
                ORDER BY TickerSymbol, Date
            """)
            targets = cursor.fetchall()
            logger.info(f"Found {len(targets)} target combinations (company, date) to check for missing metrics.")

            # 4. 遍历目标公司和日期
            for ticker, report_date in targets:
                report_date_str = report_date.strftime('%Y-%m-%d')
                logger.info(f"Processing {ticker} for report date {report_date_str}...")
                try:
                    company_id = company_dict.get(ticker)
                    if not company_id:
                        logger.warning(f"CompanyId not found for {ticker}. Skipping.")
                        continue

                    # 5. 查询已存在的指标
                    cursor.execute("""
                        SELECT DISTINCT WindCode FROM WindMetrics 
                        WHERE TickerSymbol = ? AND Date = ?
                    """, (ticker, report_date))
                    existing_metrics = {row[0] for row in cursor.fetchall()}
                    logger.info(f"Found {len(existing_metrics)} existing metrics in WindMetrics for {ticker} on {report_date_str}.")

                    # 6. 确定缺失的指标
                    # 忽略大小写比较两个集合的差集
                    missing_metrics = {x.upper() for x in all_defined_metrics} - {x.upper() for x in existing_metrics}
                    if not missing_metrics:
                        logger.info(f"No missing metrics for {ticker} on {report_date_str}. Skipping.")
                        continue
                    
                    logger.info(f"Found {len(missing_metrics)} missing metrics. Fetching from Wind...")
                    
                    # 7. 从Wind获取缺失的指标数据
                    fields_to_fetch = ",".join(missing_metrics)
                    rpt_date_wind = report_date.strftime("%Y%m%d")
                    
                    wind_data = get_wind_data(ticker, fields_to_fetch, rpt_date_wind, 1)
                    if not wind_data:
                        logger.warning(f"Could not fetch any data for missing metrics for {ticker} on {report_date_str}.")
                        continue

                    yoy_data = get_yoy_data(ticker, fields_to_fetch, rpt_date_wind)

                    # 8. 插入数据
                    insert_metrics_data(cursor, wind_data, yoy_data, report_date, ticker, company_id)
                    conn.connection.commit()

                except Exception as e:
                    logger.error(f"Error processing {ticker} for {report_date_str}: {e}", exc_info=True)
                    conn.connection.rollback()

    except Exception as e:
        logger.error(f"A critical error occurred: {e}", exc_info=True)
    finally:
        w.stop()
        logger.info("Script finished.")

if __name__ == "__main__":
    # 自动从数据库查询需要补充数据的目标
    supplement_missing_metrics()