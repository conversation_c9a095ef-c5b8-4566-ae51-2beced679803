/* QuantitativeTable 样式优化 */
.quantitative-table {
  /* 表格基础样式 */
}

.quantitative-table .group-title {
  font-weight: bold;
  font-size: 14px;
  color: #1890ff;
  padding: 8px 0;
}

.quantitative-table .metric-unit {
  font-size: 12px;
  color: #999;
  margin-top: 2px;
}

.quantitative-table .metric-name-cell {
  padding: 4px 8px;
}

.quantitative-table .metric-value-cell {
  text-align: right;
  padding: 4px 8px;
}

/* 表格性能优化相关样式 */
.quantitative-table .ant-table-tbody > tr > td {
  transition: none; /* 移除过渡动画以提升性能 */
}

.quantitative-table .ant-table-thead > tr > th {
  background-color: #fafafa;
  font-weight: 600;
}

/* 响应式优化 */
@media (max-width: 768px) {
  .quantitative-table {
    font-size: 12px;
  }
  
  .quantitative-table .group-title {
    font-size: 13px;
  }
}