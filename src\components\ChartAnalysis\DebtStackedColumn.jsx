import React, { useEffect, useRef } from 'react';
import { Chart } from '@antv/g2';
import axios from 'axios';

const DebtStackedColumn = ({ data, height = 320, title }) => {
  const container = useRef();
  useEffect(() => {
    if (!container.current) return;
    container.current.innerHTML = '';
    const chart = new Chart({
      container: container.current,
      autoFit: true,
      height,
      title: {
        title: title,
       
      },
    });
    // 计算每个公司的总计值
    const companyTotals = {};
    data.forEach(d => {
      if (!companyTotals[d.company]) {
        companyTotals[d.company] = 0;
      }
      companyTotals[d.company] += d.value || 0;
    });
    Object.keys(companyTotals).forEach(company => {
      companyTotals[company] = Math.round(companyTotals[company]);
    });
    chart
      .interval()
      .data(data)
      .encode('x', 'company')
      .encode('y', 'value')
      .encode('color', 'type')
      .transform({ type: 'stackY' })
      .axis({ y: false, x: { title: false } })
      .legend('color', {
        position: 'top',
        maxRows: 1,
      }) .label({
        text: (d) => {
          const total = d.value;
          if (total === 0) return ''; // 为0时不显示
            return Math.round(total);
          
        },
       
      });
    // 只显示顶部总计标签
    chart
      .text()
      .data(Object.keys(companyTotals).map(company => ({
        company,
        total: companyTotals[company]
      })))
      .encode('x', 'company')
      .encode('y', 'total')
      .encode('text', 'total')
      .style({
        textAlign: 'center',
        fontSize: 14,
        fontWeight: 'bold',
        fill: '#333',
        dy: -5,
      })
      
      chart
      .labelTransform({ type: 'overlapHide' })
      .labelTransform({ type: 'contrastReverse' });
    chart.render();
    return () => chart.destroy();
  }, [data, height]);
  return <div ref={container} style={{ width: '100%', height: '100%' }} />;
};
export default DebtStackedColumn; 