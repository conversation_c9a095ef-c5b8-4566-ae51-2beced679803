#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
财务配置项表模型
用于存储Excel生成的配置数据
"""

import json
from datetime import datetime
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config.database_config import get_db_connection

class FinancialConfigModel:
    """
    财务配置项表模型
    """
    
    @staticmethod
    def create_tables():
        """
        创建配置项相关表
        """
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            # 创建配置项主表
            create_config_table = """
            IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='FinancialConfigs' AND xtype='U')
            CREATE TABLE FinancialConfigs (
                ConfigId INT IDENTITY(1,1) PRIMARY KEY,
                ConfigName NVARCHAR(100) NOT NULL,
                ConfigType NVARCHAR(50) NOT NULL, -- 'excel_generated', 'manual', etc.
                Description NVARCHAR(500),
                IsActive BIT DEFAULT 1,
                CreatedAt DATETIME DEFAULT GETDATE(),
                UpdatedAt DATETIME DEFAULT GETDATE(),
                CreatedBy NVARCHAR(50),
                ConfigData NTEXT -- JSON格式的完整配置数据
            )
            """
            
            # 创建配置项详情表
            create_config_items_table = """
            IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='FinancialConfigItems' AND xtype='U')
            CREATE TABLE FinancialConfigItems (
                ItemId INT IDENTITY(1,1) PRIMARY KEY,
                ConfigId INT NOT NULL,
                ReportType NVARCHAR(50) NOT NULL, -- '资产负债表', '利润表', '现金流量表'
                ItemKey NVARCHAR(50) NOT NULL,
                ItemName NVARCHAR(200) NOT NULL,
                WindCode NVARCHAR(50),
                IndicatorCode NVARCHAR(100),
                Level INT DEFAULT 1,
                RowType NVARCHAR(20) DEFAULT 'sub', -- 'header', 'sub', 'total'
                SortOrder INT DEFAULT 0,
                HasWindCode BIT DEFAULT 0,
                Unit NVARCHAR(20),
                Description NVARCHAR(500),
                IsActive BIT DEFAULT 1,
                CreatedAt DATETIME DEFAULT GETDATE(),
                FOREIGN KEY (ConfigId) REFERENCES FinancialConfigs(ConfigId)
            )
            """
            
            # 创建WindCode映射表
            create_windcode_mapping_table = """
            IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='FinancialWindCodeMappings' AND xtype='U')
            CREATE TABLE FinancialWindCodeMappings (
                MappingId INT IDENTITY(1,1) PRIMARY KEY,
                ConfigId INT NOT NULL,
                ReportType NVARCHAR(50) NOT NULL,
                WindCode NVARCHAR(50) NOT NULL,
                IndicatorName NVARCHAR(200),
                IndicatorCode NVARCHAR(100),
                Unit NVARCHAR(20),
                Description NVARCHAR(500),
                IsActive BIT DEFAULT 1,
                CreatedAt DATETIME DEFAULT GETDATE(),
                FOREIGN KEY (ConfigId) REFERENCES FinancialConfigs(ConfigId)
            )
            """
            
            # 执行创建表的SQL
            cursor.execute(create_config_table)
            cursor.execute(create_config_items_table)
            cursor.execute(create_windcode_mapping_table)
            
            conn.commit()
            conn.close()
            
            return True
            
        except Exception as e:
            raise Exception(f"创建配置表失败: {e}")
    
    @staticmethod
    def save_config(config_name, config_data, description=None, created_by=None):
        """
        保存配置到数据库
        
        Args:
            config_name: 配置名称
            config_data: 配置数据字典
            description: 配置描述
            created_by: 创建者
            
        Returns:
            int: 配置ID
        """
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            # 插入主配置记录
            insert_config_sql = """
            INSERT INTO FinancialConfigs (ConfigName, ConfigType, Description, CreatedBy, ConfigData)
            OUTPUT INSERTED.ConfigId
            VALUES (?, 'excel_generated', ?, ?, ?)
            """
            
            config_json = json.dumps(config_data, ensure_ascii=False)
            cursor.execute(insert_config_sql, (config_name, description, created_by, config_json))
            config_id = cursor.fetchone()[0]
            
            # 插入配置项详情
            for report_type, report_config in config_data.get('reportTypes', {}).items():
                items = report_config.get('items', [])
                
                for idx, item in enumerate(items):
                    insert_item_sql = """
                    INSERT INTO FinancialConfigItems (
                        ConfigId, ReportType, ItemKey, ItemName, WindCode, IndicatorCode,
                        Level, RowType, SortOrder, HasWindCode, Unit, Description
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """
                    
                    # 获取第一个WindCode（如果有）
                    windcode = None
                    indicator_code = None
                    unit = None
                    item_description = None
                    
                    if item.get('windcodeDetails'):
                        first_detail = item['windcodeDetails'][0]
                        windcode = first_detail.get('windCode')
                        indicator_code = first_detail.get('indicatorCode')
                        unit = first_detail.get('unit')
                        item_description = first_detail.get('description')
                    
                    cursor.execute(insert_item_sql, (
                        config_id,
                        report_type,
                        item.get('key', str(idx + 1)),
                        item.get('item', ''),
                        windcode,
                        indicator_code,
                        item.get('level', 1),
                        item.get('rowType', 'sub'),
                        idx,
                        item.get('hasWindCode', False),
                        unit,
                        item_description
                    ))
            
            # 插入WindCode映射
            for report_type, report_config in config_data.get('reportTypes', {}).items():
                windcodes = report_config.get('windCodes', [])
                
                for windcode in windcodes:
                    # 从L2BenchmarkIndicators表获取详细信息
                    get_indicator_sql = """
                    SELECT IndicatorNameCN, IndicatorNameEN, Unit, IndicatorDesc
                    FROM L2BenchmarkIndicators
                    WHERE WindCode = ? AND IsActive = 1
                    """
                    
                    cursor.execute(get_indicator_sql, (windcode,))
                    indicator_row = cursor.fetchone()
                    
                    if indicator_row:
                        insert_mapping_sql = """
                        INSERT INTO FinancialWindCodeMappings (
                            ConfigId, ReportType, WindCode, IndicatorName, IndicatorCode, Unit, Description
                        ) VALUES (?, ?, ?, ?, ?, ?, ?)
                        """
                        
                        cursor.execute(insert_mapping_sql, (
                            config_id,
                            report_type,
                            windcode,
                            indicator_row[0],  # IndicatorNameCN
                            indicator_row[1],  # IndicatorNameEN
                            indicator_row[2],  # Unit
                            indicator_row[3]   # IndicatorDesc
                        ))
            
            conn.commit()
            conn.close()
            
            return config_id
            
        except Exception as e:
            raise Exception(f"保存配置失败: {e}")
    
    @staticmethod
    def get_config_by_id(config_id):
        """
        根据ID获取配置
        
        Args:
            config_id: 配置ID
            
        Returns:
            dict: 配置数据
        """
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            # 获取主配置
            get_config_sql = """
            SELECT ConfigId, ConfigName, ConfigType, Description, IsActive, 
                   CreatedAt, UpdatedAt, CreatedBy, ConfigData
            FROM FinancialConfigs
            WHERE ConfigId = ? AND IsActive = 1
            """
            
            cursor.execute(get_config_sql, (config_id,))
            config_row = cursor.fetchone()
            
            if not config_row:
                return None
            
            config = {
                'configId': config_row[0],
                'configName': config_row[1],
                'configType': config_row[2],
                'description': config_row[3],
                'isActive': config_row[4],
                'createdAt': config_row[5].isoformat() if config_row[5] else None,
                'updatedAt': config_row[6].isoformat() if config_row[6] else None,
                'createdBy': config_row[7],
                'configData': json.loads(config_row[8]) if config_row[8] else {}
            }
            
            conn.close()
            return config
            
        except Exception as e:
            raise Exception(f"获取配置失败: {e}")
    
    @staticmethod
    def get_config_items_by_report_type(config_id, report_type):
        """
        根据配置ID和报表类型获取配置项
        
        Args:
            config_id: 配置ID
            report_type: 报表类型
            
        Returns:
            list: 配置项列表
        """
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            get_items_sql = """
            SELECT ItemId, ItemKey, ItemName, WindCode, IndicatorCode,
                   Level, RowType, SortOrder, HasWindCode, Unit, Description
            FROM FinancialConfigItems
            WHERE ConfigId = ? AND ReportType = ? AND IsActive = 1
            ORDER BY SortOrder, ItemId
            """
            
            cursor.execute(get_items_sql, (config_id, report_type))
            rows = cursor.fetchall()
            
            items = []
            for row in rows:
                item = {
                    'itemId': row[0],
                    'key': row[1],
                    'item': row[2],
                    'windCode': row[3],
                    'indicatorCode': row[4],
                    'level': row[5],
                    'rowType': row[6],
                    'sortOrder': row[7],
                    'hasWindCode': row[8],
                    'unit': row[9],
                    'description': row[10]
                }
                items.append(item)
            
            conn.close()
            return items
            
        except Exception as e:
            raise Exception(f"获取配置项失败: {e}")
    
    @staticmethod
    def get_windcodes_by_config_and_report(config_id, report_type):
        """
        根据配置ID和报表类型获取WindCode列表
        
        Args:
            config_id: 配置ID
            report_type: 报表类型
            
        Returns:
            list: WindCode列表
        """
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            get_windcodes_sql = """
            SELECT DISTINCT WindCode, IndicatorName, IndicatorCode, Unit, Description
            FROM FinancialWindCodeMappings
            WHERE ConfigId = ? AND ReportType = ? AND IsActive = 1
            ORDER BY WindCode
            """
            
            cursor.execute(get_windcodes_sql, (config_id, report_type))
            rows = cursor.fetchall()
            
            windcodes = []
            for row in rows:
                windcode_info = {
                    'windCode': row[0],
                    'indicatorName': row[1],
                    'indicatorCode': row[2],
                    'unit': row[3],
                    'description': row[4]
                }
                windcodes.append(windcode_info)
            
            conn.close()
            return windcodes
            
        except Exception as e:
            raise Exception(f"获取WindCode列表失败: {e}")
    
    @staticmethod
    def list_configs(page=1, page_size=20):
        """
        获取配置列表
        
        Args:
            page: 页码
            page_size: 每页大小
            
        Returns:
            dict: 包含配置列表和分页信息
        """
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            # 获取总数
            count_sql = "SELECT COUNT(*) FROM FinancialConfigs WHERE IsActive = 1"
            cursor.execute(count_sql)
            total_count = cursor.fetchone()[0]
            
            # 获取分页数据
            offset = (page - 1) * page_size
            list_sql = """
            SELECT ConfigId, ConfigName, ConfigType, Description, CreatedAt, CreatedBy
            FROM FinancialConfigs
            WHERE IsActive = 1
            ORDER BY CreatedAt DESC
            OFFSET ? ROWS FETCH NEXT ? ROWS ONLY
            """
            
            cursor.execute(list_sql, (offset, page_size))
            rows = cursor.fetchall()
            
            configs = []
            for row in rows:
                config = {
                    'configId': row[0],
                    'configName': row[1],
                    'configType': row[2],
                    'description': row[3],
                    'createdAt': row[4].isoformat() if row[4] else None,
                    'createdBy': row[5]
                }
                configs.append(config)
            
            conn.close()
            
            return {
                'configs': configs,
                'pagination': {
                    'page': page,
                    'pageSize': page_size,
                    'totalCount': total_count,
                    'totalPages': (total_count + page_size - 1) // page_size
                }
            }
            
        except Exception as e:
            raise Exception(f"获取配置列表失败: {e}")