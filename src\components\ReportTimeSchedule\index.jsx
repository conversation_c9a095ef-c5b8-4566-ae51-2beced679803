import React, { useState, useEffect } from 'react';
import { Table, Card, Spin, message, Button, Space } from 'antd';
import { ArrowLeftOutlined } from '@ant-design/icons';

// 根据日期获取季度标签
const getQuarterLabel = (dateStr) => {
  if (!dateStr) return '';
  
  // 处理GMT格式的日期字符串
  let date;
  if (dateStr.includes('GMT')) {
    date = new Date(dateStr);
  } else {
    date = new Date(dateStr);
  }
  
  const month = date.getMonth() + 1; // getMonth()返回0-11
  const day = date.getDate();
  
  // 根据月日组合判断季度
  if (month === 3 && day === 31) {
    return '一季度';
  } else if (month === 6 && day === 30) {
    return '半年报';
  } else if (month === 9 && day === 30) {
    return '三季度';
  } else if (month === 12 && day === 31) {
    return '年度报告';
  } else {
    return `${month}月${day}日`;
  }
};

// 根据日期获取对应的季度代码（用于财务报表检索）
const getQuarterCode = (dateStr) => {
  if (!dateStr) return 'Q4';
  
  let date;
  if (dateStr.includes('GMT')) {
    date = new Date(dateStr);
  } else {
    date = new Date(dateStr);
  }
  
  const month = date.getMonth() + 1;
  const day = date.getDate();
  
  // 根据月日组合判断季度
  if (month === 3 && day === 31) {
    return 'Q1';
  } else if (month === 6 && day === 30) {
    return 'Q2';
  } else if (month === 9 && day === 30) {
    return 'Q3';
  } else if (month === 12 && day === 31) {
    return 'Q4';
  } else {
    return 'Q4'; // 默认返回Q4
  }
};

// 格式化日期为YYYY-MM-DD格式
const formatDateKey = (dateStr) => {
  if (!dateStr) return '';
  
  let date;
  if (dateStr.includes('GMT')) {
    date = new Date(dateStr);
  } else {
    date = new Date(dateStr);
  }
  
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  
  return `${year}-${month}-${day}`;
};

// 获取年份
const getYear = (dateStr) => {
  if (!dateStr) return '';
  
  let date;
  if (dateStr.includes('GMT')) {
    date = new Date(dateStr);
  } else {
    date = new Date(dateStr);
  }
  
  return date.getFullYear();
};

// 格式化显示的日期值，去掉时分秒
const formatDisplayDate = (dateValue) => {
  if (!dateValue) return '-';
  
  // 如果值包含时间部分，去掉时分秒
  if (dateValue.includes(' 00:00:00')) {
    return dateValue.split(' 00:00:00')[0];
  }
  
  // 如果是完整的日期时间格式，解析后只取日期部分
  try {
    const date = new Date(dateValue);
    if (!isNaN(date.getTime())) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    }
  } catch (e) {
    // 如果解析失败，返回原值
  }
  
  return dateValue;
};

const ReportTimeSchedule = ({ navigateToPage, params = {} }) => {
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState([]);
  const [tableColumns, setTableColumns] = useState([]);
  const [filteredData, setFilteredData] = useState([]);
  const [tableHeight, setTableHeight] = useState(400);

  // 计算表格高度
  useEffect(() => {
    const calculateTableHeight = () => {
      const windowHeight = window.innerHeight;
      // 减去页面顶部padding(20px)、卡片标题和按钮区域(约80px)、底部padding(20px)、其他边距(约60px)
      const availableHeight = windowHeight - 180;
      setTableHeight(Math.max(400, availableHeight)); // 最小高度400px
    };

    calculateTableHeight();
    
    // 监听窗口大小变化
    window.addEventListener('resize', calculateTableHeight);
    
    return () => {
      window.removeEventListener('resize', calculateTableHeight);
    };
  }, []);

  useEffect(() => {
    fetchReportTimes();
  }, []);

  // 当接收到参数时，自动执行筛选
  useEffect(() => {
    if (params.companies && data.length > 0) {
      applyFilters(params);
    }
  }, [params, data]);

  const fetchReportTimes = async () => {
    setLoading(true);
    try {
      const response = await fetch('./api/report-times');
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const result = await response.json();
      
      if (result.error) {
        throw new Error(result.error);
      }
      
      processData(result);
    } catch (error) {
      console.error('获取报告时间数据失败:', error);
      message.error('获取数据失败: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const processData = (rawData) => {
    // 按公司分组数据
    const groupedData = {};
    const dateSet = new Set();

    rawData.forEach(item => {
      const { CompanyName, date, OriValue } = item;
      
      if (!groupedData[CompanyName]) {
        groupedData[CompanyName] = {
          companyName: CompanyName,
        };
      }
      
      if (date) {
        const formattedDate = formatDateKey(date);
        dateSet.add(formattedDate);
        groupedData[CompanyName][formattedDate] = OriValue;
      }
    });

    // 找出拥有最多期数的公司，以此为准确定所有列
    const allDates = Array.from(dateSet);
    
    // 按日期倒序排序（最新的在前面）
    const sortedDates = allDates.sort((a, b) => {
      // 将日期字符串转换为Date对象进行比较
      return new Date(b).getTime() - new Date(a).getTime();
    });
    
    // 构建表格列
    const columns = [
      {
        title: '公司',
        dataIndex: 'companyName',
        key: 'companyName',
        fixed: 'left',
        width: 200,
      }
    ];

    // 为每个日期创建列（使用拥有最多期数的公司的所有期数）
    sortedDates.forEach(date => {
      const year = getYear(date);
      const quarterLabel = getQuarterLabel(date);
      
      columns.push({
        title: (
          <div style={{ textAlign: 'center' }}>
            <div>{year}年{quarterLabel}</div>
           
          </div>
        ),
        dataIndex: date,
        key: date,
        width: 120,
        align: 'center',
        render: (value, record) => {
          const displayDate = formatDisplayDate(value);
          
          // 如果有值，则显示为可点击的链接
          if (value && value !== '-') {
            return (
              <Button
                type="link"
                size="small"
                style={{ 
                  padding: 0, 
                  height: 'auto',
                  color: '#1890ff',
                  textDecoration: 'underline'
                }}
                onClick={() => handleReportTimeClick(record.companyName, date)}
                title={`查看${record.companyName}${getYear(date)}年${getQuarterLabel(date)}财务报表`}
              >
                {displayDate}
              </Button>
            );
          }
          
          return displayDate;
        }
      });
    });

    // 为所有公司补全缺失的期数列（确保所有公司都有相同的列结构）
    const processedData = Object.values(groupedData).map(company => {
      const completeCompany = { ...company };
      sortedDates.forEach(date => {
        if (!completeCompany[date]) {
          completeCompany[date] = null; // 缺失的期数设为null，显示时会变为'-'
        }
      });
      return completeCompany;
    });

    setTableColumns(columns);
    setData(processedData);
    setFilteredData(processedData); // 初始显示所有数据
  };

  // 应用筛选条件
  const applyFilters = (filterParams) => {
    let filtered = [...data];

    // 按公司筛选
    if (filterParams.companies && filterParams.companies.length > 0) {
      filtered = filtered.filter(item => 
        filterParams.companies.includes(item.companyName)
      );
      
      // 显示筛选提示信息
      message.success(`已筛选显示 ${filterParams.companies.length} 家公司的报告时间`);
    }

    setFilteredData(filtered);
  };

  // 清除筛选条件
  const clearFilters = () => {
    setFilteredData(data);
    message.info('已清除筛选条件，显示所有公司');
  };

  // 处理点击报告时间链接
  const handleReportTimeClick = (companyName, date) => {
    if (!date || !companyName) return;
    
    const year = getYear(date);
    const quarter = getQuarterCode(date);
    
    // 传递参数到财务报表页面
    const params = {
      companyName: companyName,
      year: year.toString(),
      quarter: quarter,
      fromPage: 'report-schedule',
      autoSearch: true // 标记需要自动检索
    };
    
    if (navigateToPage) {
      navigateToPage('reports', params);
    }
  };

  // 获取当前筛选状态的标题
  const getCardTitle = () => {
    const baseTitle = '定期报告公布时间';
    if (params.companies && params.companies.length > 0) {
      return `${baseTitle} (已筛选 ${params.companies.length} 家公司)`;
    }
    return baseTitle;
  };

  return (
    <div style={{ 
      padding: '0',
      paddingBottom: '0px',
      height: 'calc(100vh - 80px)',
      boxSizing: 'border-box', 
    }}>
      <Card 
        title={getCardTitle()}
        style={{ 
          marginBottom: 0,
          height: `100%`,
          display: 'flex',
          flexDirection: 'column'
        }}
        bodyStyle={{
          flex: 1,
          overflow: 'hidden', 
        }}
        extra={
          <Space>
            {params.companies && params.companies.length > 0 && (
              <Button onClick={clearFilters} size="small">
                清除筛选
              </Button>
            )}
            {params.fromPage && (
              <Button 
                type="link" 
                icon={<ArrowLeftOutlined />}
                onClick={() => navigateToPage && navigateToPage(params.fromPage)}
                size="small"
              >
                返回{params.fromPage === 'analysis' ? '财务分析' : params.fromPage === 'charts' ? '图表分析' : '上一页'}
              </Button>
            )}
          </Space>
        }
      >
        <Spin spinning={loading}>
          <Table
            columns={tableColumns}
            dataSource={filteredData}
            rowKey="companyName"
            scroll={{ 
              x: 'max-content',
              y: tableHeight - 55
            }}
            pagination={false}
            size="small"
            style={{
              height: tableHeight   // 加上表头高度
            }}
          />
        </Spin>
      </Card>
    </div>
  );
};

export default ReportTimeSchedule; 