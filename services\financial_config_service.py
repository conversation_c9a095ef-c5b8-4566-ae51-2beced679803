#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
财务配置项服务类
处理配置项相关的业务逻辑
"""

import json
from datetime import datetime
from typing import List, Dict, Any, Optional
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from models.financial_config_model import FinancialConfigModel
from excel_processor import ExcelFinancialProcessor
from financial_report_config_generator import FinancialReportConfigGenerator
from config.database_config import get_db_connection

class FinancialConfigService:
    def __init__(self):
        self.config_model = FinancialConfigModel
    
    def initialize_tables(self):
        """
        初始化配置表
        
        Returns:
            bool: 是否成功
        """
        try:
            return self.config_model.create_tables()
        except Exception as e:
            raise Exception(f"初始化配置表失败: {e}")
    
    def generate_config_from_excel(self, excel_path: str, config_name: str, 
                                 description: str = None, created_by: str = None) -> int:
        """
        从Excel文件生成配置并保存到数据库
        
        Args:
            excel_path: Excel文件路径
            config_name: 配置名称
            description: 配置描述
            created_by: 创建者
            
        Returns:
            int: 配置ID
        """
        try:
            # 1. 处理Excel文件
            processor = ExcelFinancialProcessor(excel_path)
            excel_results = processor.process()
            
            # 2. 生成配置
            generator = FinancialReportConfigGenerator()
            config_data = generator.generate_config(excel_results)
            
            # 3. 保存到数据库
            config_id = self.config_model.save_config(
                config_name=config_name,
                config_data=config_data,
                description=description,
                created_by=created_by
            )
            
            return config_id
            
        except Exception as e:
            raise Exception(f"从Excel生成配置失败: {e}")
    
    def get_three_reports_config(self, config_id: int) -> Dict[str, Any]:
        """
        获取三表配置数据
        
        Args:
            config_id: 配置ID
            
        Returns:
            dict: 三表配置数据
        """
        try:
            # 获取配置基本信息
            config = self.config_model.get_config_by_id(config_id)
            if not config:
                raise Exception(f"配置不存在: {config_id}")
            
            # 获取三表配置项
            report_types = ['资产负债表', '利润表', '现金流量表']
            three_reports = {}
            
            for report_type in report_types:
                # 获取配置项
                items = self.config_model.get_config_items_by_report_type(config_id, report_type)
                
                # 获取WindCode列表
                windcodes = self.config_model.get_windcodes_by_config_and_report(config_id, report_type)
                
                three_reports[report_type] = {
                    'reportType': report_type,
                    'items': items,
                    'windCodes': [wc['windCode'] for wc in windcodes],
                    'windCodeDetails': windcodes,
                    'summary': {
                        'totalItems': len(items),
                        'windCodeCount': len(windcodes),
                        'hasWindCodeItems': len([item for item in items if item['hasWindCode']])
                    }
                }
            
            return {
                'configId': config_id,
                'configName': config['configName'],
                'description': config['description'],
                'createdAt': config['createdAt'],
                'createdBy': config['createdBy'],
                'reports': three_reports,
                'summary': {
                    'totalReports': len(three_reports),
                    'totalItems': sum(report['summary']['totalItems'] for report in three_reports.values()),
                    'totalWindCodes': sum(report['summary']['windCodeCount'] for report in three_reports.values())
                }
            }
            
        except Exception as e:
            raise Exception(f"获取三表配置失败: {e}")
    
    def get_report_config_with_values(self, config_id: int, report_type: str, 
                                    company_id: str = None, report_date: str = None) -> Dict[str, Any]:
        """
        获取报表配置并填充数值
        
        Args:
            config_id: 配置ID
            report_type: 报表类型
            company_id: 公司ID
            report_date: 报告日期
            
        Returns:
            dict: 包含数值的报表配置
        """
        try:
            # 获取配置项
            items = self.config_model.get_config_items_by_report_type(config_id, report_type)
            windcodes = self.config_model.get_windcodes_by_config_and_report(config_id, report_type)
            
            # 获取WindCode列表
            windcode_list = [wc['windCode'] for wc in windcodes if wc['windCode']]
            
            # 查询数值数据
            values_data = {}
            if windcode_list and company_id:
                values_data = self._get_metric_values(windcode_list, company_id, report_date)
            
            # 填充数值到配置项
            enriched_items = []
            for item in items:
                enriched_item = item.copy()
                
                if item['windCode'] and item['windCode'] in values_data:
                    value_info = values_data[item['windCode']]
                    enriched_item.update({
                        'currentValue': value_info.get('value'),
                        'formattedValue': value_info.get('formatted_value'),
                        'reportDate': value_info.get('report_date'),
                        'hasValue': True
                    })
                else:
                    enriched_item.update({
                        'currentValue': None,
                        'formattedValue': '',
                        'reportDate': report_date,
                        'hasValue': False
                    })
                
                enriched_items.append(enriched_item)
            
            return {
                'configId': config_id,
                'reportType': report_type,
                'companyId': company_id,
                'reportDate': report_date,
                'items': enriched_items,
                'windCodes': windcode_list,
                'summary': {
                    'totalItems': len(enriched_items),
                    'itemsWithValues': len([item for item in enriched_items if item['hasValue']]),
                    'windCodeCount': len(windcode_list)
                }
            }
            
        except Exception as e:
            raise Exception(f"获取报表配置和数值失败: {e}")
    
    def _get_metric_values(self, windcodes: List[str], company_id: str, 
                          report_date: str = None) -> Dict[str, Dict[str, Any]]:
        """
        获取指标数值
        
        Args:
            windcodes: WindCode列表
            company_id: 公司ID
            report_date: 报告日期
            
        Returns:
            dict: WindCode到数值的映射
        """
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            # 构建查询条件
            windcode_placeholders = ','.join(['?' for _ in windcodes])
            
            query = f"""
                SELECT 
                    mv.WindCode,
                    mv.CalculatedValue AS value,
                    mv.ReportDate AS report_date,
                    mv.TickerSymbol AS company_id
                FROM MetricValues mv
                WHERE mv.WindCode IN ({windcode_placeholders})
                AND mv.TickerSymbol = ?
            """
            
            params = windcodes + [company_id]
            
            # 添加报告日期条件
            if report_date:
                query += " AND mv.ReportDate = ?"
                params.append(report_date)
            else:
                # 获取最新数据
                query += """
                AND mv.ReportDate = (
                    SELECT MAX(ReportDate) 
                    FROM MetricValues mv2 
                    WHERE mv2.WindCode = mv.WindCode 
                    AND mv2.TickerSymbol = mv.TickerSymbol
                )
                """
            
            cursor.execute(query, params)
            rows = cursor.fetchall()
            
            values_data = {}
            for row in rows:
                windcode = row[0]
                value = row[1]
                report_date = row[2]
                
                # 格式化金额
                formatted_value = ""
                if value is not None:
                    try:
                        formatted_value = f"{float(value):,.2f}"
                    except (ValueError, TypeError):
                        formatted_value = str(value)
                
                values_data[windcode] = {
                    'value': value,
                    'formatted_value': formatted_value,
                    'report_date': report_date.isoformat() if report_date else None
                }
            
            conn.close()
            return values_data
            
        except Exception as e:
            raise Exception(f"获取指标数值失败: {e}")
    
    def list_configs(self, page: int = 1, page_size: int = 20) -> Dict[str, Any]:
        """
        获取配置列表
        
        Args:
            page: 页码
            page_size: 每页大小
            
        Returns:
            dict: 配置列表和分页信息
        """
        try:
            return self.config_model.list_configs(page, page_size)
        except Exception as e:
            raise Exception(f"获取配置列表失败: {e}")
    
    def get_config_summary(self, config_id: int) -> Dict[str, Any]:
        """
        获取配置摘要信息
        
        Args:
            config_id: 配置ID
            
        Returns:
            dict: 配置摘要
        """
        try:
            config = self.config_model.get_config_by_id(config_id)
            if not config:
                raise Exception(f"配置不存在: {config_id}")
            
            # 统计各报表类型的配置项数量
            report_types = ['资产负债表', '利润表', '现金流量表']
            report_stats = {}
            
            total_items = 0
            total_windcodes = 0
            
            for report_type in report_types:
                items = self.config_model.get_config_items_by_report_type(config_id, report_type)
                windcodes = self.config_model.get_windcodes_by_config_and_report(config_id, report_type)
                
                items_with_windcode = len([item for item in items if item['hasWindCode']])
                
                report_stats[report_type] = {
                    'totalItems': len(items),
                    'itemsWithWindCode': items_with_windcode,
                    'windCodeCount': len(windcodes),
                    'matchRate': (items_with_windcode / len(items) * 100) if items else 0
                }
                
                total_items += len(items)
                total_windcodes += len(windcodes)
            
            return {
                'configId': config_id,
                'configName': config['configName'],
                'description': config['description'],
                'createdAt': config['createdAt'],
                'createdBy': config['createdBy'],
                'reportStats': report_stats,
                'overallStats': {
                    'totalItems': total_items,
                    'totalWindCodes': total_windcodes,
                    'totalReports': len(report_types)
                }
            }
            
        except Exception as e:
            raise Exception(f"获取配置摘要失败: {e}")
    
    def delete_config(self, config_id: int) -> bool:
        """
        删除配置（软删除）
        
        Args:
            config_id: 配置ID
            
        Returns:
            bool: 是否成功
        """
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            # 软删除主配置
            update_config_sql = """
            UPDATE FinancialConfigs 
            SET IsActive = 0, UpdatedAt = GETDATE() 
            WHERE ConfigId = ?
            """
            
            # 软删除配置项
            update_items_sql = """
            UPDATE FinancialConfigItems 
            SET IsActive = 0 
            WHERE ConfigId = ?
            """
            
            # 软删除WindCode映射
            update_mappings_sql = """
            UPDATE FinancialWindCodeMappings 
            SET IsActive = 0 
            WHERE ConfigId = ?
            """
            
            cursor.execute(update_config_sql, (config_id,))
            cursor.execute(update_items_sql, (config_id,))
            cursor.execute(update_mappings_sql, (config_id,))
            
            conn.commit()
            conn.close()
            
            return True
            
        except Exception as e:
            raise Exception(f"删除配置失败: {e}")