from flask import Blueprint, jsonify, request, current_app
from ..services.company_service import CompanyService
from ..utils.error_handler import handle_exception

bp = Blueprint('companies', __name__, url_prefix='/api/companies')

@bp.route('', methods=['GET'])
def get_companies():
    """获取公司列表"""
    try:
        companies = CompanyService.get_companies()
        if isinstance(companies, dict) and 'error' in companies:
            current_app.logger.error(f"Service error getting companies: {companies['error']}")
            return jsonify(companies), 500
        return jsonify(companies)
    except Exception as e:
        error_response, status_code = handle_exception(e, "获取公司列表")
        return jsonify(error_response), status_code
