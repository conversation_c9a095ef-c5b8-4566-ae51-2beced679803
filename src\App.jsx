import React, { useState } from 'react';
import { Menu } from 'antd';
import FinancialAnalysis from './components/FinancialAnalysis';
import FinancialReports from './components/FinancialReports'; 
import ChartAnalysis from './components/ChartAnalysis';
import ReportTimeSchedule from './components/ReportTimeSchedule';
import 'antd/dist/reset.css';

function App() {
  const [currentPage, setCurrentPage] = useState('analysis');
  const [pageParams, setPageParams] = useState({});

  const menuItems = [
    {
      key: 'analysis',
      label: '财务分析'
    },
    {
      key: 'reports',
      label: '财务报表'
    },
    {
      key: 'charts',
      label: '图表分析'
    },
    {
      key: 'report-schedule',
      label: '定期报告公布时间'
    },
   
  ];

  // 导航到指定页面并传递参数
  const navigateToPage = (page, params = {}) => {
    setCurrentPage(page);
    setPageParams(params);
  };

  const renderContent = () => {
    switch (currentPage) {
      case 'analysis':
        return <FinancialAnalysis navigateToPage={navigateToPage} params={pageParams} />;
      case 'reports':
        return <FinancialReports navigateToPage={navigateToPage} params={pageParams} />;
      case 'charts':
        return <ChartAnalysis navigateToPage={navigateToPage} params={pageParams} />;
      case 'report-schedule':
        return <ReportTimeSchedule navigateToPage={navigateToPage} params={pageParams} />;
      default:
        return <FinancialAnalysis navigateToPage={navigateToPage} params={pageParams} />;
    }
  };

  return (
    <div className="app">
      <Menu
        mode="horizontal"
        selectedKeys={[currentPage]}
        items={menuItems}
        onClick={({ key }) => setCurrentPage(key)}
        style={{ marginBottom: 0 }}
      />
      {renderContent()}
    </div>
  );
}

export default App;
