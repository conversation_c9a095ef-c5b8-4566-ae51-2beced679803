from concurrent.futures import ThreadPoolExecutor
from config import Config
from crawler.finance_crawler import fetch_data
from database.db_handler import DatabaseHandler
from logging_config import setup_logging

logger = setup_logging()


def process_company(company, config):
    """单个公司的处理流程（线程安全版本）"""
    stock_code = company.stock_code
    try:
        # 每个线程使用独立的数据库连接
        with DatabaseHandler(config) as db_handler:
            logger.info(f"启动 {stock_code} 数据处理线程")

            # 爬取数据
            report = fetch_data(stock_code)
            if not report:
                logger.warning(f"[{stock_code}] 无有效数据")
                return

            # 数据加工
            report["company_id"] = company.company_id
            logger.info(f"[{stock_code}] 获取到 {len(report)} 条记录")

            # 存储数据
            db_handler.store_append_data(report)
            logger.info(f"[{stock_code}] 存储成功")

    except Exception as e:
        logger.error(f"[{stock_code}] 处理失败: {str(e)}", exc_info=True)


def main():
    try:
        # 主线程获取数据
        with DatabaseHandler() as db:
            companies = db.get_active_companies()
            logger.info(f"总需处理公司数: {len(companies)}")

            # 动态调整线程数（建议5-15之间）
            max_workers = 1  # min(10, len(companies))
            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                # 提交所有任务
                futures = [executor.submit(process_company, comp, config)
                           for comp in companies]

                # 实时显示进度
                for future in futures:
                    future.add_done_callback(
                        lambda f: logger.debug("任务完成率: {}/{}".format(
                            len([x for x in futures if x.done()]),
                            len(futures)
                        )))

    except Exception as e:
        logger.error(f"程序初始化失败:{e}", exc_info=True)


if __name__ == "__main__":
    main()
