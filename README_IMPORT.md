# Wind数据导入工具使用说明

## 概述
Wind数据导入工具已经支持参数化配置，可以根据需要灵活设置导入的年份范围和季度。

## 文件说明

### 主要文件
- `import_wind_data.py` - 主程序文件
- `import_config.py` - 配置文件（需要根据需求修改）
- `import_config_examples.py` - 配置示例文件（参考用）

### 配置文件结构
```python
# import_config.py
START_YEAR = 2024      # 开始年份
END_YEAR = 2025        # 结束年份
QUARTERS = [           # 季度列表
    '0331',  # 一季度末 (3月31日)
    '0630',  # 二季度末 (6月30日) 
    '0930',  # 三季度末 (9月30日)
    '1231'   # 四季度末 (12月31日)
]
```

## 使用方法

### 1. 基本使用
1. 修改 `import_config.py` 文件中的参数
2. 运行 `python import_wind_data.py`

### 2. 常见配置场景

#### 场景1: 导入完整数据（推荐）
```python
START_YEAR = 2024
END_YEAR = 2025
QUARTERS = ['0331', '0630', '0930', '1231']
```

#### 场景2: 只导入年报数据
```python
START_YEAR = 2024
END_YEAR = 2025
QUARTERS = ['1231']
```

#### 场景3: 只导入半年报数据
```python
START_YEAR = 2024
END_YEAR = 2025
QUARTERS = ['0630']
```

#### 场景4: 只导入特定年份的数据
```python
START_YEAR = 2024
END_YEAR = 2024
QUARTERS = ['0331', '0630', '0930', '1231']
```

#### 场景5: 测试模式（只导入少量数据）
```python
START_YEAR = 2025
END_YEAR = 2025
QUARTERS = ['0331']
```

## 生成EXE文件

### 使用PyInstaller
```bash
# 安装PyInstaller
pip install pyinstaller

# 生成exe文件
pyinstaller --onefile import_wind_data.py

# 生成带配置文件的exe
pyinstaller --onefile --add-data "import_config.py;." import_wind_data.py
```

### 使用Auto-py-to-exe（图形界面）
1. 安装：`pip install auto-py-to-exe`
2. 运行：`auto-py-to-exe`
3. 选择 `import_wind_data.py` 文件
4. 添加 `import_config.py` 到附加文件
5. 生成exe文件

## 部署说明

### 1. 文件结构
```
windgather/
├── import_wind_data.exe          # 生成的exe文件
├── import_config.py              # 配置文件
├── import_config_examples.py     # 配置示例（可选）
└── README_IMPORT.md              # 说明文档
```

### 2. 修改配置
- 直接编辑 `import_config.py` 文件
- 修改 `START_YEAR`、`END_YEAR`、`QUARTERS` 参数
- 保存文件后运行exe

### 3. 运行程序
```bash
# 双击运行
import_wind_data.exe

# 或在命令行运行
./import_wind_data.exe
```

## 注意事项

### 1. 数据量控制
- 年份范围越大，数据量越大，导入时间越长
- 建议先使用小范围测试，确认无误后再导入完整数据

### 2. 网络连接
- 确保Wind终端正常运行
- 确保网络连接稳定

### 3. 数据库权限
- 确保数据库连接正常
- 确保有足够的写入权限

### 4. 错误处理
- 程序会自动跳过已导入的数据
- 如果中途中断，重新运行会继续导入剩余数据

## 故障排除

### 1. 配置文件错误
```
ImportError: cannot import name 'START_YEAR' from 'import_config'
```
- 检查 `import_config.py` 文件是否存在
- 检查配置参数是否正确

### 2. 数据库连接错误
- 检查数据库连接字符串
- 确认数据库服务正常运行

### 3. Wind API错误
- 确认Wind终端已登录
- 检查Wind API权限

## 更新日志

### v2.0 (当前版本)
- ✅ 支持参数化配置
- ✅ 添加配置文件支持
- ✅ 优化错误处理
- ✅ 添加详细日志

### v1.0 (原版本)
- 硬编码年份和季度
- 需要修改代码才能调整导入范围 