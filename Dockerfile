# 构建阶段1
FROM  harbor.shecc.com/baseimages/node18:latest as builder

# 设置工作目录
WORKDIR /app

RUN npm install -g pnpm@9.12.2  --registry https://repository.shecc.com/repository/npm-public/

ENV PNPM_HOME="/pnpm"
ENV PATH="$PNPM_HOME:$PATH"

# 复制 package.json 和 pnpm-lock.yaml
COPY package*.json ./  
# 使用 pnpm 安装依赖
RUN pnpm install --registry https://repository.shecc.com/repository/npm-public/

# 复制所有源代码
COPY . .

# 构建应用
RUN pnpm run build

# 运行阶段
FROM harbor.shecc.com/baseimages/nginx:latest

# 复制构建产物到 Nginx 目录
COPY --from=builder /app/dist /usr/share/nginx/html

# 暴露端口
EXPOSE 80

# Nginx 会自动启动，不需要额外的 CMD
