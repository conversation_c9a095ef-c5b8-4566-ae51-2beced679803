from decimal import Decimal, InvalidOperation
import argparse
import sys
import math
 
from WindPy import w
import uuid
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor
from database.db_handler import DatabaseHandler
from logging_config import setup_logging

logger = setup_logging()


class WindDataImporter:
    def __init__(self, start_year=None, end_year=None, quarters=None):
        """初始化Wind数据导入器
        参数：
            start_year (int): 开始年份，默认为当前年份减2
            end_year (int): 结束年份，默认为当前年份
            quarters (list): 季度列表，默认为['0331', '0630', '0930', '1231']
        步骤：
        1. 创建数据库连接
        2. 启动Wind金融接口
        3. 初始化API调用日志表
        """
        self.db = DatabaseHandler()
        w.start()
        self._init_log_table()
        
        # 设置默认参数
        current_year = datetime.now().year
        self.start_year = start_year if start_year is not None else (current_year - 2)
        self.end_year = end_year if end_year is not None else current_year
        self.quarters = quarters if quarters is not None else ['0331', '0630', '0930', '1231']
        
        logger.info(f"Wind 数据导入器初始化完成 - 年份范围: {self.start_year}-{self.end_year}, 季度: {self.quarters}")

    def _is_none_or_nan(self, value):
        """轻量级方法：检查值是否为None或NaN
        替代pandas的pd.isna，避免引入pandas依赖
        """
        if value is None:
            return True
        if isinstance(value, float) and math.isnan(value):
            return True
        return False

    def _init_log_table(self):
        """初始化API调用日志表
        表结构说明：
        - TickerSymbol: 股票代码 (nvarchar20)
        - RptDate: 报告日期 (datetime2)
        - ID: 唯一标识 (UUID)
        - create_time: 记录创建时间 (datetime)
        """
        create_table_sql = """
        IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='WindApiCallLog')
        CREATE TABLE WindApiCallLog (
            TickerSymbol nvarchar(20) not null,
            RptDate datetime2 not null,
            ID varchar(36) not null PRIMARY KEY,
            create_time datetime not null default getdate()
        )
        """
        with self.db as db:
            cursor = db.connection.cursor()
            cursor.execute(create_table_sql)
            db.connection.commit()

    def _get_metrics_definitions(self):
        """获取需要采集的指标定义
        返回：
            list[tuple] - 启用的指标定义列表，包含指标名称和Wind代码
        查询条件：
            MetricsDefinition表中Enabled=1的有效指标
        """
        select_query = "SELECT Name, WindCode FROM MetricsDefinition WHERE Enabled=1"
        try:
            with self.db as db:  # 使用上下文管理器获取数据库连接
                cursor = db.connection.cursor()
                cursor.execute(select_query)
                results = cursor.fetchall()
                logger.info(f"成功获取 {len(results)} 条启用的指标定义")
                return results
        except Exception as e:
            logger.error(f"获取指标定义失败: {str(e)}", exc_info=True)
            return []

    def _get_report_dates(self, TickerSymbol):
        """动态生成需要查询的报告日期
        参数：
            TickerSymbol (str): 股票代码
        逻辑：
            1. 根据配置的年份范围和季度生成所有日期
            2. 排除已成功查询过的日期
            3. 过滤需要排除的特殊日期
        返回：
            list[str] - 需要处理的日期列表，格式为YYYYMMDD
        """
        all_dates = []

        # 根据配置的年份范围和季度生成日期
        for year in range(self.start_year, self.end_year + 1):
            for quarter in self.quarters:
                date_str = f"{year}{quarter}"
                all_dates.append(date_str)

        # 排除已查询过的日期（针对当前公司）
        check_sql = "SELECT RptDate FROM WindApiCallLog WHERE TickerSymbol=?"
        with self.db as db:
            cursor = db.connection.cursor()
            cursor.execute(check_sql, (TickerSymbol,))
            existing_dates = {d[0].strftime("%Y%m%d")
                              for d in cursor.fetchall()}

        # 过滤需要处理的日期（保留缺少的季度末数据）
        return [
            d for d in all_dates
            if d not in existing_dates
            and not self._should_exclude_date(d)
        ]

    def _should_exclude_date(self, date_str):
        """排除特定日期的过滤规则
        参数：
            date_str (str): 日期字符串 (YYYYMMDD格式)
        示例：
            默认排除2099-12-31（测试数据）
        扩展：
            可通过修改此方法添加新的排除规则
        """
        # 示例：排除2022年年报
        return date_str == "2099129"

    def import_data(self):
        logger.info("开始导入 Wind 数据")
        # 获取公司列表（包含ID和TickerSymbol）
        company_list = self._get_all_companies()
        if not company_list:
            logger.warning("没有需要更新的公司")
            return
        metrics = self._get_metrics_definitions()
        indicator_codes = [metric[1].strip() for metric in metrics]
        fields_str = ",".join(indicator_codes)

        # 获取所有需要处理的日期
        all_dates_set = set()
        for company in company_list:
            symbol = company[1]
            company_dates = self._get_report_dates(symbol)
            all_dates_set.update(company_dates)
        sorted_dates = sorted(list(all_dates_set))
        total_dates = len(sorted_dates)
        processed_dates = 0

        total_tasks = 0
        for date_str in sorted_dates:
            date_companies = []
            for company in company_list:
                symbol = company[1]
                company_dates = self._get_report_dates(symbol)
                if date_str in company_dates:
                    date_companies.append(company)
            date_task_count = len(date_companies)
            total_tasks += date_task_count
            logger.info(f"\n开始处理日期 {date_str}（共 {date_task_count} 家公司）")

            completed_companies = 0
            # 单线程顺序执行每个公司的数据查询
            for company in date_companies:
                task = (company, fields_str, date_str)
                self._query_single_company_year(*task)
                completed_companies += 1
                if completed_companies % 5 == 0 or completed_companies == date_task_count:
                    logger.info(
                        f"{date_str} 进度: {completed_companies}/{date_task_count} ({completed_companies/date_task_count:.1%})")

            processed_dates += 1
            logger.info(
                f"日期 {date_str} 处理完成，总体进度: {processed_dates}/{total_dates} 个日期")

        logger.info(f"Wind 数据导入完成，共处理 {total_tasks} 条记录")

    def _get_all_companies(self):
        """获取需要更新的公司列表
        查询条件：
            1. 公司启用状态为有效 (Enabled=1)
            2. 最近两年无查询记录
        返回：
            list[tuple] - (公司ID, 股票代码) 的元组列表
        """
        select_query = "SELECT CompanyID, TickerSymbol FROM CompanyInfo WHERE IsActive=1 "
        try:
            with self.db as db:  # 使用上下文管理器获取数据库连接
                cursor = db.connection.cursor()
                cursor.execute(select_query)
                results = cursor.fetchall()
                logger.info(f"成功获取 {len(results)} 条启用公司代码")
                return results
        except Exception as e:
            logger.error(f"获取公司代码失败: {str(e)}", exc_info=True)
            return []

    def _get_wind_data(self, TickerSymbol, fields_str, rpt_date, rpt_type):
        result = w.wss(
            TickerSymbol,            # 证券代码
            fields_str,               # 动态拼接的指标列表
            f"unit=1;rptDate={rpt_date};rptType={rpt_type};tradeDate={rpt_date};year={rpt_date[:4]}"
        )
        if result.ErrorCode != 0:
            print(f'error code: {result.ErrorCode}')
            return None

        data = {}
        for i, field in enumerate(result.Fields):
            value = result.Data[i][0]
            if self._is_none_or_nan(value):
                value = None
            data[field] = value
            # 检查所有指标值是否均为空
        if all(v is None for v in data.values()):
            logger.warning(f"全空数据告警: {TickerSymbol} {rpt_date} 所有指标值均为空")
            return None
        return data

    def _query_single_company_year(self, company_info, fields_str, rpt_date):
        """单公司年度数据查询方法（支持指标补全）"""
        company_id, ticker_symbol = company_info
        logger.info(f"开始更新 {ticker_symbol} {rpt_date} 数据")

        try:
            # 新增报告日期有效性检查
            if not self._check_report_exists(ticker_symbol, rpt_date):
                logger.warning(f"跳过未发布报告: {ticker_symbol} {rpt_date}")
                return None

            # 每个线程使用独立数据库连接
            with DatabaseHandler() as local_db:
                cursor = local_db.connection.cursor()
                date_obj = datetime.strptime(rpt_date, '%Y%m%d')

                # 保持原有逻辑不变
                self._delete_existing_metrics(
                    cursor, ticker_symbol, date_obj, company_id)
                wind_data = self._get_wind_data(
                    ticker_symbol, fields_str, rpt_date, 1)
                yoy_data = self._get_yoy_data(
                    ticker_symbol, fields_str, rpt_date)
                self._insert_metrics_data(
                    cursor, wind_data, yoy_data, date_obj, ticker_symbol, company_id)
                
                # 记录API调用日志
                self._log_api_call(cursor, ticker_symbol, date_obj)
                
                # 提交事务
                local_db.connection.commit()

        except Exception as e:
            logger.error(f"数据处理失败: {str(e)}", exc_info=True)

    def _has_existing_log(self, cursor, symbol, date_obj):
        """检查是否存在API调用记录"""
        cursor.execute(
            "SELECT 1 FROM WindApiCallLog WHERE TickerSymbol=? AND RptDate=?",
            (symbol, date_obj)
        )
        return cursor.fetchone() is not None

    def _supplement_metrics(self, cursor, wind_data, yoy_data, date_obj, company_id):
        """指标补全核心逻辑"""
        if not wind_data:
            return

        # 获取已存在指标
        cursor.execute(
            "SELECT DISTINCT WindCode FROM WindMetrics WHERE CompanyID=? AND Date=?",
            (company_id, date_obj)
        )
        existing_codes = {row[0] for row in cursor.fetchall()}

        # 过滤新指标并插入
        new_data = {code: wind_data[code]
                    for code in wind_data if code not in existing_codes}
        if new_data:
            self._insert_metrics_data(
                cursor, new_data, yoy_data, date_obj, "", company_id)
            logger.info(f"成功补全 {len(new_data)} 个新指标")

    def _delete_existing_metrics(self, cursor, symbol, date, company_id):
        """删除已有指标数据"""
        cursor.execute(
            "DELETE FROM WindMetrics WHERE TickerSymbol=? AND Date=? and CompanyId=?",
            (symbol, date, company_id)
        )

    def _get_yoy_data(self, symbol, fields, rpt_date):
        """获取同比数据"""

        last_year = str(int(rpt_date[:4]) - 1) + rpt_date[4:]
        return self._get_wind_data(symbol, fields, last_year, 3) or {}

    def _safe_convert_decimal(self, value, precision=(18, 6)):
        """
        安全转换为Decimal并验证精度范围
        :param value: 输入值
        :param precision: 元组 (总位数, 小数位数)
        :return: (转换后的Decimal, 是否有效)
        """
        if value is None:
            return None, True  # 根据数据库字段是否允许NULL决定

        try:
            # 先转换为Decimal类型
            dec_value = Decimal(str(value))
        except (TypeError, InvalidOperation):
            return None, False
        return dec_value, True
        # 转换原始值为全格式字符串

    def convert_large_number(self, value):
        if value is None:
            return None
        try:
            # 科学计数法字符串转Decimal再转全格式字符串
            if isinstance(value, str) and 'e' in value.lower():
                d = Decimal(value.lower().replace('e', 'E'))
                return format(d, 'f').rstrip('0').rstrip('.')  # 保留完整格式
            # 数值类型直接转字符串会带科学计数，需通过Decimal转换
            elif isinstance(value, (float, int)):
                return format(Decimal(str(value)), 'f').rstrip('0').rstrip('.')
            # 普通字符串直接返回
            else:
                return str(value)
        except:
            return str(value)[:500]  # 异常情况保留原始截断

    def _insert_metrics_data(self, cursor, wind_data, yoy_data, date_obj, symbol, company_id, skip_invalid=True):
        """带数据验证的插入方法"""
        invalid_rows = []
        valid_data = []

        for code in wind_data.keys():
            # 转换Value字段
            raw_value = wind_data.get(code)
            value_dec, is_value_valid = self._safe_convert_decimal(
                raw_value, (18, 6))

            # 转换YoYValue字段
            raw_yoy = yoy_data.get(code)
            yoy_dec, is_yoy_valid = self._safe_convert_decimal(
                raw_yoy, (18, 6))

            # 验证结果判断
            if not all([is_value_valid, is_yoy_valid]):
                invalid_rows.append({
                    "wind_code": code,
                    "value": value_dec,
                    "orivalue": raw_value,
                    "yoy_value": yoy_dec,
                    "reason": f"Value valid: {is_value_valid}, YoY valid: {is_yoy_valid}"
                })
                if not skip_invalid:
                    raise ValueError(f"Invalid data for code: {code}")

            ori_value_str = self.convert_large_number(raw_value)
            valid_data.append((
                str(uuid.uuid4()),
                code,
                value_dec,          # 使用转换后的Decimal类型
                date_obj,
                yoy_dec,            # 使用转换后的Decimal类型
                datetime.now(),
                symbol,
                company_id,
                ori_value_str  # 数据库字段长度限制
            ))

        # 记录无效数据
        if invalid_rows:
            logger.warning("发现 %d 条无效数据，样例：", len(invalid_rows))
            for row in invalid_rows[:3]:
                logger.warning("WindCode=%s | Value=%s | YoYValue=%s | 原因=%s",
                               row['wind_code'], row['value'], row['yoy_value'], row['reason'])

        # 执行插入
        if valid_data:
            cursor.executemany("""
                INSERT INTO WindMetrics 
                (ID, WindCode, Value, Date, YoYValue, CreateDate, TickerSymbol, CompanyID,OriValue)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?,?)
            """, valid_data)
            logger.info("成功插入 %d 条有效数据", len(valid_data))
        else:
            logger.warning("没有有效数据需要插入")

    def _log_api_call(self, cursor, symbol, date_obj):
        """记录API调用日志"""
        cursor.execute("""
            INSERT INTO WindApiCallLog (TickerSymbol, RptDate, ID, create_time)
            VALUES (?, ?, ?, ?)
        """, (
            symbol,
            date_obj,
            str(uuid.uuid4()),
            datetime.now()
        ))

    def _check_report_exists(self, symbol, rpt_date):
        """检查报告是否已发布"""
        result = w.wss(
            symbol,
            "stm_issuingdate",
            f"rptDate={rpt_date};rptType=1"
        )

        # 验证数据有效性
        # 检查Wind API返回是否有效
        if result.ErrorCode != 0 or self._is_none_or_nan(result.Data[0][0]):
            logger.debug(
                f"无效报告日期: {symbol} {rpt_date} | Wind返回: {result.Data[0][0]}")
            return False
            
        # 获取报告发布日期
        issuing_date = result.Data[0][0]
        if not isinstance(issuing_date, datetime):
            issuing_date = datetime.strptime(issuing_date, '%Y-%m-%d')
            
        # 检查报告发布日期是否在报告时间和当前时间之间
        report_date = datetime.strptime(rpt_date, '%Y%m%d')
        current_date = datetime.now()
        
        if not (report_date <= issuing_date <= current_date):
            logger.debug(f"报告发布日期无效: {symbol} {rpt_date} | 发布日期: {issuing_date}")
            return False
            
        return True

    def _delete_unpublished_data(self, symbol, rpt_date):
        """删除未发布报告的相关数据"""
        date_obj = datetime.strptime(rpt_date, '%Y%m%d')
        with DatabaseHandler() as db:
            cursor = db.connection.cursor()
            try:
                # 删除WindMetrics表中的数据
                cursor.execute(
                    "DELETE FROM WindMetrics WHERE TickerSymbol=? AND Date=?",
                    (symbol, date_obj)
                )
                # 删除WindApiCallLog表中的记录
                cursor.execute(
                    "DELETE FROM WindApiCallLog WHERE TickerSymbol=? AND RptDate=?",
                    (symbol, date_obj)
                )
                db.connection.commit()
                logger.info(f"已删除未发布报告 {symbol} {rpt_date} 的相关数据")
            except Exception as e:
                logger.error(f"删除未发布报告数据失败: {str(e)}", exc_info=True)
                db.connection.rollback()
    def check_and_clean_unpublished_reports(self, rpt_date):
        """独立方法：检查并清理未发布报告的数据
        参数：
            rpt_date (str): 报告日期，格式为YYYYMMDD
        """
        # 获取所有公司列表
        company_list = self._get_all_companies()
        if not company_list:
            logger.warning("没有需要检查的公司")
            return

        # 遍历所有公司进行检查
        for company in company_list:
            symbol = company[1]
            try:
                # 检查报告是否已发布
                if not self._check_report_exists(symbol, rpt_date):
                    self._delete_unpublished_data(symbol, rpt_date)  # 调用新的方法来执行删除操作
                    logger.info(f"已清理未发布报告 {symbol} {rpt_date} 的相关数据")
            except Exception as e:
                logger.error(f"检查报告 {symbol} {rpt_date} 失败: {str(e)}", exc_info=True)
def get_smart_defaults():
    """获取智能默认值：当前2季度查询一季度，一季度查询上一年4季度"""
    now = datetime.now()
    current_year = now.year
    current_month = now.month
    
    # 确定当前季度
    if current_month <= 3:
        current_quarter = 1
        quarter_code = '0331'
    elif current_month <= 6:
        current_quarter = 2
        quarter_code = '0630'
    elif current_month <= 9:
        current_quarter = 3
        quarter_code = '0930'
    else:
        current_quarter = 4
        quarter_code = '1231'
    
    # 智能默认逻辑
    if current_quarter == 2:  # 当前是2季度，查询1季度
        target_year = current_year
        target_quarter = '0331'
    elif current_quarter == 1:  # 当前是1季度，查询上一年4季度
        target_year = current_year - 1
        target_quarter = '1231'
    else:  # 其他情况，查询上一个季度
        if current_quarter == 3:
            target_year = current_year
            target_quarter = '0630'
        else:  # current_quarter == 4
            target_year = current_year
            target_quarter = '0930'
    
    return target_year, target_quarter

def parse_arguments():
    """解析命令行参数"""
    # 获取智能默认值
    default_year, default_quarter = get_smart_defaults()
    
    parser = argparse.ArgumentParser(
        description='Wind数据导入工具 - 智能默认值版本',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog=f"""
智能默认值说明:
  当前时间: {datetime.now().strftime('%Y年%m月')}
  智能默认: {default_year}年{default_quarter}季度数据

使用示例:
  # 使用智能默认值（推荐）
  wind_import.exe
  
  # 指定年份和季度
  wind_import.exe --start-year 2024 --end-year 2024 --quarters 0331
  
  # 导入多个季度
  wind_import.exe --start-year 2024 --end-year 2025 --quarters 0331 0630 0930 1231
  
  # 只导入年报数据
  wind_import.exe --start-year 2024 --end-year 2024 --quarters 1231
  
  # 使用配置文件
  wind_import.exe --config-file my_config.py
        """
    )
    
    parser.add_argument(
        '--start-year', 
        type=int, 
        default=default_year,
        help=f'开始年份 (智能默认: {default_year})'
    )
    
    parser.add_argument(
        '--end-year', 
        type=int, 
        default=default_year,
        help=f'结束年份 (智能默认: {default_year})'
    )
    
    parser.add_argument(
        '--quarters', 
        nargs='+', 
        default=[default_quarter],
        choices=['0331', '0630', '0930', '1231'],
        help=f'季度列表 (智能默认: {default_quarter})'
    )
    
    parser.add_argument(
        '--config-file',
        type=str,
        help='配置文件路径 (可选，会覆盖命令行参数)'
    )
    
    parser.add_argument(
        '--auto-mode',
        action='store_true',
        help='自动模式：使用智能默认值，无需手动指定参数'
    )
    
    return parser.parse_args()

def load_config_from_file(config_file):
    """从配置文件加载参数"""
    try:
        import importlib.util
        spec = importlib.util.spec_from_file_location("config", config_file)
        config_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(config_module)
        
        return {
            'start_year': getattr(config_module, 'START_YEAR', 2024),
            'end_year': getattr(config_module, 'END_YEAR', 2025),
            'quarters': getattr(config_module, 'QUARTERS', ['0331', '0630', '0930', '1231'])
        }
    except Exception as e:
        logger.error(f"加载配置文件失败: {e}")
        return None

if __name__ == "__main__":
    # 解析命令行参数
    args = parse_arguments()
    
    # 如果指定了配置文件，优先使用配置文件
    if args.config_file:
        config = load_config_from_file(args.config_file)
        if config:
            start_year = config['start_year']
            end_year = config['end_year']
            quarters = config['quarters']
            print(f"📁 从配置文件加载参数: {args.config_file}")
        else:
            print("❌ 配置文件加载失败，使用命令行参数")
            start_year = args.start_year
            end_year = args.end_year
            quarters = args.quarters
    else:
        start_year = args.start_year
        end_year = args.end_year
        quarters = args.quarters
    
    # 验证参数
    if start_year > end_year:
        print("❌ 错误: 开始年份不能大于结束年份")
        sys.exit(1)
    
    if not quarters:
        print("❌ 错误: 季度列表不能为空")
        sys.exit(1)
    
    # 显示配置信息
    print("=" * 60)
    print("Wind数据导入工具")
    print("=" * 60)
    print(f"📅 数据导入配置:")
    print(f"   开始年份: {start_year}")
    print(f"   结束年份: {end_year}")
    print(f"   季度列表: {quarters}")
    print(f"   预计数据量: {(end_year - start_year + 1) * len(quarters)} 个报告期")
    print("-" * 60)
    
    # 创建导入器实例
    wind = WindDataImporter(
        start_year=start_year,
        end_year=end_year,
        quarters=quarters
    )
    
    # 执行数据导入
    wind.import_data()
