# chart_config_routes.py
from flask import Blueprint, request, jsonify
from ..services.chart_config_service import ChartConfigService
from ..utils.error_handler import handle_exception, ValidationError

chart_config_bp = Blueprint('chart_config', __name__, url_prefix='/api')
chart_config_service = ChartConfigService()

@chart_config_bp.route('/chart-data/dashboard', methods=['POST'])
def get_dashboard_chart_data():
    """
    获取仪表盘所有图表数据
    POST请求体参数：
    - report_date: 报告日期 (必需)
    - company_ids: 公司ID列表 (必需)
    
    返回结构与chart_config.py一致，每条数据包含年份信息
    所有数据都统一放在indicators数组中，每个记录包含date和year字段
    """
    try:
        # 获取POST请求体数据
        request_data = request.get_json()
        if not request_data:
            raise ValidationError('请求体不能为空')
        
        report_date = request_data.get('report_date')
        if not report_date:
            raise ValidationError('report_date参数是必需的')
        
        company_ids = request_data.get('company_ids')
        if not company_ids:
            raise ValidationError('company_ids参数是必需的')
            
        # 确保company_ids是列表格式
        if isinstance(company_ids, str):
            company_ids = [id.strip() for id in company_ids.split(',') if id.strip()]
        elif not isinstance(company_ids, list):
            raise ValidationError('company_ids必须是列表或逗号分隔的字符串')
            
        if not company_ids:
            raise ValidationError('company_ids不能为空')
        
        data = chart_config_service.get_chart_data_for_dashboard(report_date, company_ids)
        return jsonify({
            'success': True,
            'data': data
        })
    except Exception as e:
        error_response, status_code = handle_exception(e, "获取仪表盘图表数据")
        return jsonify(error_response), status_code 