from logging import config
import requests
import json


def fetch_data(code):
    try:
        params = {
            'paperCode': code,
            'source': 'gjzb',
            'type': 0,
            'page': 1,
            'num': 100
        }
        url = "https://quotes.sina.cn/cn/api/openapi.php/CompanyFinanceService.getFinanceReport2022"
        response = requests.get(url=url, params=params)
        response.raise_for_status()

        # 处理JSONP响应
        json_str = response.text
        return json.loads(json_str)
    except Exception as e:
        print(f"获取数据失败: {e}")
        return None


if __name__ == '__main__':
    # 获取数据
    raw_data = fetch_data()
    if not raw_data:
        exit(1)

    # 提取需要的数据（根据实际返回结构调整）
    finance_data = raw_data.get('result', {}).get('data', [])
    print(finance_data)
