from ..repositories.company_repository import CompanyRepository
from ..repositories.indicator_repository import IndicatorRepository
from ..repositories.metrics_repository import MetricsRepository # Import MetricsRepository

class MetricsService:
    def __init__(self):
        self.company_repository = CompanyRepository()
        self.indicator_repository = IndicatorRepository()
        self.metrics_repository = MetricsRepository() # Instantiate MetricsRepository

    def fetch_all_companies(self):
        return self.company_repository.get_all_companies()

    def fetch_all_indicators(self):
        return self.indicator_repository.get_all_indicators()

    def fetch_metrics(self, ticker_symbol, indicator_name):
        # Fetch metrics data from repository
        return self.metrics_repository.get_company_metrics(ticker_symbol, indicator_name) # Corrected repository name

    def fetch_metrics_for_selection(self, ticker_symbols, indicator_names_en):
        """
        Fetches metrics data for a list of companies and a list of indicators.
        """
        if not isinstance(ticker_symbols, list) or not isinstance(indicator_names_en, list):
            # Or raise an error, or handle as appropriate
            return {"error": "ticker_symbols and indicator_names_en must be lists."} 
            
        return self.metrics_repository.get_metrics_for_selection(ticker_symbols, indicator_names_en)
