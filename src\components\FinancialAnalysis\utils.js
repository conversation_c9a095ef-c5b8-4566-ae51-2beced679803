/**
 * 格式化指标值
 * @param {number|string} value - 要格式化的值
 * @param {string} valueType - 值的类型（%, 亿, 等）
 * @returns {string} 格式化后的值
 */
export const formatMetricValue = (value, valueType) => {
  if (value === undefined) return "-";

  const numValue = Number(value);
  const trimmedType = valueType ? valueType.trim() : "";

  if (trimmedType === "%") {
    return (numValue * 100).toFixed(2) + "%";
  } else if (trimmedType === "亿") {
    return (numValue / 100000000).toLocaleString("zh-CN", {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    });
  } else {
    return numValue.toLocaleString("zh-CN", {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    });
  }
};

/**
 * 生成报告日期
 * @param {number} year - 年份
 * @param {string} quarterValue - 季度值
 * @param {Array} quarters - 季度信息数组
 * @returns {string} 格式化的日期字符串 (YYYY-MM-DD)
 */
export const generateReportDate = (year, quarterValue, quarters) => {
  const quarterInfo = quarters.find((q) => q.value === quarterValue);
  if (year && quarterInfo) {
    return `${year}-${quarterInfo.month}-${quarterInfo.day}`;
  }
  // 默认返回当前年份的年报日期
  const currentYear = new Date().getFullYear();
  return `${currentYear}-12-31`;
};

/**
 * 处理表格数据
 * @param {Object} data - 包含指标值、指标定义和公司信息的数据对象
 * @param {Array} metrics - 指标定义数组
 * @returns {Array} 处理后的表格数据
 */
export const processTableData = (data, metrics) => {
  const companiesMap = new Map(); // 使用 Map 按 company_id 分组

  data.metrics_values.forEach((item) => {
    if (!companiesMap.has(item.company_id)) {
      const companyInfo = data.companies.find((c) => c.id === item.company_id);
      companiesMap.set(item.company_id, {
        company_id: item.company_id,
        company_name: companyInfo ? companyInfo.name : item.company_id,
        key: item.company_id,
        isInternal: companyInfo ? companyInfo.IsInternal : false,
        companyGroup: companyInfo && companyInfo.IsInternal ? "集团内公司" : "集团外公司",
        metric_values: {},
      });
    }
    // 将当前指标的值和评估结果存入对应公司的 metric_values 对象中
    companiesMap.get(item.company_id).metric_values[item.metric_name] = {
      value: item.value,
      evaluation_result: item.EvaluationResult,
      translated_formula: item.TranslatedFormula,
    };
  });

  // 将 Map 转换为数组，作为表格的 dataSource
  const tableData = Array.from(companiesMap.values());

  // 按照内部公司和其他公司分组排序
  tableData.sort((a, b) => {
    // 首先按照是否为内部公司排序（内部公司在前）
    if (a.isInternal !== b.isInternal) {
      return a.isInternal ? -1 : 1;
    }
    // 然后按照公司名称排序
    return (a.company_name || "").localeCompare(b.company_name || "");
  });

  // 计算每个指标的排名
  const rankedData = tableData.map(item => {
    const rankedItem = {...item};
    rankedItem.metric_ranks = {};

    // 对每个指标进行排序并记录排名
    metrics.forEach(metric => {
      const sortedValues = [...tableData]
        .sort((a, b) => {
          const valA = a.metric_values[metric.code]?.value || 0;
          const valB = b.metric_values[metric.code]?.value || 0;
          return valB - valA; // 降序排列
        })
        .map((x, i) => ({
          company_id: x.company_id,
          rank: i + 1
        }));

      const companyRank = sortedValues.find(x => x.company_id === item.company_id);
      rankedItem.metric_ranks[metric.code] = companyRank ? companyRank.rank : '-';
    });

    return rankedItem;
  });

  // 检查是否包含电科数字公司
  const hasDiankeCompany = tableData.some(item => item.company_name === "电科数字");

  // 只有包含电科数字公司时才添加排名行
  if (hasDiankeCompany) {
    const rankRow = {
      company_id: 'rank_row',
      company_name: '',
      companyGroup: '',
      key: 'rank_row',
      isRankRow: true,
      isFixedRow: true,
      metric_values: {},
      metric_ranks: {}
    };

    // 对每个指标计算排名行的值
    metrics.forEach(metric => {
      rankRow.metric_values[metric.code] = {
        value: '排名',
        evaluation_result: '排名'
      };

      // 计算并存储排名（基于所有公司）
      const sorted = [...tableData].sort((a, b) => {
        const valA = a.metric_values[metric.code]?.value || 0;
        const valB = b.metric_values[metric.code]?.value || 0;
        return valB - valA; // 降序排列
      });

      // 存储所有公司的排名
      sorted.forEach((item, index) => {
        if (!rankRow.metric_ranks[item.company_id]) {
          rankRow.metric_ranks[item.company_id] = {};
        }
        rankRow.metric_ranks[item.company_id][metric.code] = index + 1;
      });

      // 获取电科数字公司的排名用于显示
      const diankeCompany = tableData.find(item => item.company_name === "电科数字");
      if (diankeCompany) {
        const diankeIndex = sorted.findIndex(item => item.company_id === diankeCompany.company_id);
        const diankeRank = diankeIndex !== -1 ? diankeIndex + 1 : '-';
        rankRow.metric_values[metric.code].diankeRank = diankeRank;
      }
    });

    // 排名行在最前面
    return [rankRow, ...rankedData];
  }

  // 如果没有电科数字公司，不添加排名行
  return rankedData;
};

/**
 * 根据筛选后的公司重新计算排名
 * @param {Array} tableData - 原始表格数据
 * @param {Array} selectedCompanies - 选中的公司ID列表
 * @param {Array} metrics - 指标列表
 * @returns {Array} 重新计算排名后的表格数据
 */
export const recalculateRanks = (tableData, selectedCompanies, metrics) => {
  if (!tableData || tableData.length <= 1) return tableData;

  // 获取排名行和公司数据行
  const rankRow = tableData.find(item => item.isRankRow);
  const companyRows = tableData.filter(item => !item.isRankRow);

  // 筛选选中的公司
  const filteredCompanies = companyRows.filter(
    company => selectedCompanies.includes(company.company_id)
  );

  // 检查是否选择了电科数字公司
  const diankeCompany = filteredCompanies.find(c => c.company_name === "电科数字");
  const shouldShowRank = !!diankeCompany; // 只有选择电科数字公司时才显示排名

  // 重新计算每个指标的排名（只对选中的公司进行排序）
  metrics.forEach(metric => {
    // 对筛选后的公司按指标值排序
    const sorted = [...filteredCompanies].sort((a, b) => {
      const valA = a.metric_values[metric.code]?.value || 0;
      const valB = b.metric_values[metric.code]?.value || 0;
      return valB - valA; // 降序排列
    });

    // 更新每个公司的排名（基于选中公司的排序）
    sorted.forEach((company, index) => {
      const companyRow = companyRows.find(row => row.company_id === company.company_id);
      if (companyRow) {
        companyRow.metric_ranks[metric.code] = index + 1;
      }
    });

    // 只有选择了电科数字公司时才更新排名行
    if (rankRow && shouldShowRank) {
      // 确保排名行的排名信息对象存在
      if (!rankRow.metric_ranks) {
        rankRow.metric_ranks = {};
      }

      // 更新所有选中公司的排名信息
      sorted.forEach((company, index) => {
        if (!rankRow.metric_ranks[company.company_id]) {
          rankRow.metric_ranks[company.company_id] = {};
        }
        rankRow.metric_ranks[company.company_id][metric.code] = index + 1;
      });

      // 确保排名行的 metric_values 中有该指标的条目
      if (!rankRow.metric_values[metric.code]) {
        rankRow.metric_values[metric.code] = { value: '排名' };
      }

      // 获取电科数字公司的排名
      const diankeIndex = sorted.findIndex(c => c.company_id === diankeCompany.company_id);
      const diankeRank = diankeIndex !== -1 ? diankeIndex + 1 : '-';

      // 存储电科数字公司的排名，用于显示
      rankRow.metric_values[metric.code].diankeRank = diankeRank;
    } else if (rankRow) {
      // 如果没有选择电科数字公司，清除排名信息
      if (!rankRow.metric_values[metric.code]) {
        rankRow.metric_values[metric.code] = { value: '排名' };
      }
      rankRow.metric_values[metric.code].diankeRank = '-';
    }
  });

  // 只有选择了电科数字公司时才显示排名行
  let result = [];
  if (rankRow && shouldShowRank) {
    // 确保排名行的固定属性
    rankRow.isRankRow = true;
    rankRow.isFixedRow = true;
    // 将排名行放在第一位
    result = [rankRow];
  }
  
  // 添加筛选后的公司行
  // 只返回选中的公司，而不是所有公司
  const selectedCompanyRows = companyRows.filter(row => 
    selectedCompanies.includes(row.company_id)
  );
  
  // 返回更新后的表格数据，如果显示排名则排名行在第一位
  return [...result, ...selectedCompanyRows];
};

/**
 * 显示错误信息
 * @param {string} errorMessage - 错误信息
 */
export const displayError = (errorMessage) => {
  const errorContainer = document.getElementById("error-container");
  if (errorContainer) {
    errorContainer.textContent = "发生错误: " + errorMessage;
    errorContainer.style.display = "block";
  }
  console.error("发生错误:", errorMessage); // Keep console log for debugging
};
