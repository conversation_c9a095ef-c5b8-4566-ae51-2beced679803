import { useState, useEffect } from 'react';
import { START_YEAR, FALLBACK_DEFAULT_YEAR, FALLBACK_DEFAULT_QUARTER, defaultQuarters, DEFAULT_COMPANY_IDS, getDefaultYearAndQuarter } from '../utils/constants';

export function useFilterState() {
  const [years, setYears] = useState([]);
  const [quarters] = useState(defaultQuarters);
  const [tempSelectedYear, setTempSelectedYear] = useState(FALLBACK_DEFAULT_YEAR);
  const [tempSelectedQuarter, setTempSelectedQuarter] = useState(FALLBACK_DEFAULT_QUARTER);
  const [tempSelectedCompanies, setTempSelectedCompanies] = useState(DEFAULT_COMPANY_IDS);
  const [isInitialized, setIsInitialized] = useState(false);

  useEffect(() => {
    const initializeDefaults = async () => {
      if (!isInitialized) {
        try {
          // 获取动态默认值
          const { year, quarter } = await getDefaultYearAndQuarter();
          
          // 设置年份范围
          const currentDate = new Date();
          const currentYear = currentDate.getFullYear();
          const currentMonth = currentDate.getMonth() + 1;
          const maxYear = currentMonth < 4 ? currentYear - 1 : currentYear;
          const yearArr = [];
          for (let y = START_YEAR; y <= maxYear; y++) yearArr.push(y);
          
          setYears(yearArr);
          setTempSelectedYear(year);
          setTempSelectedQuarter(quarter);
          setIsInitialized(true);
        } catch (error) {
          console.error('初始化默认值失败:', error);
          // 使用静态默认值
          const currentDate = new Date();
          const currentYear = currentDate.getFullYear();
          const currentMonth = currentDate.getMonth() + 1;
          const maxYear = currentMonth < 4 ? currentYear - 1 : currentYear;
          const yearArr = [];
          for (let y = START_YEAR; y <= maxYear; y++) yearArr.push(y);
          setYears(yearArr);
          setTempSelectedYear(FALLBACK_DEFAULT_YEAR);
          setTempSelectedQuarter(FALLBACK_DEFAULT_QUARTER);
          setIsInitialized(true);
        }
      }
    };

    initializeDefaults();
  }, [isInitialized]);

  return {
    years, quarters,
    tempSelectedYear, setTempSelectedYear,
    tempSelectedQuarter, setTempSelectedQuarter,
    tempSelectedCompanies, setTempSelectedCompanies
  };
} 