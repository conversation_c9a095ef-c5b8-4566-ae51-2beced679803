/* 主容器样式 */
.financial-reports-container {
  
  background: transparent;
  min-height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
}

.financial-reports { 
  margin: 0 auto;
  background: transparent; 
}

/* 卡片样式 */
.financial-reports .ant-card {
   border: 1px solid #d9d9d9;
  border-radius: 0;
  box-shadow: none;
  background: transparent;
}

.financial-reports .ant-card-head {
  background: transparent;
  border-bottom: 2px solid #000;
  padding: 0 16px;
}

.financial-reports .ant-card-head-title {
  color: #000;
  font-size: 18px;
  font-weight: 600;
  padding: 16px 0;
}

/* 标签页样式 */
.financial-reports .ant-tabs {
  margin-top: 16px;
}

.financial-reports .ant-tabs-tab {
  font-size: 14px;
  font-weight: 500;
  padding: 8px 16px;
  margin-right: 4px;
  color: #000;
}

.financial-reports .ant-tabs-tab:hover {
  background: #f5f5f5;
}

.financial-reports .ant-tabs-tab-active {
  background: transparent;
  color: #000;
  border-bottom: 2px solid #000;
}

/* 通用表格样式 */
.balance-sheet,
.income-statement,
.cash-flow-statement {
  padding: 32px;
  background: white;
  border-radius: 0;
}

.balance-sheet-header,
.income-statement-header,
.cash-flow-statement-header {
  text-align: center;
  margin-bottom: 32px;
  padding: 24px;
  background: transparent;
  border-radius: 0;
  box-shadow: none;
}

.balance-sheet-header h3,
.income-statement-header h3,
.cash-flow-statement-header h3 {
  font-size: 24px;
  font-weight: 700;
  color: #000;
  margin-bottom: 12px;
  text-shadow: none;
}

.balance-sheet-header p,
.income-statement-header p,
.cash-flow-statement-header p {
  font-size: 14px;
  color: #666;
  margin: 0;
  font-weight: 500;
}

/* 表格样式 */
.balance-sheet-table,
.income-statement-table,
.cash-flow-statement-table {
  font-family: 'SF Pro Text', 'Segoe UI', 'Microsoft YaHei', 'PingFang SC', -apple-system, BlinkMacSystemFont, sans-serif;
  font-size: 15px;
  line-height: 1.5;
}

.balance-sheet-table .ant-table-thead > tr > th,
.income-statement-table .ant-table-thead > tr > th,
.cash-flow-statement-table .ant-table-thead > tr > th {
  background: #fff;
  font-weight: 400;
  text-align: center;
  border: 1px solid #d9d9d9;
  padding: 14px 12px;
  color: #333;
  font-size: 15px;
  letter-spacing: 0.5px;
}

.cash-flow-statement-table table ,.balance-sheet-table table, .income-statement-table table, .cash-flow-statement-table table{
  border-collapse: collapse;
  border: 0px;
}

.balance-sheet-table .ant-table-tbody > tr > td,
.income-statement-table .ant-table-tbody > tr > td,
.cash-flow-statement-table .ant-table-tbody > tr > td {
  border: 1px solid #d9d9d9;
  padding: 10px 12px;
  vertical-align: middle;
  color: #333;
  font-size: 14px;
  font-weight: 400;
  background: #fff !important;
}

/* 移除项目列背景色，保持简洁 */

/* 覆盖Ant Design表格的默认样式 */
.balance-sheet-table .ant-table,
.income-statement-table .ant-table,
.cash-flow-statement-table .ant-table {
  border-radius: 0 !important;
}

.balance-sheet-table .ant-table-container,
.income-statement-table .ant-table-container,
.cash-flow-statement-table .ant-table-container {
  border-radius: 0 !important;
}

.balance-sheet-table .ant-table-tbody > tr:nth-child(even) > td,
.income-statement-table .ant-table-tbody > tr:nth-child(even) > td,
.cash-flow-statement-table .ant-table-tbody > tr:nth-child(even) > td {
  background: #fff !important;
}

.balance-sheet-table .ant-table-tbody > tr:nth-child(odd) > td,
.income-statement-table .ant-table-tbody > tr:nth-child(odd) > td,
.cash-flow-statement-table .ant-table-tbody > tr:nth-child(odd) > td {
  background: #fff !important;
}

.balance-sheet-table .ant-table-tbody > tr:hover > td,
.income-statement-table .ant-table-tbody > tr:hover > td,
.cash-flow-statement-table .ant-table-tbody > tr:hover > td {
  background: #fff !important;
}

/* 行样式 */
.header-row {
  font-weight: 500;
}

.subtotal-row {
  font-weight: 400;
}

.total-row {
  font-weight: 500;
}

.sub-row,
.sub-sub-row,
.sub-sub-sub-row {
  background-color: transparent;
}

/* 项目名称样式 - 实现缩进效果 */
.balance-sheet-item,
.income-statement-item,
.cash-flow-item {
  display: inline-block;
  width: 100%;
  font-weight: 400;
  color: #333;
  font-size: 14px;
  white-space: pre;
}

.header-item {
  font-weight: 500;
  color: #333;
  font-size: 15px;
}

.subtotal-item {
  font-weight: 450;
  color: #333;
  font-size: 14px;
}

.total-item {
  font-weight: 500;
  color: #333;
  font-size: 15px;
}

/* 移除padding-left样式，使用空格缩进 */
.sub-item,
.sub-sub-item,
.sub-sub-sub-item {
  font-weight: 400;
  color: #333;
  font-size: 14px;
}

/* 金额样式 */
.balance-sheet-amount,
.income-statement-amount,
.cash-flow-amount {
  font-weight: 400;
  color: #333;
  text-align: right;
  font-size: 14px;
  font-family: 'SF Mono', 'Monaco', 'Consolas', monospace;
}

.header-amount {
  font-weight: 500;
  color: #333;
  text-align: center;
  font-size: 15px;
}

.subtotal-amount {
  font-weight: 450;
  color: #333;
  text-align: right;
  font-size: 14px;
  font-family: 'SF Mono', 'Monaco', 'Consolas', monospace;
}

.total-amount {
  font-weight: 500;
  color: #333;
  text-align: right;
  font-size: 15px;
  font-family: 'SF Mono', 'Monaco', 'Consolas', monospace;
}

/* 负数显示 */
.negative-amount {
  color: #000;
}

.negative-amount::before {
  content: '(';
}

.negative-amount::after {
  content: ')';
}

/* 响应式设计 */
@media (max-width: 768px) {
  .financial-reports-container {
    padding: 10px;
  }
  
  .sub-item {
    padding-left: 15px;
  }
  
  .sub-sub-item {
    padding-left: 30px;
  }
  
  .sub-sub-sub-item {
    padding-left: 45px;
  }
}

/* 打印样式 */
@media print {
  .financial-reports-container {
    background: white;
  }
  
  .financial-reports .ant-card {
    border: 1px solid #000;
  }
}
