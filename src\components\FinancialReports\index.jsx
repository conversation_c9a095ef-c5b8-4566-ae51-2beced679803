import React, { useState, useEffect, useCallback } from 'react';
import { Card, Tabs, Button, message, Select, Space } from 'antd';
import { DownloadOutlined, SearchOutlined, ArrowLeftOutlined } from '@ant-design/icons';
import * as XLSX from 'xlsx';
import { formatMetricValue, getDefaultYearAndQuarter } from '../../utils/formatters';
import BalanceSheet from './BalanceSheet';
import IncomeStatement from './IncomeStatement';
import CashFlowStatement from './CashFlowStatement';
import './index.css';
import axios from 'axios'; // 引入axios用于API请求
import defaultFinancialData from './defaultFinancialData.json';

const { TabPane } = Tabs;
const { Option } = Select;

const FinancialReports = ({ navigateToPage, params = {} }) => {
  const [selectedCompany, setSelectedCompany] = useState('');
  
  // 获取默认年份和季度（将在useEffect中动态设置）
  const [selectedYear, setSelectedYear] = useState('2024');
  const [selectedQuarter, setSelectedQuarter] = useState('Q4');
  const [financialData, setFinancialData] = useState({ balanceSheet: [], incomeStatement: [], cashFlowStatement: [] });
  const [companies, setCompanies] = useState([]);
  const [loading, setLoading] = useState(false);

  // 归一化季度参数
  const normalizeQuarter = (q) => {
    if (q === undefined || q === null || q === '') return 'Q4';
    const t = String(q).toUpperCase().trim();
    const map = {
      'Q1': 'Q1', '1': 'Q1', '01': 'Q1', '03-31': 'Q1',
      'Q2': 'Q2', '2': 'Q2', '02': 'Q2', '06-30': 'Q2',
      'Q3': 'Q3', '3': 'Q3', '03': 'Q3', '09-30': 'Q3',
      'Q4': 'Q4', '4': 'Q4', '04': 'Q4', '12-31': 'Q4', 'ANNUAL': 'Q4', 'FY': 'Q4', 'YEAR': 'Q4'
    };
    return map[t] || 'Q4';
  };

  // 参数优先：如果传入了 year/quarter，优先应用
  useEffect(() => {
    if (params.year) {
      setSelectedYear(String(params.year));
    }
    if (params.quarter) {
      setSelectedQuarter(normalizeQuarter(params.quarter));
    }
  }, [params.year, params.quarter]);

  // 初始化默认年份和季度
  useEffect(() => {
    const initializeDefaults = async () => {
      try {
        // 获取L2Metrics最新报告日期
        const { year, quarter } = await getDefaultYearAndQuarter();
        
        // 将季度转换为Q1-Q4格式
        const quarterMap = {
          'Q1': 'Q1',
          'Q2': 'Q2', 
          'Q3': 'Q3',
          'ANNUAL': 'Q4'
        };
        
        const quarterValue = quarterMap[quarter] || 'Q4';
        
        // 如果没有传入参数，使用动态默认值
        if (!params.year) {
          setSelectedYear(year.toString());
        }
        if (!params.quarter) {
          setSelectedQuarter(quarterValue);
        }
      } catch (error) {
        console.error('获取默认年份和季度失败:', error);
        // 使用静态默认值
        if (!params.year) {
          setSelectedYear('2024');
        }
        if (!params.quarter) {
          setSelectedQuarter('Q4');
        }
      }
    };

    initializeDefaults();
  }, [params.year, params.quarter]);

  // 获取公司列表
  useEffect(() => {
    const fetchCompanies = async () => {
      try {
        const response = await axios.get('./api/companies');
        const companiesData = response.data.map(company => ({
          value: company.id,
          label: company.name
        }));
        setCompanies(companiesData);
        
        // 参数优先：companyId > companyName > 列表第一个
        if (companiesData.length > 0) {
          if (params.companyId !== undefined && params.companyId !== null && params.companyId !== '') {
            const matchedById = companiesData.find(c => String(c.value) === String(params.companyId));
            if (matchedById) {
              setSelectedCompany(matchedById.value);
              return;
            }
          }

          if (params.companyName) {
            const matchedByName = companiesData.find(company => company.label === params.companyName);
            if (matchedByName) {
              setSelectedCompany(matchedByName.value);
              return;
            }
          }

          setSelectedCompany(companiesData[0].value);
        }
      } catch (error) {
        console.error('获取公司列表失败:', error);
        message.error('获取公司列表失败！');
      }
    };

    fetchCompanies();
  }, [params.companyName, params.companyId]);

  // 生成从2019年到当前年份的年份数组
  const years = (() => {
    const currentYear = new Date().getFullYear();
    const years = [];
    for (let year = 2019; year <= currentYear; year++) {
      years.push(year.toString());
    }
    return years;
  })();
  const quarters = ['Q1', 'Q2', 'Q3', 'Q4'];

  // 获取财务数据
  const fetchFinancialData = useCallback(async (companyId) => {
    if (!companyId) return;
    
    setLoading(true);
    try {
      // 根据季度计算季度末日期
      const getQuarterEndDate = (year, quarter) => {
        const quarterMonths = {
          Q1: '03-31',
          Q2: '06-30', 
          Q3: '09-30',
          Q4: '12-31'
        };
        return `${year}-${quarterMonths[quarter]}`;
      };

      const response = await axios.post('./api/financial/reports/all', {
        company_id: companyId,
        report_date: getQuarterEndDate(selectedYear, selectedQuarter),
        format_amounts: true
      });

      // 数据转换函数（增强版）
      const transformReportData = (apiData) => {
        if (!apiData || !Array.isArray(apiData)) return [];
        
        return apiData.map(item => {
          // 确保item.item是字符串
          const itemText = item.item !== undefined && item.item !== null 
            ? String(item.item) 
            : '';
          
          return {
            key: item.WindCode || '',
            item: itemText,
            currentAmount: item.Value !== null ? formatMetricValue(item.Value, null, null, null) : formatMetricValue(0, null, null, null),
            previousAmount: item.YoYValue !== null ? formatMetricValue(item.YoYValue, null, null, null) : formatMetricValue(0, null, null, null),
            level: item.Level || 0,
            rowType: determineRowType({...item, item: itemText})
          };
        });
      };

      // 判断行类型（增强版）
      const determineRowType = (item) => {
        try {
          if (!item || item.item === null || item.item === undefined) return 'normal';
          
          // 确保item.item是字符串
          const itemText = typeof item.item === 'string' ? item.item : String(item.item);
          
          if (!itemText) return 'normal';
          
          // 处理标题行
          if (/^[一二三四五六七八九十]、/.test(itemText)) {
            return 'header';
          }
          
          // 处理子项
          if (typeof itemText.includes === 'function' && (
              itemText.includes('其中：') || itemText.includes('其中:') ||
              itemText.includes('（一）') || itemText.includes('（二）') ||
              itemText.includes('加：') || itemText.includes('减：'))) {
            return 'sub';
          }
          
          // 处理合计行
          if (typeof itemText.includes === 'function' && 
              (itemText.includes('合计') || itemText.includes('总计'))) {
            return 'total';
          }
          
          // 处理小计行
          if (typeof itemText.includes === 'function' && (
              itemText.includes('净利润') || itemText.includes('营业利润') || 
              itemText.includes('利润总额') || itemText.includes('经营现金流'))) {
            return 'subtotal';
          }
          
          return 'normal';
        } catch (e) {
          console.error('判断行类型出错:', e);
          return 'normal';
        }
      };

      setFinancialData({
        balanceSheet: transformReportData(response.data.data.balanceSheet),
        incomeStatement: transformReportData(response.data.data.incomeStatement),
        cashFlowStatement: transformReportData(response.data.data.cashFlowStatement)
      });
    } catch (error) {
      console.error('获取财务数据失败:', error);
      message.error('获取财务数据失败！');
    } finally {
      setLoading(false);
    }
  }, [selectedYear, selectedQuarter]);

  // 自动检索逻辑（默认加载数据）
  useEffect(() => {
    if (selectedCompany && companies.length > 0) {
      // 延迟一小段时间确保组件完全加载
      const timer = setTimeout(() => {
        fetchFinancialData(selectedCompany);
        if (params.autoSearch) {
          message.info(`已自动加载${params.companyName || ''}的${selectedYear}年${selectedQuarter}财务报表`);
        }
      }, 500);
      
      return () => clearTimeout(timer);
    }
  }, [selectedCompany, companies, params.autoSearch, params.companyName, selectedYear, selectedQuarter, fetchFinancialData]);

  const handleCompanyChange = (value) => {
    setSelectedCompany(value);
  };

  const handleYearChange = (value) => {
    setSelectedYear(value);
  };

  const handleQuarterChange = (value) => {
    setSelectedQuarter(value);
  };

  // 处理检索按钮点击
  const handleSearch = () => {
    if (selectedCompany) {
      fetchFinancialData(selectedCompany);
    } else {
      message.warning('请先选择公司！');
    }
  };

  const exportToExcel = () => {
    try {
      const wb = XLSX.utils.book_new();
      const companyLabel = companies.find(c => c.value === selectedCompany)?.label || selectedCompany;
      const period = `${selectedYear}年${selectedQuarter}`;

      const formatSheetData = (title, data) => {
        const sheetData = [
          [title, '', ''],
          [`公司名称：${companyLabel}`, '', ''],
          [`报告期间：${period}`, '', ''],
          ['', '', ''],
          ['项目', `${selectedYear}年金额`, `${parseInt(selectedYear) - 1}年金额`] // 动态表头年份
        ];
        data.forEach(row => {
          const indent = '  '.repeat(row.level || 0);
          sheetData.push([`${indent}${row.item}`, row.currentAmount, row.previousAmount]);
        });
        return sheetData;
      };

      const balanceSheetExportData = formatSheetData('资产负债表', financialData.balanceSheet);
      const incomeStatementExportData = formatSheetData('利润表', financialData.incomeStatement);
      const cashFlowExportData = formatSheetData('现金流量表', financialData.cashFlowStatement);

      const ws1 = XLSX.utils.aoa_to_sheet(balanceSheetExportData);
      const ws2 = XLSX.utils.aoa_to_sheet(incomeStatementExportData);
      const ws3 = XLSX.utils.aoa_to_sheet(cashFlowExportData);

      XLSX.utils.book_append_sheet(wb, ws1, '资产负债表');
      XLSX.utils.book_append_sheet(wb, ws2, '利润表');
      XLSX.utils.book_append_sheet(wb, ws3, '现金流量表');

      const fileName = `财务报表_${companyLabel}_${selectedYear}${selectedQuarter}.xlsx`;
      XLSX.writeFile(wb, fileName);

      message.success('财务报表导出成功！');
    } catch (error) {
      console.error('导出失败:', error);
      message.error('导出失败，请重试！');
    }
  };

  // 获取当前页面标题
  const getCardTitle = () => {
    const baseTitle = '财务报表';
    if (params.companyName) {
      return `${baseTitle} - ${params.companyName}`;
    }
    return baseTitle;
  };

  return (
    <div className="financial-reports-container">
      <div className="financial-reports">
        <Card 
          title={getCardTitle()}
          extra={
            <div style={{ display: 'flex', gap: '12px', alignItems: 'center' }}>
              <div style={{ 
                fontSize: '12px', 
                color: '#666', 
                marginRight: '8px',
                fontStyle: 'italic'
              }}>
                数据将自动加载
              </div>
              <Select
                value={selectedCompany}
                onChange={setSelectedCompany}
                style={{ width: 150 }}
                placeholder="选择公司"
              >
                {companies.map(company => (
                  <Option key={company.value} value={company.value}>
                    {company.label}
                  </Option>
                ))}
              </Select>
              <Select
                value={selectedYear}
                onChange={setSelectedYear}
                style={{ width: 80 }}
                placeholder="年份"
              >
                {years.map(year => (
                  <Option key={year} value={year}>{year}</Option>
                ))}
              </Select>
              <Select
                value={selectedQuarter}
                onChange={setSelectedQuarter}
                style={{ width: 70 }}
                placeholder="季度"
              >
                {quarters.map(quarter => (
                  <Option key={quarter} value={quarter}>{quarter}</Option>
                ))}
              </Select>
              <Button 
                type="primary" 
                icon={<SearchOutlined />}
                onClick={handleSearch}
                loading={loading}
                style={{ marginRight: 8 }}
              >
                {loading ? '加载中...' : '检索'}
              </Button>
              <Button 
                icon={<DownloadOutlined />}
                onClick={exportToExcel}
                disabled={loading || financialData.balanceSheet.length === 0}
              >
                导出Excel
              </Button>
              {params.fromPage && (
                <Button 
                  type="link" 
                  icon={<ArrowLeftOutlined />}
                  onClick={() => navigateToPage && navigateToPage(params.fromPage)}
                  size="small"
                >
                  返回{params.fromPage === 'report-schedule' ? '报告时间' : '上一页'}
                </Button>
              )}
            </div>
          }
        >
          <Tabs defaultActiveKey="1">
            <TabPane tab="资产负债表" key="1">
              <BalanceSheet data={financialData.balanceSheet} company={selectedCompany} year={selectedYear} quarter={selectedQuarter} />
            </TabPane>
            <TabPane tab="利润表" key="2">
              <IncomeStatement data={financialData.incomeStatement} company={selectedCompany} year={selectedYear} quarter={selectedQuarter} />
            </TabPane>
            <TabPane tab="现金流量表" key="3">
              <CashFlowStatement data={financialData.cashFlowStatement} company={selectedCompany} year={selectedYear} quarter={selectedQuarter} />
            </TabPane>
          </Tabs>
        </Card>
      </div>
    </div>
  );
};

export default FinancialReports;
