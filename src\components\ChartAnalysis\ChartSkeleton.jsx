import React from 'react';

const ChartSkeleton = ({ height = 320, title = "图表", type = "default" }) => {
  const getSkeletonContent = () => {
    switch (type) {
      case "bar":
        return (
          <div style={{ padding: '20px' }}>
            {/* 标题骨架 */}
            <div style={{ 
              height: '24px', 
              background: 'linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%)',
              backgroundSize: '200% 100%',
              animation: 'loading 1.5s infinite',
              borderRadius: '4px',
              marginBottom: '20px',
              width: '60%'
            }} />
            
            {/* 图例骨架 */}
            <div style={{ 
              display: 'flex', 
              gap: '20px', 
              marginBottom: '30px',
              justifyContent: 'center'
            }}>
              {[1, 2, 3].map(i => (
                <div key={i} style={{ 
                  display: 'flex', 
                  alignItems: 'center', 
                  gap: '8px' 
                }}>
                  <div style={{ 
                    width: '12px', 
                    height: '12px', 
                    borderRadius: '2px',
                    background: 'linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%)',
                    backgroundSize: '200% 100%',
                    animation: 'loading 1.5s infinite'
                  }} />
                  <div style={{ 
                    width: '60px', 
                    height: '14px',
                    background: 'linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%)',
                    backgroundSize: '200% 100%',
                    animation: 'loading 1.5s infinite',
                    borderRadius: '2px'
                  }} />
                </div>
              ))}
            </div>
            
            {/* 柱状图骨架 */}
            <div style={{ 
              display: 'flex', 
              justifyContent: 'space-around', 
              alignItems: 'flex-end',
              height: '200px',
              padding: '0 20px'
            }}>
              {[1, 2, 3, 4, 5].map(i => (
                <div key={i} style={{ 
                  display: 'flex', 
                  flexDirection: 'column', 
                  alignItems: 'center',
                  gap: '8px'
                }}>
                  <div style={{ 
                    width: '40px', 
                    height: `${120 + Math.random() * 80}px`,
                    background: 'linear-gradient(180deg, #f0f0f0 0%, #e0e0e0 100%)',
                    borderRadius: '4px 4px 0 0',
                    animation: 'loading 1.5s infinite'
                  }} />
                  <div style={{ 
                    width: '50px', 
                    height: '12px',
                    background: 'linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%)',
                    backgroundSize: '200% 100%',
                    animation: 'loading 1.5s infinite',
                    borderRadius: '2px'
                  }} />
                </div>
              ))}
            </div>
          </div>
        );
        
      case "line":
        return (
          <div style={{ padding: '20px' }}>
            {/* 标题骨架 */}
            <div style={{ 
              height: '24px', 
              background: 'linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%)',
              backgroundSize: '200% 100%',
              animation: 'loading 1.5s infinite',
              borderRadius: '4px',
              marginBottom: '20px',
              width: '60%'
            }} />
            
            {/* 图例骨架 */}
            <div style={{ 
              display: 'flex', 
              gap: '20px', 
              marginBottom: '30px',
              justifyContent: 'center'
            }}>
              {[1, 2].map(i => (
                <div key={i} style={{ 
                  display: 'flex', 
                  alignItems: 'center', 
                  gap: '8px' 
                }}>
                  <div style={{ 
                    width: '20px', 
                    height: '2px',
                    background: 'linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%)',
                    backgroundSize: '200% 100%',
                    animation: 'loading 1.5s infinite'
                  }} />
                  <div style={{ 
                    width: '60px', 
                    height: '14px',
                    background: 'linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%)',
                    backgroundSize: '200% 100%',
                    animation: 'loading 1.5s infinite',
                    borderRadius: '2px'
                  }} />
                </div>
              ))}
            </div>
            
            {/* 折线图骨架 */}
            <div style={{ 
              height: '200px',
              padding: '20px',
              position: 'relative'
            }}>
              <svg width="100%" height="100%" style={{ opacity: 0.3 }}>
                <path
                  d="M 20 150 Q 60 100 100 120 Q 140 140 180 80 Q 220 60 260 100"
                  stroke="#e0e0e0"
                  strokeWidth="2"
                  fill="none"
                />
                <path
                  d="M 20 120 Q 60 80 100 100 Q 140 120 180 60 Q 220 40 260 80"
                  stroke="#f0f0f0"
                  strokeWidth="2"
                  fill="none"
                />
              </svg>
            </div>
          </div>
        );
        
      case "radar":
        return (
          <div style={{ padding: '20px', textAlign: 'center' }}>
            {/* 标题骨架 */}
            <div style={{ 
              height: '24px', 
              background: 'linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%)',
              backgroundSize: '200% 100%',
              animation: 'loading 1.5s infinite',
              borderRadius: '4px',
              marginBottom: '20px',
              width: '60%',
              margin: '0 auto 20px'
            }} />
            
            {/* 雷达图骨架 */}
            <div style={{ 
              width: '200px', 
              height: '200px', 
              margin: '0 auto',
              position: 'relative',
              opacity: 0.3
            }}>
              <svg width="100%" height="100%">
                <polygon
                  points="100,20 150,50 150,150 100,180 50,150 50,50"
                  fill="none"
                  stroke="#e0e0e0"
                  strokeWidth="1"
                />
                <polygon
                  points="100,40 130,60 130,140 100,160 70,140 70,60"
                  fill="none"
                  stroke="#f0f0f0"
                  strokeWidth="1"
                />
              </svg>
            </div>
          </div>
        );
        
      default:
        return (
          <div style={{ padding: '20px' }}>
            {/* 标题骨架 */}
            <div style={{ 
              height: '24px', 
              background: 'linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%)',
              backgroundSize: '200% 100%',
              animation: 'loading 1.5s infinite',
              borderRadius: '4px',
              marginBottom: '20px',
              width: '60%'
            }} />
            
            {/* 内容骨架 */}
            <div style={{ 
              height: '200px',
              background: 'linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%)',
              backgroundSize: '200% 100%',
              animation: 'loading 1.5s infinite',
              borderRadius: '8px'
            }} />
          </div>
        );
    }
  };

  return (
    <div style={{ 
      width: '100%', 
      height: height,
      background: '#fff',
      borderRadius: '8px',
      boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
      overflow: 'hidden'
    }}>
      <style>
        {`
          @keyframes loading {
            0% { background-position: 200% 0; }
            100% { background-position: -200% 0; }
          }
        `}
      </style>
      {getSkeletonContent()}
    </div>
  );
};

export default ChartSkeleton; 