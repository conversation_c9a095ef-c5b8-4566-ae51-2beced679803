-- 创建财务指标计算结果表
CREATE TABLE L2Metrics (
    ID INT IDENTITY(1,1) PRIMARY KEY,                -- 主键ID
    TickerSymbol NVARCHAR(20) NOT NULL,              -- 股票代码
    CompanyName NVARCHAR(100) NOT NULL,              -- 公司名称
    ReportDate DATE NOT NULL,                        -- 报告日期
    IndicatorNameCN NVARCHAR(100) NOT NULL,          -- 指标中文名称
    IndicatorNameEN NVARCHAR(100) NOT NULL,          -- 指标英文名称
    FormulaDesc NVARCHAR(MAX),                       -- 公式中文描述
    FormulaEN NVARCHAR(MAX),                         -- 公式英文描述
    CalculatedValue DECIMAL(38, 6),                  -- 计算结果
    LowerBound DECIMAL(38, 6),                       -- 下限值
    UpperBound DECIMAL(38, 6),                       -- 上限值
    RangeValue NVARCHAR(100),                        -- 范围值描述
    EvaluationResult NVARCHAR(50),                   -- 评估结果（优秀/良好/一般/较差）
    CalculationTime DATETIME DEFAULT GETDATE(),      -- 计算时间
    Remarks NVARCHAR(MAX),                           -- 备注
    CreateTime DATETIME DEFAULT GETDATE()            -- 创建时间
    CONSTRAINT UK_FinancialMetricsResults UNIQUE (TickerSymbol, ReportDate, IndicatorNameEN)  -- 唯一约束
);

-- 创建指标计算日志表
CREATE TABLE FinancialMetricsCalculationLog (
    ID INT IDENTITY(1,1) PRIMARY KEY,                -- 主键ID
    TickerSymbol NVARCHAR(20) NOT NULL,              -- 股票代码
    ReportDate DATE NOT NULL,                        -- 报告日期
    CalculationTime DATETIME DEFAULT GETDATE(),      -- 计算时间
    SuccessCount INT,                                -- 成功计算的指标数量
    FailureCount INT,                                -- 计算失败的指标数量
    ExecutionTime DECIMAL(10, 3),                    -- 执行时间（秒）
    LogMessage NVARCHAR(MAX)                         -- 日志信息
);

-- 创建指标计算错误日志表
CREATE TABLE FinancialMetricsErrorLog (
    ID INT IDENTITY(1,1) PRIMARY KEY,                -- 主键ID
    TickerSymbol NVARCHAR(20) NOT NULL,              -- 股票代码
    ReportDate DATE NOT NULL,                        -- 报告日期
    IndicatorNameCN NVARCHAR(100) NOT NULL,          -- 指标中文名称
    IndicatorNameEN NVARCHAR(100) NOT NULL,          -- 指标英文名称
    FormulaDesc NVARCHAR(MAX),                       -- 公式中文描述
    FormulaEN NVARCHAR(MAX),                         -- 公式英文描述
    ErrorMessage NVARCHAR(MAX),                      -- 错误信息
    ProcessedFormula NVARCHAR(MAX),                  -- 处理后的公式
    ErrorTime DATETIME DEFAULT GETDATE()             -- 错误时间
);

-- 创建指标定义表（如果不存在）
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'FinancialMetricsDefinition')
BEGIN
    CREATE TABLE FinancialMetricsDefinition (
        ID INT IDENTITY(1,1) PRIMARY KEY,                -- 主键ID
        IndicatorNameCN NVARCHAR(100) NOT NULL,          -- 指标中文名称
        IndicatorNameEN NVARCHAR(100) NOT NULL,          -- 指标英文名称
        IndicatorDescription NVARCHAR(MAX),              -- 指标描述
        FormulaDesc NVARCHAR(MAX),                       -- 公式中文描述
        FormulaEN NVARCHAR(MAX),                         -- 公式英文描述
        LowerBound DECIMAL(38, 6),                       -- 下限值
        UpperBound DECIMAL(38, 6),                       -- 上限值
        RangeValue NVARCHAR(100),                        -- 范围值描述
        Category NVARCHAR(50),                           -- 指标类别（盈利能力/偿债能力/运营能力/成长能力/现金流量）
        IsActive BIT DEFAULT 1,                          -- 是否启用
        CreateTime DATETIME DEFAULT GETDATE(),           -- 创建时间
        UpdateTime DATETIME DEFAULT GETDATE(),           -- 更新时间
        CONSTRAINT UK_FinancialMetricsDefinition UNIQUE (IndicatorNameCN, IndicatorNameEN)  -- 唯一约束
    );
END

-- 创建指标历史数据表
CREATE TABLE FinancialMetricsHistory (
    ID INT IDENTITY(1,1) PRIMARY KEY,                -- 主键ID
    TickerSymbol NVARCHAR(20) NOT NULL,              -- 股票代码
    CompanyName NVARCHAR(100) NOT NULL,              -- 公司名称
    ReportDate DATE NOT NULL,                        -- 报告日期
    IndicatorNameEN NVARCHAR(100) NOT NULL,          -- 指标英文名称
    CalculatedValue DECIMAL(38, 6),                  -- 计算结果
    YoYGrowth DECIMAL(10, 4),                        -- 同比增长率
    QoQGrowth DECIMAL(10, 4),                        -- 环比增长率
    IndustryAvg DECIMAL(38, 6),                      -- 行业平均值
    IndustryRank INT,                                -- 行业排名
    CalculationTime DATETIME DEFAULT GETDATE(),      -- 计算时间
    CONSTRAINT UK_FinancialMetricsHistory UNIQUE (TickerSymbol, ReportDate, IndicatorNameEN)  -- 唯一约束
);

-- 创建存储过程，用于保存指标计算结果
CREATE OR ALTER PROCEDURE SaveMetricsResults
    @TickerSymbol NVARCHAR(20),
    @CompanyName NVARCHAR(100),
    @ReportDate DATE,
    @IndicatorNameCN NVARCHAR(100),
    @IndicatorNameEN NVARCHAR(100),
    @FormulaDesc NVARCHAR(MAX),
    @FormulaEN NVARCHAR(MAX),
    @CalculatedValue DECIMAL(38, 6),
    @LowerBound DECIMAL(38, 6) = NULL,
    @UpperBound DECIMAL(38, 6) = NULL,
    @RangeValue NVARCHAR(100) = NULL,
    @Remarks NVARCHAR(MAX) = NULL
AS
BEGIN
    SET NOCOUNT ON;
    
    -- 确定评估结果
    DECLARE @EvaluationResult NVARCHAR(50) = '一般';
    
    IF @LowerBound IS NOT NULL AND @UpperBound IS NOT NULL
    BEGIN
        IF @CalculatedValue < @LowerBound
            SET @EvaluationResult = '较差';
        ELSE IF @CalculatedValue > @UpperBound
            SET @EvaluationResult = '优秀';
        ELSE
            SET @EvaluationResult = '良好';
    END
    
    -- 插入或更新结果
    MERGE FinancialMetricsResults AS target
    USING (SELECT @TickerSymbol, @ReportDate, @IndicatorNameEN) AS source (TickerSymbol, ReportDate, IndicatorNameEN)
    ON (target.TickerSymbol = source.TickerSymbol AND target.ReportDate = source.ReportDate AND target.IndicatorNameEN = source.IndicatorNameEN)
    WHEN MATCHED THEN
        UPDATE SET 
            CompanyName = @CompanyName,
            IndicatorNameCN = @IndicatorNameCN,
            FormulaDesc = @FormulaDesc,
            FormulaEN = @FormulaEN,
            CalculatedValue = @CalculatedValue,
            LowerBound = @LowerBound,
            UpperBound = @UpperBound,
            RangeValue = @RangeValue,
            EvaluationResult = @EvaluationResult,
            CalculationTime = GETDATE(),
            Remarks = @Remarks
    WHEN NOT MATCHED THEN
        INSERT (TickerSymbol, CompanyName, ReportDate, IndicatorNameCN, IndicatorNameEN, FormulaDesc, FormulaEN, 
                CalculatedValue, LowerBound, UpperBound, RangeValue, EvaluationResult, Remarks)
        VALUES (@TickerSymbol, @CompanyName, @ReportDate, @IndicatorNameCN, @IndicatorNameEN, @FormulaDesc, @FormulaEN, 
                @CalculatedValue, @LowerBound, @UpperBound, @RangeValue, @EvaluationResult, @Remarks);
    
    -- 更新历史数据表
    -- 计算同比增长率
    DECLARE @LastYearValue DECIMAL(38, 6);
    DECLARE @LastQuarterValue DECIMAL(38, 6);
    DECLARE @YoYGrowth DECIMAL(10, 4) = NULL;
    DECLARE @QoQGrowth DECIMAL(10, 4) = NULL;
    
    -- 获取去年同期的值
    SELECT @LastYearValue = CalculatedValue
    FROM FinancialMetricsResults
    WHERE TickerSymbol = @TickerSymbol 
      AND IndicatorNameEN = @IndicatorNameEN
      AND ReportDate = DATEADD(YEAR, -1, @ReportDate);
    
    -- 获取上一季度的值
    SELECT @LastQuarterValue = CalculatedValue
    FROM FinancialMetricsResults
    WHERE TickerSymbol = @TickerSymbol 
      AND IndicatorNameEN = @IndicatorNameEN
      AND ReportDate = (
          SELECT MAX(ReportDate)
          FROM FinancialMetricsResults
          WHERE TickerSymbol = @TickerSymbol 
            AND IndicatorNameEN = @IndicatorNameEN
            AND ReportDate < @ReportDate
      );
    
    -- 计算同比增长率
    IF @LastYearValue IS NOT NULL AND @LastYearValue <> 0
        SET @YoYGrowth = (@CalculatedValue - @LastYearValue) / ABS(@LastYearValue) * 100;
    
    -- 计算环比增长率
    IF @LastQuarterValue IS NOT NULL AND @LastQuarterValue <> 0
        SET @QoQGrowth = (@CalculatedValue - @LastQuarterValue) / ABS(@LastQuarterValue) * 100;
    
    -- 更新历史数据表
    MERGE FinancialMetricsHistory AS target
    USING (SELECT @TickerSymbol, @ReportDate, @IndicatorNameEN) AS source (TickerSymbol, ReportDate, IndicatorNameEN)
    ON (target.TickerSymbol = source.TickerSymbol AND target.ReportDate = source.ReportDate AND target.IndicatorNameEN = source.IndicatorNameEN)
    WHEN MATCHED THEN
        UPDATE SET 
            CompanyName = @CompanyName,
            CalculatedValue = @CalculatedValue,
            YoYGrowth = @YoYGrowth,
            QoQGrowth = @QoQGrowth,
            CalculationTime = GETDATE()
    WHEN NOT MATCHED THEN
        INSERT (TickerSymbol, CompanyName, ReportDate, IndicatorNameEN, CalculatedValue, YoYGrowth, QoQGrowth, CalculationTime)
        VALUES (@TickerSymbol, @CompanyName, @ReportDate, @IndicatorNameEN, @CalculatedValue, @YoYGrowth, @QoQGrowth, GETDATE());
END;
GO

-- 创建存储过程，用于记录计算错误
CREATE OR ALTER PROCEDURE LogMetricsCalculationError
    @TickerSymbol NVARCHAR(20),
    @ReportDate DATE,
    @IndicatorNameCN NVARCHAR(100),
    @IndicatorNameEN NVARCHAR(100),
    @FormulaDesc NVARCHAR(MAX),
    @FormulaEN NVARCHAR(MAX),
    @ErrorMessage NVARCHAR(MAX),
    @ProcessedFormula NVARCHAR(MAX) = NULL
AS
BEGIN
    SET NOCOUNT ON;
    
    INSERT INTO FinancialMetricsErrorLog (TickerSymbol, ReportDate, IndicatorNameCN, IndicatorNameEN, 
                                         FormulaDesc, FormulaEN, ErrorMessage, ProcessedFormula)
    VALUES (@TickerSymbol, @ReportDate, @IndicatorNameCN, @IndicatorNameEN, 
            @FormulaDesc, @FormulaEN, @ErrorMessage, @ProcessedFormula);
END;
GO

-- 创建存储过程，用于记录计算日志
CREATE OR ALTER PROCEDURE LogMetricsCalculation
    @TickerSymbol NVARCHAR(20),
    @ReportDate DATE,
    @SuccessCount INT,
    @FailureCount INT,
    @ExecutionTime DECIMAL(10, 3),
    @LogMessage NVARCHAR(MAX) = NULL
AS
BEGIN
    SET NOCOUNT ON;
    
    INSERT INTO FinancialMetricsCalculationLog (TickerSymbol, ReportDate, SuccessCount, FailureCount, ExecutionTime, LogMessage)
    VALUES (@TickerSymbol, @ReportDate, @SuccessCount, @FailureCount, @ExecutionTime, @LogMessage);
END;
GO

-- 创建视图，用于查看最新的指标计算结果
CREATE OR ALTER VIEW vw_LatestFinancialMetrics
AS
SELECT r.TickerSymbol, 
       r.CompanyName, 
       r.ReportDate, 
       r.IndicatorNameCN, 
       r.IndicatorNameEN, 
       r.CalculatedValue, 
       r.EvaluationResult,
       h.YoYGrowth,
       h.QoQGrowth,
       d.Category,
       d.IndicatorDescription
FROM FinancialMetricsResults r
LEFT JOIN FinancialMetricsHistory h ON r.TickerSymbol = h.TickerSymbol 
                                    AND r.ReportDate = h.ReportDate 
                                    AND r.IndicatorNameEN = h.IndicatorNameEN
LEFT JOIN FinancialMetricsDefinition d ON r.IndicatorNameEN = d.IndicatorNameEN
WHERE r.ReportDate = (
    SELECT MAX(ReportDate)
    FROM FinancialMetricsResults
    WHERE TickerSymbol = r.TickerSymbol
);
GO

-- 创建视图，用于查看指标计算错误
CREATE OR ALTER VIEW vw_FinancialMetricsErrors
AS
SELECT e.TickerSymbol,
       c.CompanyName,
       e.ReportDate,
       e.IndicatorNameCN,
       e.IndicatorNameEN,
       e.FormulaDesc,
       e.FormulaEN,
       e.ErrorMessage,
       e.ProcessedFormula,
       e.ErrorTime
FROM FinancialMetricsErrorLog e
LEFT JOIN CompanyInfo c ON e.TickerSymbol = c.TickerSymbol;
GO

-- 创建函数，用于获取指标的历史趋势
CREATE OR ALTER FUNCTION fn_GetMetricTrend
(
    @TickerSymbol NVARCHAR(20),
    @IndicatorNameEN NVARCHAR(100),
    @PeriodCount INT = 8
)
RETURNS TABLE
AS
RETURN
(
    SELECT TOP (@PeriodCount)
           h.TickerSymbol,
           h.CompanyName,
           h.ReportDate,
           h.IndicatorNameEN,
           d.IndicatorNameCN,
           h.CalculatedValue,
           h.YoYGrowth,
           h.QoQGrowth
    FROM FinancialMetricsHistory h
    LEFT JOIN FinancialMetricsDefinition d ON h.IndicatorNameEN = d.IndicatorNameEN
    WHERE h.TickerSymbol = @TickerSymbol
      AND h.IndicatorNameEN = @IndicatorNameEN
    ORDER BY h.ReportDate DESC
);
GO

-- 创建函数，用于获取公司的所有最新指标
CREATE OR ALTER FUNCTION fn_GetCompanyLatestMetrics
(
    @TickerSymbol NVARCHAR(20)
)
RETURNS TABLE
AS
RETURN
(
    SELECT r.TickerSymbol,
           r.CompanyName,
           r.ReportDate,
           r.IndicatorNameCN,
           r.IndicatorNameEN,
           r.CalculatedValue,
           r.EvaluationResult,
           h.YoYGrowth,
           d.Category
    FROM FinancialMetricsResults r
    LEFT JOIN FinancialMetricsHistory h ON r.TickerSymbol = h.TickerSymbol 
                                       AND r.ReportDate = h.ReportDate 
                                       AND r.IndicatorNameEN = h.IndicatorNameEN
    LEFT JOIN FinancialMetricsDefinition d ON r.IndicatorNameEN = d.IndicatorNameEN
    WHERE r.TickerSymbol = @TickerSymbol
      AND r.ReportDate = (
          SELECT MAX(ReportDate)
          FROM FinancialMetricsResults
          WHERE TickerSymbol = @TickerSymbol
      )
);
GO
