import os
from flask import Flask, send_from_directory
from flask_cors import CORS
from .config.settings import config # Corrected import for settings

def create_app(config_name='default'):
    """应用工厂函数"""
    # Define static folder path relative to the project root
    # api/__init__.py is in d:\StudyCode\fin\财务数据分析网站\api\
    # project_root is d:\StudyCode\fin\财务数据分析网站\
    # __file__ is the path to api/__init__.py
    # os.path.dirname(__file__) is d:\StudyCode\fin\财务数据分析网站\api
    # os.path.join(os.path.dirname(__file__), '..') is d:\StudyCode\fin\财务数据分析网站
    project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
    static_folder_path = os.path.join(project_root, 'web')

    app = Flask(__name__, static_folder=static_folder_path, static_url_path='')
    app.config.from_object(config[config_name])
    # 如果有其他初始化，例如数据库、扩展等，可以在这里进行
    # db.init_app(app) # Example if using Flask-SQLAlchemy
    # mail.init_app(app)

    CORS(app) # 允许所有来源的跨域请求，生产环境中应配置更严格的策略

    # 注册蓝图
    from .routes import register_routes
    register_routes(app)

    # Add route for serving index.html from root
    @app.route('/')
    def serve_index():
        # Serves index.html from the 'web' static folder
        # app.static_folder is already configured by Flask(__name__, static_folder=...)
        if not app.static_folder or not os.path.exists(os.path.join(app.static_folder, 'index.html')):
            # Log or handle the case where static folder or index.html is missing
            # For now, let's assume it exists or Flask will handle it (e.g. 404)
            # This check is more for robustness if 'web/index.html' might not exist
            current_app.logger.warning(f"Static folder or index.html not found at {app.static_folder}")
            return jsonify({"error": "Frontend not found"}), 404
        return send_from_directory(app.static_folder, 'index.html')

    return app

# Create the Flask app instance for Gunicorn and direct runs
config_name = os.environ.get('FLASK_CONFIG') or 'default'
app = create_app(config_name)
# print(f"Flask app created in __init__.py with '{config_name}' configuration.") # Optional debug
