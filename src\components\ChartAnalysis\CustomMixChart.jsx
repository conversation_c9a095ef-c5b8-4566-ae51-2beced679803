import React, { useEffect, useRef, useState } from 'react';
import { Chart } from '@antv/g2';

const CustomMixChart = ({ data = [], title = '', height = 320, barTypes = [], lineTypes = [], isEnlarged = false }) => {
  const container = useRef(null);
  const chartRef = useRef(null);
  const [scaleRatio, setScaleRatio] = useState(1);

  useEffect(() => {
    if (isEnlarged) {
      setScaleRatio(1.5);
    } else {
      setScaleRatio(1);
    }
  }, [isEnlarged]);

  useEffect(() => {
    if (!container.current || !data || data.length === 0) {
      return;
    }

    // 清理容器
    container.current.innerHTML = '';

    // 过滤有效数据
    const validData = data.filter(item => 
      item && 
      typeof item.company === 'string' && 
      item.company.trim() !== ''
    );

    if (validData.length === 0) {
      return;
    }

    const createChart = () => {
      if (!container.current) {
        return;
      }

      // 计算容器高度
      let containerHeight = height;
      if (height === '100%') {
        containerHeight = container.current.offsetHeight;
        if (containerHeight === 0) {
          setTimeout(() => createChart(), 100);
          return;
        }
      }

      const finalHeight = typeof containerHeight === 'number' ? containerHeight : 320;

      // 创建图表
      const chart = new Chart({
        container: container.current,
        autoFit: true,
        height: finalHeight,
        title: {
          title,
          titleFontSize: 22,
        },
      });

      // 处理柱状图数据
      const barData = [];
      const finalBarTypes = barTypes.length > 0 ? barTypes : ['value'];
      
      validData.forEach(item => {
        const company = item.company;
        finalBarTypes.forEach(barType => {
          const barItem = item[barType];
          if (typeof barItem === 'number' && barItem > 0) {
            barData.push({
              company,
              type: barType,
              value: barItem,
              category: 'bar'
            });
          }
        });
      });

      // 处理折线图数据
      const lineData = [];
      const finalLineTypes = lineTypes.length > 0 ? lineTypes : ['trend'];
      
      validData.forEach(item => {
        const company = item.company;
        finalLineTypes.forEach(lineType => {
          const lineItem = item[lineType];
          if (typeof lineItem === 'number') {
            lineData.push({
              company,
              type: lineType,
              value: lineItem,
              category: 'line'
            });
          }
        });
      });

      // 合并数据
      const mixedData = [...barData, ...lineData];

      if (mixedData.length === 0) {
        return;
      }

      // 配置图表
      chart.data(mixedData);

      chart.scale('value', {
        nice: true,
      });

      chart.scale('company', {
        nice: true,
      });

      chart.axis('company', {
        title: null,
        label: {
          style: {
            fontSize: 12 * scaleRatio,
            fill: '#666',
          },
        },
      });

      chart.axis('value', {
        title: null,
        label: {
          style: {
            fontSize: 12 * scaleRatio,
            fill: '#666',
          },
        },
      });

      chart.tooltip({
        showCrosshairs: false,
        shared: true,
        showMarkers: false,
      });

      chart.legend({
        position: 'top',
        itemWidth: 14 * scaleRatio,
        itemHeight: 14 * scaleRatio,
        itemSpacing: 8 * scaleRatio,
        text: {
          style: {
            fontSize: 12 * scaleRatio,
            fill: '#666',
          },
        },
      });

      // 柱状图
      if (barData.length > 0) {
        chart
          .interval()
          .data(barData)
          .encode('x', 'company')
          .encode('y', 'value')
          .encode('color', 'type')
          .label('value', {
            content: (d) => {
              if (d.value === 0) return '';
              return d.value?.toLocaleString() || '';
            },
            style: {
              fontSize: 11 * scaleRatio,
              fill: '#333',
              textAlign: 'center',
            },
          });
      }

      // 折线图
      if (lineData.length > 0) {
        chart
          .line()
          .data(lineData)
          .encode('x', 'company')
          .encode('y', 'value')
          .encode('color', 'type')
          .style({
            lineWidth: 2 * scaleRatio,
          })
          .point()
          .encode('x', 'company')
          .encode('y', 'value')
          .encode('color', 'type')
          .style({
            r: 3 * scaleRatio,
          });
      }

      chart.render();
      chartRef.current = chart;
    };

    // 使用 requestAnimationFrame 确保 DOM 准备好
    const startChartCreation = () => {
      requestAnimationFrame(() => {
        if (container.current && container.current.offsetHeight > 0) {
          createChart();
        } else {
          setTimeout(startChartCreation, 50);
        }
      });
    };

    startChartCreation();

    return () => {
      if (chartRef.current) {
        chartRef.current.destroy();
        chartRef.current = null;
      }
    };
  }, [data, height, barTypes, lineTypes, scaleRatio, isEnlarged]);

  return (
    <div 
      ref={container} 
      style={{ 
        width: '100%', 
        height: height === '100%' ? '100%' : `${height}px`,
        position: 'relative'
      }} 
    />
  );
};

export default CustomMixChart; 