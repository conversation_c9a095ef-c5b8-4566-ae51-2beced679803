class Config:
    # 爬虫配置  https://quotes.sina.cn/cn/api/openapi.php/CompanyFinanceService.getFinanceReport2022?paperCode={{#1736130812569.stockcode#}}&source=gjzb&type=0&page=1&num=10&callback=hqccall937347809
    API_URL = "https://quotes.sina.cn/cn/api/openapi.php/CompanyFinanceService.getFinanceReport2022"

    # 数据库配置
    DB_CONFIG = {
        'driver': 'ODBC Driver 17 for SQL Server',
        'server': 'shecc-report.shecc.com',
        'database': 'FinancialReport',
        'user': 'FinancialReport',
        'password': 'tSwcbyy@'
    }

    # 文件路径
    JSON_FILE = 'finance_data.json'
