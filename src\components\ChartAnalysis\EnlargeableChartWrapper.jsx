import React, { useState, useRef, isValidElement, cloneElement } from 'react';
import { ZoomInOutlined } from '@ant-design/icons';

const ANIMATION_DURATION = 300; // ms

const EnlargeableChartWrapper = ({ children, title, style }) => {
  const [enlarged, setEnlarged] = useState(false);
  const [showFullscreen, setShowFullscreen] = useState(false);
  const [showButton, setShowButton] = useState(false);
  const fullscreenRef = useRef(null);

  // 只对真正的图表组件cloneElement
  const isChart = (child) => {
    if (!isValidElement(child)) return false;
    const chartNames = [
      'CustomBar', 'CustomLine', 'CustomRadar', 'CustomBarWithLine', 'CustomMixChart',
      'AssetStackedColumn', 'DebtStackedColumn', 'HorizontalBar', 'CashHorizontalBar'
    ];
    return chartNames.includes(child.type?.name);
  };

  // 进入全屏
  const handleEnlarge = () => {
    setEnlarged(true);
    setShowFullscreen(true);
    setTimeout(() => {
      if (fullscreenRef.current && fullscreenRef.current.requestFullscreen) {
        fullscreenRef.current.requestFullscreen();
      }
    }, 0);
  };

  // 退出全屏
  const handleClose = () => {
    setEnlarged(false);
    if (document.fullscreenElement) {
      document.exitFullscreen();
    }
    setTimeout(() => setShowFullscreen(false), ANIMATION_DURATION);
  };

  React.useEffect(() => {
    if (!showFullscreen) return;
    const onKeyDown = (e) => {
      if (e.key === 'Escape') {
        handleClose();
      }
    };
    window.addEventListener('keydown', onKeyDown);
    const onFullscreenChange = () => {
      if (!document.fullscreenElement) {
        handleClose();
      }
    };
    document.addEventListener('fullscreenchange', onFullscreenChange);
    return () => {
      window.removeEventListener('keydown', onKeyDown);
      document.removeEventListener('fullscreenchange', onFullscreenChange);
    };
  }, [showFullscreen]);

  // 关键：只在放大时传递 isIndividualFullscreen
  return (
    <>
      <div
        style={{ position: 'relative', ...style, height: '100%' }}
        onMouseEnter={() => setShowButton(true)}
        onMouseLeave={() => setShowButton(false)}
      >
        <button
          style={{
            position: 'absolute', top: 8, right: 8, zIndex: 999,
            background: 'rgba(255,255,255,0.8)', border: '1px solid #ccc', borderRadius: '50%',
            cursor: 'pointer', boxShadow: '0 2px 8px rgba(0,0,0,0.2)',
            display: 'flex', alignItems: 'center', justifyContent: 'center',
            width: 32, height: 32,
            opacity: showButton ? 1 : 0,
            pointerEvents: showButton ? 'auto' : 'none',
            transition: 'all 0.3s ease'
          }}
          onClick={handleEnlarge}
          title="全屏查看"
        >
          <ZoomInOutlined style={{ fontSize: 16, color: '#1783ff' }} />
        </button>
        <div style={{ width: '100%', height: '100%' }}>
          {children}
        </div>
      </div>
      {showFullscreen && (
        <div
          ref={fullscreenRef}
          style={{
            position: 'fixed', top: 0, left: 0, width: '100vw', height: '100vh',
            background: 'rgba(0,0,0,0.7)', zIndex: 9999, display: 'flex', flexDirection: 'column',
            alignItems: 'center', justifyContent: 'center',
            opacity: enlarged ? 1 : 0,
            transform: enlarged ? 'scale(1)' : 'scale(0.98)',
            transition: `opacity ${ANIMATION_DURATION}ms cubic-bezier(.4,0,.2,1), transform ${ANIMATION_DURATION}ms cubic-bezier(.4,0,.2,1)`
          }}
        >
          <div
            style={{
              background: '#fff', borderRadius: 8, maxWidth: '100vw', maxHeight: '100vh',
              width: '100vw', height: '100vh', overflow: 'auto', position: 'relative', boxShadow: '0 4px 24px rgba(0,0,0,0.3)',
              display: 'flex', alignItems: 'center', justifyContent: 'center',
              opacity: enlarged ? 1 : 0.96,
              transform: enlarged ? 'scale(1)' : 'scale(0.98)',
              transition: `opacity ${ANIMATION_DURATION}ms cubic-bezier(.4,0,.2,1), transform ${ANIMATION_DURATION}ms cubic-bezier(.4,0,.2,1)`
            }}
          >
            <div style={{ width: '100%', height: '100%', padding: '20px' }}>
              {cloneElement(children, {
                height: window.innerHeight - 40,
                width: '100%',
                isIndividualFullscreen: true
              })}
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default EnlargeableChartWrapper; 