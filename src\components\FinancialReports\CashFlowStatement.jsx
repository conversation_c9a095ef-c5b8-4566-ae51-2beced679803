import React from 'react';
import { Table } from 'antd';

/**
 * 现金流量表组件
 */
const CashFlowStatement = ({ data = [] }) => {
  // 使用从props传入的数据，如果未提供则使用空数组
  const displayData = Array.isArray(data) && data.length > 0 ? data : [
    // 保留一些默认结构或提示信息，以防数据为空
    {
      key: 'empty',
      item: '暂无数据',
      currentAmount: '',
      previousAmount: '',
      level: 0,
      rowType: 'header'
    }
  ];




  const columns = [
    {
      title: '项目',
      dataIndex: 'item',
      key: 'item',
      width: '60%',
      render: (text, record) => {
        let className = 'cash-flow-item';
        
        if (record.rowType === 'header') {
          className += ' header-item';
        } else if (record.rowType === 'subtotal') {
          className += ' subtotal-item';
        } else if (record.rowType === 'total') {
          className += ' total-item';
        }
        
        // 动态计算缩进，每个level使用4个空格
        const indent = '    '.repeat(Math.max(0, record.level || 0));
        // 对level=0或header行加粗
        const fontWeight = (record.level === 0 || record.rowType === 'header') ? 'bold' : 'normal';
        
        return (
          <span className={className} style={{ fontWeight }}>
            {indent}{text}
          </span>
        );
      }
    },
    {
      title: '本期金额',
      dataIndex: 'currentAmount',
      key: 'currentAmount',
      width: '20%',
      align: 'right',
      render: (text, record) => {
        let className = 'cash-flow-amount';
        
        if (record.rowType === 'header') {
          className += ' header-amount';
        } else if (record.rowType === 'subtotal') {
          className += ' subtotal-amount';
        } else if (record.rowType === 'total') {
          className += ' total-amount';
        }
        
        // 确保text是字符串
        const textStr = text !== undefined && text !== null ? String(text) : '';
        if (textStr.includes('(') && textStr.includes(')')) {
          className += ' negative-amount';
        }
        
        return (
          <span className={className}>
            {text}
          </span>
        );
      }
    },
    {
      title: '上期金额',
      dataIndex: 'previousAmount',
      key: 'previousAmount',
      width: '20%',
      align: 'right',
      render: (text, record) => {
        let className = 'cash-flow-amount';
        
        if (record.rowType === 'header') {
          className += ' header-amount';
        } else if (record.rowType === 'subtotal') {
          className += ' subtotal-amount';
        } else if (record.rowType === 'total') {
          className += ' total-amount';
        }
        
        // 确保text是字符串
        const textStr = text !== undefined && text !== null ? String(text) : '';
        if (textStr.includes('(') && textStr.includes(')')) {
          className += ' negative-amount';
        }
        
        return (
          <span className={className}>
            {text}
          </span>
        );
      }
    }
  ];

  return (
    <div className="cash-flow-statement">
      <Table
        columns={columns}
        dataSource={displayData}
        pagination={false}
        bordered
        size="small"
        className="cash-flow-statement-table"
        rowClassName={(record) => {
          if (record.rowType === 'header') return 'header-row';
          if (record.rowType === 'subtotal') return 'subtotal-row';
          if (record.rowType === 'total') return 'total-row';
          if (record.level === 1) return 'sub-row';
          if (record.level === 2) return 'sub-sub-row';
          return '';
        }}
      />
    </div>
  );
};

export default CashFlowStatement;
