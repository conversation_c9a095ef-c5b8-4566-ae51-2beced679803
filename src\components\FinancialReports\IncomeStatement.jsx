import React from 'react';
import { Table } from 'antd';

/**
 * 利润表组件
 */
const IncomeStatement = ({ data = [] }) => {
  // 使用从props传入的数据，如果未提供则使用空数组
  const displayData = Array.isArray(data) && data.length > 0 ? data : [
    {
      key: 'empty',
      item: '暂无数据',
      currentAmount: '',
      previousAmount: '',
      level: 0,
      rowType: 'header'
    }
  ];
    // 原有的数据结构被替换为 props.data，下面的代码保持不变，但会使用 displayData
    // const data = [
    //   {
    //     key: '1',
    //     item: '一、营业收入',
    //     currentAmount: '10,000,000.00',
    //     previousAmount: '9,500,000.00',
    //     level: 0,
    //     rowType: 'header'
    //   },
    //   {
    //     key: '2',
    //     item: '  减：营业成本',
    //     currentAmount: '6,000,000.00',
    //     previousAmount: '5,700,000.00',
    //     level: 1,
    //     rowType: 'sub'
    //   },
    //   {
    //     key: '3',
    //     item: '      税金及附加',
    //     currentAmount: '100,000.00',
    //     previousAmount: '95,000.00',
    //     level: 2,
    //     rowType: 'sub'
    //   },
    //   {
    //     key: '4',
    //     item: '      销售费用',
    //     currentAmount: '800,000.00',
    //     previousAmount: '760,000.00',
    //     level: 2,
    //     rowType: 'sub'
    //   },
    //   {
    //     key: '5',
    //     item: '      管理费用',
    //     currentAmount: '1,200,000.00',
    //     previousAmount: '1,140,000.00',
    //     level: 2,
    //     rowType: 'sub'
    //   },
    //   {
    //     key: '6',
    //     item: '      研发费用',
    //     currentAmount: '500,000.00',
    //     previousAmount: '475,000.00',
    //     level: 2,
    //     rowType: 'sub'
    //   },
    //   {
    //     key: '7',
    //     item: '      财务费用',
    //     currentAmount: '150,000.00',
    //     previousAmount: '142,500.00',
    //     level: 2,
    //     rowType: 'sub'
    //   },
    //   {
    //     key: '8',
    //     item: '        其中：利息费用',
    //     currentAmount: '120,000.00',
    //     previousAmount: '114,000.00',
    //     level: 3,
    //     rowType: 'sub'
    //   },
    //   {
    //     key: '9',
    //     item: '              利息收入',
    //     currentAmount: '20,000.00',
    //     previousAmount: '19,000.00',
    //     level: 3,
    //     rowType: 'sub'
    //   },
    //   {
    //     key: '10',
    //     item: '  加：其他收益',
    //     currentAmount: '50,000.00',
    //     previousAmount: '47,500.00',
    //     level: 1,
    //     rowType: 'sub'
    //   },
    //   {
    //     key: '11',
    //     item: '      投资收益',
    //     currentAmount: '100,000.00',
    //     previousAmount: '95,000.00',
    //     level: 2,
    //     rowType: 'sub'
    //   },
    //   {
    //     key: '12',
    //     item: '      公允价值变动收益',
    //     currentAmount: '30,000.00',
    //     previousAmount: '28,500.00',
    //     level: 2,
    //     rowType: 'sub'
    //   },
    //   {
    //     key: '13',
    //     item: '      信用减值损失',
    //     currentAmount: '-20,000.00',
    //     previousAmount: '-19,000.00',
    //     level: 2,
    //     rowType: 'sub'
    //   },
    //   {
    //     key: '14',
    //     item: '      资产减值损失',
    //     currentAmount: '-10,000.00',
    //     previousAmount: '-9,500.00',
    //     level: 2,
    //     rowType: 'sub'
    //   },
    //   {
    //     key: '15',
    //     item: '      资产处置收益',
    //     currentAmount: '5,000.00',
    //     previousAmount: '4,750.00',
    //     level: 2,
    //     rowType: 'sub'
    //   },
    //   {
    //     key: '16',
    //     item: '二、营业利润',
    //     currentAmount: '1,405,000.00',
    //     previousAmount: '1,334,750.00',
    //     level: 0,
    //     rowType: 'subtotal'
    //   },
    //   {
    //     key: '17',
    //     item: '  加：营业外收入',
    //     currentAmount: '25,000.00',
    //     previousAmount: '23,750.00',
    //     level: 1,
    //     rowType: 'sub'
    //   },
    //   {
    //     key: '18',
    //     item: '  减：营业外支出',
    //     currentAmount: '15,000.00',
    //     previousAmount: '14,250.00',
    //     level: 1,
    //     rowType: 'sub'
    //   },
    //   {
    //     key: '19',
    //     item: '三、利润总额',
    //     currentAmount: '1,415,000.00',
    //     previousAmount: '1,344,250.00',
    //     level: 0,
    //     rowType: 'subtotal'
    //   },
    //   {
    //     key: '20',
    //     item: '  减：所得税费用',
    //     currentAmount: '353,750.00',
    //     previousAmount: '336,062.50',
    //     level: 1,
    //     rowType: 'sub'
    //   },
    //   {
    //     key: '21',
    //     item: '四、净利润',
    //     currentAmount: '1,061,250.00',
    //     previousAmount: '1,008,187.50',
    //     level: 0,
    //     rowType: 'total'
    //   },
    //   {
    //     key: '22',
    //     item: '（一）持续经营净利润',
    //     currentAmount: '1,061,250.00',
    //     previousAmount: '1,008,187.50',
    //     level: 1,
    //     rowType: 'sub'
    //   },
    //   {
    //     key: '23',
    //     item: '（二）终止经营净利润',
    //     currentAmount: '',
    //     previousAmount: '',
    //     level: 1,
    //     rowType: 'sub'
    //   },
    //   {
    //     key: '24',
    //     item: '五、其他综合收益的税后净额',
    //     currentAmount: '10,000.00',
    //     previousAmount: '9,500.00',
    //     level: 0,
    //     rowType: 'header'
    //   },
    //   {
    //     key: '25',
    //     item: '（一）不能重分类进损益的其他综合收益',
    //     currentAmount: '5,000.00',
    //     previousAmount: '4,750.00',
    //     level: 1,
    //     rowType: 'sub'
    //   },
    //   {
    //     key: '26',
    //     item: '  1.重新计量设定受益计划变动额',
    //     currentAmount: '5,000.00',
    //     previousAmount: '4,750.00',
    //     level: 2,
    //     rowType: 'sub'
    //   },
    //   {
    //     key: '27',
    //     item: '（二）将重分类进损益的其他综合收益',
    //     currentAmount: '5,000.00',
    //     previousAmount: '4,750.00',
    //     level: 1,
    //     rowType: 'sub'
    //   },
    //   {
    //     key: '28',
    //     item: '  1.可供出售金融资产公允价值变动损益',
    //     currentAmount: '5,000.00',
    //     previousAmount: '4,750.00',
    //     level: 2,
    //     rowType: 'sub'
    //   },
    //   {
    //     key: '29',
    //     item: '六、综合收益总额',
    //     currentAmount: '1,071,250.00',
    //     previousAmount: '1,017,687.50',
    //     level: 0,
    //     rowType: 'total'
    //   },
    //   {
    //     key: '30',
    //     item: '七、每股收益：',
    //     currentAmount: '',
    //     previousAmount: '',
    //     level: 0,
    //     rowType: 'header'
    //   },
    //   {
    //     key: '31',
    //     item: '（一）基本每股收益',
    //     currentAmount: '0.21',
    //     previousAmount: '0.20',
    //     level: 1,
    //     rowType: 'sub'
    //   },
    //   {
    //     key: '32',
    //     item: '（二）稀释每股收益',
    //     currentAmount: '0.21',
    //     previousAmount: '0.20',
    //     level: 1,
    //     rowType: 'sub'
    //   }
    // ];
  // 使用从props传入的数据，如果未提供则使用空数组（已在文件开头声明）



  const columns = [
    {
      title: '项目',
      dataIndex: 'item',
      key: 'item',
      width: '50%',
      render: (text, record) => {
        let className = 'income-statement-item';
        if (record.rowType === 'header') {
          className += ' header-item';
        } else if (record.rowType === 'subtotal') {
          className += ' subtotal-item';
        } else if (record.rowType === 'total') {
          className += ' total-item';
        }
        
        // 动态计算缩进，每个level使用4个空格
        const indent = '    '.repeat(Math.max(0, record.level || 0));
        // 对标题行(header)和一级标题(level=0)加粗
        const fontWeight = (record.level === 0 || record.rowType === 'header') ? 'bold' : 'normal';
        
        return (
          <span className={className} style={{ fontWeight }}>
            {indent}{text}
          </span>
        );
      }
    },
    {
      title: '本期金额',
      dataIndex: 'currentAmount',
      key: 'currentAmount',
      width: '25%',
      align: 'right',
      render: (text, record) => {
        let className = 'income-statement-amount';
        if (record.rowType === 'header') {
          className += ' header-amount';
        } else if (record.rowType === 'subtotal') {
          className += ' subtotal-amount';
        } else if (record.rowType === 'total') {
          className += ' total-amount';
        }
        
        return (
          <span className={className}>
            {text}
          </span>
        );
      }
    },
    {
      title: '上期金额',
      dataIndex: 'previousAmount',
      key: 'previousAmount',
      width: '25%',
      align: 'right',
      render: (text, record) => {
        let className = 'income-statement-amount';
        if (record.rowType === 'header') {
          className += ' header-amount';
        } else if (record.rowType === 'subtotal') {
          className += ' subtotal-amount';
        } else if (record.rowType === 'total') {
          className += ' total-amount';
        }
        
        return (
          <span className={className}>
            {text}
          </span>
        );
      }
    }
  ];

  return (
    <div className="income-statement">
      <Table
        columns={columns}
        dataSource={displayData}
        pagination={false}
        bordered
        size="small"
        className="income-statement-table"
        rowClassName={(record) => {
          if (record.rowType === 'header') return 'header-row';
          if (record.rowType === 'subtotal') return 'subtotal-row';
          if (record.rowType === 'total') return 'total-row';
          if (record.level === 1) return 'sub-row';
          if (record.level === 2) return 'sub-sub-row';
          if (record.level === 3) return 'sub-sub-sub-row';
          return '';
        }}
      />
    </div>
  );
};

export default IncomeStatement;
