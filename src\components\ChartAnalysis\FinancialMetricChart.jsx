import React, { useEffect, useRef } from 'react';
import { Chart } from '@antv/g2';

const FinancialMetricChart = ({ 
  title, 
  data, 
  year = '2024', 
  allCompanies = [],
  companyColorMap = {},
  height = 400
}) => {
  const chartRef = useRef(null);

  useEffect(() => {
    if (!chartRef.current || !data || data.length === 0) return;
    chartRef.current.innerHTML = '';
    const chart = new Chart({
      container: chartRef.current,
      autoFit: true,
      height,
    });
    // 配置颜色域
    const domain = allCompanies;
    const range = allCompanies.map(id => companyColorMap[id]);
    chart
      .interval()
      .data(data)
      .encode('x', 'category')
      .encode('y', 'value')
      .encode('color', 'category')
      .scale('color', { domain, range })
      .label({
        text: (d) => {
          if (!d) return '';
          if (d.displayValue !== undefined && d.displayValue !== null && d.displayValue !== '') {
            return d.displayValue;
          }
          if (d.value !== undefined && d.value !== null) {
            return Number(d.value).toFixed(2);
          }
          return '';
        },
        position: 'top',
        style: {
          fill: '#000',
          fontSize: 12,
          fontWeight: 'bold',
        },
      })
      .axis('y', {
        grid: true,
        gridLineWidth: 1,
        gridStroke: '#f0f0f0',
        labelFormatter: (d) => d,
      })
      .axis('x', {
        labelFill: '#666',
        labelFontSize: 12,
      })
      .legend('color', {
        position: 'top',
        itemLabelFill: '#666',
        itemLabelFontSize: 12,
      })
      .tooltip({
        title: (d) => d?.category || '',
        items: [
          {
            field: 'value',
            name: '数值',
            valueFormatter: (d, datum) => {
              if (datum && datum.displayValue !== undefined && datum.displayValue !== null && datum.displayValue !== '') {
                return datum.displayValue;
              }
              if (d !== undefined && d !== null) {
                return Number(d).toFixed(2);
              }
              return '';
            },
          },
        ],
      });
    chart.render();
    return () => chart.destroy();
  }, [data, allCompanies, companyColorMap, height]);

  if (!data || data.length === 0) {
    return (
      <div style={{ textAlign: 'center', padding: '50px 0', color: '#999', background: '#fff', borderRadius: 8 }}>
        暂无数据
      </div>
    );
  }

  return (
    <div style={{ position: 'relative', background: '#fff', borderRadius: 8, boxShadow: '0 2px 8px #0001', padding: 16 }}>
      <div style={{ textAlign: 'center', marginBottom: 8, fontWeight: 'bold', fontSize: 18 }}>{title}</div>
      <div ref={chartRef} style={{ width: '100%', height }} />
      <div style={{ textAlign: 'center', marginTop: 10, fontSize: 16, color: '#666' }}>{year}</div>
    </div>
  );
};

export default FinancialMetricChart;