from flask import Blueprint, jsonify, current_app
from ..services.report_time_service import ReportTimeService
from ..utils.error_handler import handle_exception

bp = Blueprint('report_times', __name__, url_prefix='/api/report-times')

@bp.route('', methods=['GET'])
def get_report_times():
    """获取定期报告公布时间"""
    try:
        report_times = ReportTimeService.get_report_times()
        if isinstance(report_times, dict) and 'error' in report_times:
            current_app.logger.error(f"Service error getting report times: {report_times['error']}")
            return jsonify(report_times), 500
        return jsonify(report_times)
    except Exception as e:
        error_response, status_code = handle_exception(e, "获取报告时间")
        return jsonify(error_response), status_code

@bp.route('/latest-l2metrics', methods=['GET'])
def get_latest_l2metrics_report_date():
    """获取L2Metrics表中最新的报告日期"""
    try:
        latest_date = ReportTimeService.get_latest_l2metrics_report_date()
        if isinstance(latest_date, dict) and 'error' in latest_date:
            current_app.logger.error(f"Service error getting latest L2Metrics report date: {latest_date['error']}")
            return jsonify(latest_date), 500
        return jsonify(latest_date)
    except Exception as e:
        error_response, status_code = handle_exception(e, "获取最新L2Metrics报告日期")
        return jsonify(error_response), status_code 