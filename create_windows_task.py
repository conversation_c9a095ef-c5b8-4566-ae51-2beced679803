#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Windows任务计划创建脚本
用于创建定时运行整合程序的Windows任务计划
"""

import os
import subprocess
import sys
from datetime import datetime

def create_task_scheduler_script():
    """创建任务计划脚本"""
    
    # 获取当前目录的绝对路径
    current_dir = os.path.abspath(os.getcwd())
    exe_path = os.path.join(current_dir, "integrated_processor.exe")
    
    # 创建批处理文件
    batch_content = f"""@echo off
chcp 65001 >nul
cd /d "{current_dir}"

echo ========================================
echo 整合数据处理程序 - 定时任务执行
echo 执行时间: %date% %time%
echo ========================================

REM 记录开始时间
echo 任务开始执行: %date% %time% >> task_log.txt

REM 执行程序
integrated_processor.exe

REM 记录结束时间
echo 任务执行完成: %date% %time% >> task_log.txt
echo. >> task_log.txt

REM 如果程序执行失败，记录错误
if errorlevel 1 (
    echo 任务执行失败，错误代码: %errorlevel% >> task_log.txt
    echo. >> task_log.txt
)
"""
    
    batch_file = "scheduled_task.bat"
    with open(batch_file, 'w', encoding='utf-8') as f:
        f.write(batch_content)
    
    print(f"✓ 创建批处理文件: {batch_file}")
    return batch_file

def create_task_xml():
    """创建任务计划XML文件"""
    
    current_dir = os.path.abspath(os.getcwd())
    batch_file = os.path.join(current_dir, "scheduled_task.bat")
    
    # 获取当前用户
    username = os.getenv('USERNAME')
    domain = os.getenv('USERDOMAIN')
    
    xml_content = f"""<?xml version="1.0" encoding="UTF-16"?>
<Task version="1.4" xmlns="http://schemas.microsoft.com/windows/2004/02/mit/task">
  <Triggers>
    <CalendarTrigger>
      <Repetition>
        <Interval>PT1H</Interval>
        <StopAtDurationEnd>false</StopAtDurationEnd>
      </Repetition>
      <StartBoundary>2025-08-05T09:00:00</StartBoundary>
      <Enabled>true</Enabled>
      <ScheduleByDay>
        <DaysInterval>1</DaysInterval>
      </ScheduleByDay>
    </CalendarTrigger>
  </Triggers>
  <Principals>
    <Principal id="Author">
      <UserId>{domain}\\{username}</UserId>
      <LogonType>InteractiveToken</LogonType>
      <RunLevel>HighestAvailable</RunLevel>
    </Principal>
  </Principals>
  <Settings>
    <MultipleInstancesPolicy>IgnoreNew</MultipleInstancesPolicy>
    <DisallowStartIfOnBatteries>false</DisallowStartIfOnBatteries>
    <StopIfGoingOnBatteries>false</StopIfGoingOnBatteries>
    <AllowHardTerminate>true</AllowHardTerminate>
    <StartWhenAvailable>true</StartWhenAvailable>
    <RunOnlyIfNetworkAvailable>false</RunOnlyIfNetworkAvailable>
    <IdleSettings>
      <StopOnIdleEnd>false</StopOnIdleEnd>
      <RestartOnIdle>false</RestartOnIdle>
    </IdleSettings>
    <AllowStartOnDemand>true</AllowStartOnDemand>
    <Enabled>true</Enabled>
    <Hidden>false</Hidden>
    <RunOnlyIfIdle>false</RunOnlyIfIdle>
    <WakeToRun>false</WakeToRun>
    <ExecutionTimeLimit>PT4H</ExecutionTimeLimit>
    <Priority>7</Priority>
  </Settings>
  <Actions Context="Author">
    <Exec>
      <Command>cmd.exe</Command>
      <Arguments>/c "{batch_file}"</Arguments>
      <WorkingDirectory>{current_dir}</WorkingDirectory>
    </Exec>
  </Actions>
</Task>"""
    
    xml_file = "integrated_processor_task.xml"
    with open(xml_file, 'w', encoding='utf-8') as f:
        f.write(xml_content)
    
    print(f"✓ 创建任务计划XML文件: {xml_file}")
    return xml_file

def create_manual_scripts():
    """创建手动管理脚本"""
    
    # 创建注册任务脚本
    register_content = """@echo off
chcp 65001 >nul
echo 注册Windows任务计划...
echo.

REM 删除已存在的任务（如果有）
schtasks /delete /tn "整合数据处理程序" /f 2>nul

REM 注册新任务
schtasks /create /tn "整合数据处理程序" /xml "integrated_processor_task.xml"

if errorlevel 1 (
    echo 任务注册失败！
    echo 请以管理员身份运行此脚本。
    pause
) else (
    echo 任务注册成功！
    echo 任务名称: 整合数据处理程序
    echo 执行频率: 每天上午9点开始，每小时执行一次
    echo.
    echo 您可以在"任务计划程序"中查看和管理此任务。
)

pause
"""
    
    register_file = "注册任务计划.bat"
    with open(register_file, 'w', encoding='utf-8') as f:
        f.write(register_content)
    print(f"✓ 创建注册脚本: {register_file}")
    
    # 创建删除任务脚本
    delete_content = """@echo off
chcp 65001 >nul
echo 删除Windows任务计划...
echo.

schtasks /delete /tn "整合数据处理程序" /f

if errorlevel 1 (
    echo 任务删除失败！
    echo 可能任务不存在或需要管理员权限。
) else (
    echo 任务删除成功！
)

pause
"""
    
    delete_file = "删除任务计划.bat"
    with open(delete_file, 'w', encoding='utf-8') as f:
        f.write(delete_content)
    print(f"✓ 创建删除脚本: {delete_file}")
    
    # 创建查看任务脚本
    view_content = """@echo off
chcp 65001 >nul
echo 查看Windows任务计划状态...
echo.

schtasks /query /tn "整合数据处理程序" /fo table

echo.
echo 任务日志文件: task_log.txt
if exist task_log.txt (
    echo 最近的执行记录:
    echo ----------------------------------------
    type task_log.txt | findstr /c:"任务开始执行" /c:"任务执行完成" /c:"任务执行失败" | tail -10
) else (
    echo 暂无执行记录
)

pause
"""
    
    view_file = "查看任务状态.bat"
    with open(view_file, 'w', encoding='utf-8') as f:
        f.write(view_content)
    print(f"✓ 创建查看脚本: {view_file}")

def create_readme():
    """创建任务计划说明文件"""
    
    readme_content = """Windows任务计划使用说明
===============================

本脚本用于创建Windows任务计划，自动定时执行整合数据处理程序。

文件说明:
- scheduled_task.bat: 任务执行的批处理文件
- integrated_processor_task.xml: 任务计划配置文件
- 注册任务计划.bat: 注册Windows任务计划
- 删除任务计划.bat: 删除Windows任务计划
- 查看任务状态.bat: 查看任务执行状态
- task_log.txt: 任务执行日志（自动生成）

任务配置:
- 任务名称: 整合数据处理程序
- 执行频率: 每天上午9点开始，每小时执行一次
- 执行程序: integrated_processor.exe
- 工作目录: 当前目录

使用步骤:
1. 以管理员身份运行"注册任务计划.bat"
2. 系统会自动创建Windows任务计划
3. 任务会在每天上午9点开始，每小时自动执行一次
4. 执行日志会记录在task_log.txt文件中

管理任务:
- 查看任务状态: 运行"查看任务状态.bat"
- 删除任务: 运行"删除任务计划.bat"
- 手动管理: 打开"任务计划程序"（taskschd.msc）

注意事项:
1. 必须以管理员身份运行注册脚本
2. 确保Wind金融终端在任务执行时已启动
3. 确保数据库连接正常
4. 任务执行时间较长，请耐心等待
5. 可以通过修改XML文件调整执行频率

自定义配置:
如需修改执行频率，请编辑integrated_processor_task.xml文件：
- PT1H: 每小时执行一次
- PT30M: 每30分钟执行一次
- PT2H: 每2小时执行一次

技术支持:
如有问题，请查看task_log.txt日志文件或联系技术支持。
"""
    
    readme_file = "任务计划说明.txt"
    with open(readme_file, 'w', encoding='utf-8') as f:
        f.write(readme_content)
    print(f"✓ 创建说明文件: {readme_file}")

def main():
    """主函数"""
    print("=" * 60)
    print("Windows任务计划创建工具")
    print("=" * 60)
    print(f"创建时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"工作目录: {os.getcwd()}")
    print("-" * 60)
    
    try:
        # 创建批处理文件
        print("创建任务执行脚本...")
        create_task_scheduler_script()
        
        # 创建XML配置文件
        print("\n创建任务计划配置...")
        create_task_xml()
        
        # 创建管理脚本
        print("\n创建管理脚本...")
        create_manual_scripts()
        
        # 创建说明文件
        print("\n创建说明文件...")
        create_readme()
        
        print("\n" + "=" * 60)
        print("✓ 任务计划文件创建完成!")
        print("\n使用说明:")
        print("1. 以管理员身份运行'注册任务计划.bat'")
        print("2. 任务将每天上午9点开始，每小时自动执行")
        print("3. 执行日志记录在task_log.txt中")
        print("4. 使用'查看任务状态.bat'查看执行情况")
        print("\n注意: 必须以管理员身份运行注册脚本!")
        
    except Exception as e:
        print(f"✗ 创建失败: {e}")
        return False
    
    return True

if __name__ == "__main__":
    main() 