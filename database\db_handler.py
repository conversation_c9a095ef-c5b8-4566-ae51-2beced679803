from contextlib import contextmanager
import json
import pyodbc
import threading
from datetime import datetime
from typing import List, Optional
from .models import Company, FinancialReport
from config import Config

class DatabaseHandler:
    _pool = None
    _lock = threading.Lock()

    def __init__(self):
        myconfig=Config()
        self.config = myconfig.DB_CONFIG
        self.connection = None  # 当前实例使用的数据库连接

        # 初始化连接池（线程安全）
        with DatabaseHandler._lock:
            if not DatabaseHandler._pool:
                self._create_pool()

    def _create_pool(self):
        """创建包含5个连接的连接池"""
        DatabaseHandler._pool = [
            pyodbc.connect(
                f"DRIVER={self.config['driver']};"
                f"SERVER={self.config['server']};"
                f"DATABASE={self.config['database']};"
                f"UID={self.config['user']};"
                f"PWD={self.config['password']}"
            ) for _ in range(50)
        ]

    def __enter__(self):
        """上下文管理入口：从池中获取连接并返回实例"""
        with DatabaseHandler._lock:
            if not DatabaseHandler._pool:
                raise RuntimeError("数据库连接池已耗尽")
            self.connection = DatabaseHandler._pool.pop()
        return self  # 关键修改：返回实例本身

    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理出口：归还连接到池"""
        if self.connection:
            with DatabaseHandler._lock:
                DatabaseHandler._pool.append(self.connection)
            self.connection = None

    @staticmethod
    def load_json(file_path: str) -> dict:
        """加载JSON文件（完全保留原有实现）"""
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)

    def store_append_data(self, data1):
        """增量存储财务数据（完整保留原有逻辑）"""
        self._validate_connection()

        cursor = None
        try:
            cursor = self.connection.cursor()
            data = data1['result']['data']
            company_id = data1["company_id"]

            # 获取当前最大日期
            cursor.execute(
                "SELECT MAX(DateValue) FROM FinancialReport WHERE CompanyID = ?",
                (company_id,)
            )
            max_db_date = cursor.fetchone()[0]

            # 过滤需要插入的日期
            new_date_items = [
                item for item in data['report_date']
                if max_db_date is None or item['date_value'] > max_db_date
            ]

            if not new_date_items:
                return

            # 准备批量插入数据
            params_list = []
            for date_item in new_date_items:
                date_value = date_item['date_value']
                report = data['report_list'].get(date_value)
                if not report:
                    continue

                params = (
                    company_id,
                    date_value,
                    date_item['date_description'],
                    date_item['date_type'],
                    report.get('rType'),
                    report.get('rCurrency'),
                    report.get('data_source'),
                    report.get('is_audit'),
                    report.get('publish_date'),
                    1 if report.get('is_exist_yoy') else 0,
                    json.dumps(report['data'], ensure_ascii=False),
                    company_id,
                    date_value
                )
                params_list.append(params)

            # 执行批量插入
            insert_sql = '''
            INSERT INTO FinancialReport (
                CompanyID,
                DateValue,
                DateDescription,
                DateType,
                RType,
                RCurrency,
                DataSource,
                IsAudit,
                PublishDate,
                IsExistYoy,
                ReportData
            )
            SELECT ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?
            WHERE NOT EXISTS (
                SELECT 1 FROM FinancialReport 
                WHERE CompanyID = ? AND DateValue = ?
            )
            '''
            if params_list:
                cursor.executemany(insert_sql, params_list)
                self.connection.commit()

        except Exception as e:
            self.connection.rollback()
            raise RuntimeError(f"增量存储失败: {str(e)}")
        finally:
            if cursor:
                cursor.close()

    def store_to_sql(self, data1):
        """全量存储财务数据（完整MERGE逻辑）"""
        self._validate_connection()

        cursor = None
        try:
            cursor = self.connection.cursor()
            data = data1['result']['data']

            for date_item in data['report_date']:
                date_value = date_item['date_value']
                report = data['report_list'].get(date_value)
                if not report:
                    continue

                # 准备MERGE数据
                merge_data = (
                    data1["company_id"],        # CompanyID
                    date_value,                 # DateValue
                    date_item['date_description'],  # DateDescription
                    date_item['date_type'],      # DateType
                    report.get('rType'),        # RType
                    report.get('rCurrency'),    # RCurrency
                    report.get('data_source'),  # DataSource
                    report.get('is_audit'),     # IsAudit
                    report.get('publish_date'),  # PublishDate
                    1 if report.get('is_exist_yoy') else 0,  # IsExistYoy
                    # ReportData
                    json.dumps(report['data'], ensure_ascii=False),
                )

                # 执行MERGE操作
                merge_sql = '''
                MERGE INTO FinancialReport AS target
                USING (VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)) 
                    AS source(
                        CompanyID,
                        DateValue,
                        DateDescription,
                        DateType,
                        RType,
                        RCurrency,
                        DataSource,
                        IsAudit,
                        PublishDate,
                        IsExistYoy,
                        ReportData
                    )
                ON target.CompanyID = source.CompanyID
                    AND target.DateValue = source.DateValue
                WHEN MATCHED THEN
                    UPDATE SET 
                        DateDescription = source.DateDescription,
                        DateType = source.DateType,
                        RType = source.RType,
                        RCurrency = source.RCurrency,
                        DataSource = source.DataSource,
                        IsAudit = source.IsAudit,
                        PublishDate = source.PublishDate,
                        IsExistYoy = source.IsExistYoy,
                        ReportData = source.ReportData
                WHEN NOT MATCHED THEN
                    INSERT (
                        CompanyID,
                        DateValue,
                        DateDescription,
                        DateType,
                        RType,
                        RCurrency,
                        DataSource,
                        IsAudit,
                        PublishDate,
                        IsExistYoy,
                        ReportData
                    )
                    VALUES (
                        source.CompanyID,
                        source.DateValue,
                        source.DateDescription,
                        source.DateType,
                        source.RType,
                        source.RCurrency,
                        source.DataSource,
                        source.IsAudit,
                        source.PublishDate,
                        source.IsExistYoy,
                        source.ReportData
                    );
                '''
                cursor.execute(merge_sql, merge_data)

            self.connection.commit()
        except Exception as e:
            self.connection.rollback()
            raise RuntimeError(f"全量存储失败: {str(e)}")
        finally:
            if cursor:
                cursor.close()

    def save_financial_report(self, report: FinancialReport):
        """保存单个财务报告（完整保留ORM逻辑）"""
        self._validate_connection()

        cursor = None
        try:
            cursor = self.connection.cursor()
            cursor.execute('''
                MERGE INTO FinancialReport AS target
                USING (VALUES (?, ?, ?, ?, ?)) 
                    AS source (
                        CompanyID, 
                        DateValue, 
                        ReportDate, 
                        ReportType, 
                        ReportData
                    )
                ON target.CompanyID = source.CompanyID 
                    AND target.DateValue = source.DateValue
                WHEN MATCHED THEN
                    UPDATE SET 
                        ReportDate = source.ReportDate,
                        ReportType = source.ReportType,
                        ReportData = source.ReportData
                WHEN NOT MATCHED THEN
                    INSERT (
                        CompanyID, 
                        DateValue, 
                        ReportDate, 
                        ReportType, 
                        ReportData
                    )
                    VALUES (
                        source.CompanyID, 
                        source.DateValue, 
                        source.ReportDate, 
                        source.ReportType, 
                        source.ReportData
                    );
            ''', (
                report.company_id,
                report.date_value,
                report.report_date,
                report.report_type,
                report.raw_data
            ))
            self.connection.commit()
        except Exception as e:
            self.connection.rollback()
            raise RuntimeError(f"财务报告保存失败: {str(e)}")
        finally:
            if cursor:
                cursor.close()

    def get_active_companies(self) -> List[Company]:
        """获取活跃公司列表（完整ORM转换）"""
        self._validate_connection()

        cursor = None
        try:
            cursor = self.connection.cursor()
            cursor.execute('''
                SELECT 
                    CompanyID, 
                    StockCode, 
                    CompanyName 
                FROM CompanyInfo 
                WHERE IsActive = 1
            ''')

            return [
                Company(
                    company_id=row.CompanyID,
                    stock_code=row.StockCode,
                    name=row.CompanyName
                ) for row in cursor.fetchall()
            ]
        finally:
            if cursor:
                cursor.close()

    def get_company_by_code(self, stock_code: str) -> Optional[Company]:
        """根据股票代码查询公司（完整ORM转换）"""
        self._validate_connection()

        cursor = None
        try:
            cursor = self.connection.cursor()
            cursor.execute('''
                SELECT 
                    CompanyID, 
                    StockCode, 
                    CompanyName 
                FROM CompanyInfo 
                WHERE StockCode = ?
            ''', (stock_code,))

            row = cursor.fetchone()
            return Company(
                company_id=row.CompanyID,
                stock_code=row.StockCode,
                name=row.CompanyName
            ) if row else None
        finally:
            if cursor:
                cursor.close()

    def _validate_connection(self):
        """验证连接有效性"""
        if not self.connection or self.connection.closed:
            raise RuntimeError("数据库连接未初始化或已关闭")
