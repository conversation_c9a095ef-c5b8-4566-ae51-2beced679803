import React from 'react';
import AssetStackedColumn from './AssetStackedColumn';
import DebtStackedColumn from './DebtStackedColumn';
import CustomLine from './CustomLine';
import CustomBar from './CustomBar';
import CustomRadar from './CustomRadar';
import HorizontalBar from './HorizontalBar';
import TwoFundIncomePanel from './TwoFundIncomePanel';
import CashFlowTable from './CashFlowTable'
import TwoFundAnalysisPanel from './TwoFundAnalysisPanel';
import CashHorizontalBar from './CashHorizontalBar';
import CustomBarWithLine from './CustomBarWithLine';
import CustomMixChart from './CustomMixChart';
import EnlargeableChartWrapper from './EnlargeableChartWrapper';
import TurnoverToggleChart from './TurnoverToggleChart';
import CollectionRatioToggleChart from './CollectionRatioToggleChart';

const ChartCard = ({ title }) => (
  <div style={{
    background: '#fff',
    borderRadius: 8,
    boxShadow: '0 2px 8px #0001',
    padding: 16,
    minHeight: 180,
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    fontWeight: 'bold',
    fontSize: 16,
    color: '#1783FF',
    textAlign: 'center',
  }}>
    {title}
  </div>
);

const DashboardLayout = ({
  barData = [],
  radarData = [],
  radarIndicators = [],
  companyColorMap = {},
  allCompanies = [],
  assetStackData = [],
  debtStackData = [],
  debtToAssetsData = [],
  totalAssetTurnoverData = [],
  equityTurnoverData = [],
  grossMarginData = [],
  threeFeeRatioData = [],
  netProfitMarginData = [],
  roeBasicData = [],
  twoFundNetData = [],
  inventoryTurnoverData = [],
  receivablesTurnoverData = [],
  netCashFlowsOperActData = [],
  cashCycleData = [],
  revenuePerRdPersonnelData = [],
  cashCollectionRatioData = [],
  rdInvestData = [],
  rdExpData = [],
  revenuePerPersonData = [],
  divAualCashDividendData = [],
  employeeData = [],
  nonRecurringProfitLossData = [],
  twoFundIncomeData = [],
  cashFlowData = [],
  isFullscreen = false,
}) => {
  // 从嵌套数据中提取主指标数据（营业收入、利润总额、扣非归母净利润）
  const getMainIndicatorData = (data, indicatorName) => {
    const chartData = data.find(chart => chart.title === indicatorName);
    return chartData ? chartData.data : [];
  };

  const operRevData = getMainIndicatorData(barData, '营业收入');
  const profitData = getMainIndicatorData(barData, '利润总额');
  const deductedProfitData = getMainIndicatorData(barData, '扣非归母净利润');

  // 计算缩放比例，用于响应式调整字体大小 
  const scale = isFullscreen ? 1 : 1; // 整体全屏时不放大，保持原有大小
  const getChartHeight = (h) => Math.round(h * scale);
  const getFontSize = (f) => Math.round(f * scale);

  return (
    <div style={{ width: '100%', padding: 5, height: isFullscreen ? '100vh' : undefined, minHeight: isFullscreen ? '100vh' : undefined, boxSizing: 'border-box' }}>
      <div style={{ display: 'flex', alignItems: 'flex-start', width: '100%', margin: '0 auto 32px auto' }}>
        {/* 左侧列 */}
        <div style={{ flex: 1, display: 'flex', flexDirection: 'column', gap: 16 }}>
          {/* 顶部主图区 1行4列 50%宽度 */}
          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(4, 1fr)', width: '100%', height: getChartHeight(320), borderBottom: '2px solid #e0e0e0' }}>
            <EnlargeableChartWrapper title="营业收入">
              <CustomBar data={operRevData} title="营业收入" height={getChartHeight(320)} fontSize={getFontSize(14)} isFullscreen={isFullscreen} />
            </EnlargeableChartWrapper>
            <EnlargeableChartWrapper title="利润总额">
              <CustomBar data={profitData} title="利润总额" height={getChartHeight(320)} fontSize={getFontSize(14)} isFullscreen={isFullscreen} />
            </EnlargeableChartWrapper>
            <EnlargeableChartWrapper title="扣非归母净利润">
              <CustomBar data={deductedProfitData} title="扣非归母净利润" height={getChartHeight(320)} fontSize={getFontSize(14)} isFullscreen={isFullscreen} />
            </EnlargeableChartWrapper>
            <EnlargeableChartWrapper title="财务雷达图">
              <CustomRadar data={radarData} title="财务雷达图" height={getChartHeight(320)} fontSize={getFontSize(14)} isFullscreen={isFullscreen} />
            </EnlargeableChartWrapper>
          </div>
          {/* 灰色横线 */}
          {/* 分区2：毛利率趋势、三费费率趋势、归母净利率趋势、净资产收益率趋势 */}
          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(4, 1fr)', width: '100%', borderBottom: '2px solid #e0e0e0' }}>
            <EnlargeableChartWrapper title="毛利率趋势">
              <CustomLine data={grossMarginData} title="毛利率趋势" height={getChartHeight(285)} fontSize={getFontSize(14)} isFullscreen={isFullscreen} />
            </EnlargeableChartWrapper>
            <EnlargeableChartWrapper title="三费费率趋势">
              <CustomLine data={threeFeeRatioData} title="三费费率趋势" height={getChartHeight(285)} fontSize={getFontSize(14)} isFullscreen={isFullscreen} />
            </EnlargeableChartWrapper>
            <EnlargeableChartWrapper title="归母净利率趋势">
              <CustomLine data={netProfitMarginData} title="归母净利率趋势" height={getChartHeight(285)} fontSize={getFontSize(14)} isFullscreen={isFullscreen} />
            </EnlargeableChartWrapper>
            <EnlargeableChartWrapper title="净资产收益率趋势">
              <CustomLine data={roeBasicData} title="净资产收益率趋势" height={getChartHeight(285)} fontSize={getFontSize(14)} isFullscreen={isFullscreen} />
            </EnlargeableChartWrapper>
          </div>
          {/* 分区4补充：现金流和营业收现率 */}
          <div style={{ display: 'flex', flexDirection: 'row', gap: 16, height: getChartHeight(200) }}>
            <EnlargeableChartWrapper title="现金流量表" style={{ flex: 1, height: '100%' }}>
              <CashFlowTable data={cashFlowData} height={getChartHeight(200)} fontSize={getFontSize(14)} isFullscreen={isFullscreen} />
            </EnlargeableChartWrapper>
            <div style={{ flex: 1, height: '100%' }}>
              <CollectionRatioToggleChart
                revenuePerRdPersonnelData={revenuePerRdPersonnelData}
                cashCollectionRatioData={cashCollectionRatioData}
                height={getChartHeight(200)}
                fontSize={getFontSize(14)}
                isFullscreen={isFullscreen}
              />
            </div>
          </div>
          <div style={{ display: 'flex', flexDirection: 'row', gap: 16, height: getChartHeight(420) }}>
            <EnlargeableChartWrapper title="经营活动产生的现金流量净额" style={{ flex: 1, height: '100%' }}>
              <CashHorizontalBar height={getChartHeight(400)}
                data={netCashFlowsOperActData}
                title="经营活动产生的现金流量净额"
                fontSize={getFontSize(14)}
                isFullscreen={isFullscreen}
              />
            </EnlargeableChartWrapper>
            <EnlargeableChartWrapper title="现金周期" style={{ flex: 1, height: '100%' }} >
              <CustomLine data={cashCycleData} title="现金周期（天）" height={getChartHeight(400)} fontSize={getFontSize(14)} isFullscreen={isFullscreen} />
            </EnlargeableChartWrapper>
          </div>
        </div>
        {/* 中间虚线分割线 */}
        <div
          style={{
            width: 2,
            alignSelf: 'stretch',
            height: 'auto',
            background: 'repeating-linear-gradient(to bottom, #6fa8dc99, #6fa8dc99 6px, transparent 6px, transparent 12px)',
            margin: '0 24px',
            borderRadius: 2,
          }}
        />
        {/* 右侧列 */}
        <div style={{ flex: 1, display: 'flex', flexDirection: 'column', gap: 16 }}>
          {/* 分区1：资产结构、负债结构、资产负债率、总资产周转率 */}
          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(4, 1fr)', width: '100%', height: getChartHeight(320), }}>
            <EnlargeableChartWrapper title="资产结构" style={{ height: '100%' }}>
              <AssetStackedColumn data={assetStackData} title="资产结构" height={getChartHeight(320)} fontSize={getFontSize(14)} isFullscreen={isFullscreen} />
            </EnlargeableChartWrapper>
            <EnlargeableChartWrapper title="负债结构" style={{ height: '100%' }}>
              <AssetStackedColumn data={debtStackData} title="负债结构" height={getChartHeight(320)} fontSize={getFontSize(14)} isFullscreen={isFullscreen} />
            </EnlargeableChartWrapper>
            <EnlargeableChartWrapper title="资产负债率" style={{ height: '100%' }}>
              <CustomLine data={debtToAssetsData} title="资产负债率" height={getChartHeight(320)} fontSize={getFontSize(14)} isFullscreen={isFullscreen} />
            </EnlargeableChartWrapper>
            <TurnoverToggleChart 
              totalAssetTurnoverData={totalAssetTurnoverData}
              equityTurnoverData={equityTurnoverData}
              height={getChartHeight(320)}
              fontSize={getFontSize(14)}
              isFullscreen={isFullscreen}
            />
          </div>

          {/* 三列布局：两金分析联动区域(50%) + 存货周转率(25%) + 应收账款周转率(25%) */}
          <div style={{ display: 'grid', gridTemplateColumns: '5fr 3fr 4fr 4fr', width: '100%',   borderBottom: '2px solid #e0e0e0' }}>
          <EnlargeableChartWrapper title="两金分析" style={{ height: '100%' }}>
            <HorizontalBar title="两金分析(亿元)"
              allCompanies={allCompanies}
              data={twoFundNetData}
              height={getChartHeight(280)}
              fontSize={getFontSize(14)}
              isFullscreen={isFullscreen}
            />
           </EnlargeableChartWrapper>
              <TwoFundIncomePanel title='两金占营业收入比重及同比变动'
                data={twoFundIncomeData}
              />
            <EnlargeableChartWrapper title="存货周转率">
              <CustomLine data={inventoryTurnoverData} title="存货周转率" height={getChartHeight(280)} fontSize={getFontSize(14)} isFullscreen={isFullscreen} />
            </EnlargeableChartWrapper>
            <EnlargeableChartWrapper title="应收账款周转率">
              <CustomLine data={receivablesTurnoverData} title="应收账款周转率" height={getChartHeight(280)} fontSize={getFontSize(14)} isFullscreen={isFullscreen} />
            </EnlargeableChartWrapper>
          </div>
          {/* 分区4：研发投入、研发费用、人效、分红、员工数、非经常性损益 */}
          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr 1fr', width: '100%', height: getChartHeight(320) }}>
            <EnlargeableChartWrapper title="研发投入">
              <CustomBarWithLine data={rdInvestData} title="研发投入" barTypes={['stmnote_rdexp']} height={getChartHeight(320)} fontSize={getFontSize(14)} isFullscreen={isFullscreen} />
            </EnlargeableChartWrapper>
            <EnlargeableChartWrapper title="人效情况">
              <CustomBarWithLine data={revenuePerPersonData} title="人效情况" barTypes={['salary_per_capita']} height={getChartHeight(320)} fontSize={getFontSize(14)} isFullscreen={isFullscreen} />
            </EnlargeableChartWrapper>
            <EnlargeableChartWrapper title="分红">
              <CustomBarWithLine data={divAualCashDividendData} title="分红" barTypes={['div_aualcashdividend_curr']} height={getChartHeight(320)} fontSize={getFontSize(14)} isFullscreen={isFullscreen} />
            </EnlargeableChartWrapper>
          </div>
          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(3, 1fr)', width: '100%', height: getChartHeight(320) }}>
            <EnlargeableChartWrapper title="研发费用">
              <CustomBarWithLine data={rdExpData} title="研发费用" barTypes={['rd_exp']} height={getChartHeight(320)} fontSize={getFontSize(14)} isFullscreen={isFullscreen} />
            </EnlargeableChartWrapper>
            <EnlargeableChartWrapper title="员工数">
              <CustomLine data={employeeData} title="员工数" height={getChartHeight(320)} fontSize={getFontSize(14)} isFullscreen={isFullscreen} />
            </EnlargeableChartWrapper>
            <EnlargeableChartWrapper title="非经常性损益（万元）">
              <HorizontalBar data={nonRecurringProfitLossData} title="非经常性损益（万元）" height={getChartHeight(320)} fontSize={getFontSize(14)} isFullscreen={isFullscreen} />
            </EnlargeableChartWrapper>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DashboardLayout; 