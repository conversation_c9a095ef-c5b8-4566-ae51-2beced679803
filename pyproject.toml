[project]
name = "windgather"
version = "0.1.0"
description = ""
authors = [
    {name = "吕建武",email = "<EMAIL>"}
]
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "beautifulsoup4 (==4.13.4)",
    "openpyxl (==3.1.5)",
    "pandas (==2.3.0)",
    "pdfplumber (==0.11.7)",
    "pyodbc (==5.2.0)",
    "requests (==2.32.4)"
]


[build-system]
requires = ["poetry-core>=2.0.0,<3.0.0"]
build-backend = "poetry.core.masonry.api"
