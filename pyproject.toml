[project]
name = "api"
version = "0.1.0"
description = ""
authors = [
    {name = "吕建武",email = "<EMAIL>"}
]

readme = "README.md"
requires-python = ">=3.11.5" 

[tool.poetry]
package-mode = false

[tool.poetry.dependencies]
python = "3.11.5"
flask = "2.3.3"
flask-cors = "4.0.0"
pyodbc = "4.0.39"
python-dotenv = "1.0.0"
Werkzeug = "2.3.7"
pandas = "2.0.3"
numpy = "1.25.2"
openpyxl = "3.1.2"
gunicorn = "*"

[build-system]
requires = ["poetry-core>=2.0.0,<3.0.0"]
build-backend = "poetry.core.masonry.api"
 