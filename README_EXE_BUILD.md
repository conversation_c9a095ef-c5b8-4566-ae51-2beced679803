# Wind数据导入工具 - EXE打包指南

## 概述

本项目提供了将Wind数据导入工具打包为exe文件的功能，支持智能默认值，可以根据当前时间自动选择要导入的数据期。

## 智能默认值逻辑

程序会根据当前时间自动选择要导入的数据：
- **当前2季度** → 查询1季度数据
- **当前1季度** → 查询上一年4季度数据  
- **当前3季度** → 查询2季度数据
- **当前4季度** → 查询3季度数据

## 文件结构

```
windgather/
├── import_wind_data.py          # 主程序（已支持命令行参数）
├── build_exe_simple.py          # 简化打包脚本
├── build.bat                    # Windows批处理打包脚本
├── test_smart_defaults.py       # 智能默认值测试脚本
├── import_config.py             # 配置文件
├── import_config_examples.py    # 配置示例
├── database/                    # 数据库相关文件
├── logging_config.py            # 日志配置
└── README_EXE_BUILD.md          # 本文档
```

## 打包方法

### 方法一：使用批处理脚本（推荐）

1. 双击运行 `build.bat`
2. 脚本会自动检查依赖并安装缺失的包
3. 自动执行打包过程
4. 生成的文件在 `dist/` 目录中

### 方法二：手动执行

1. 检查依赖：
```bash
python build_exe_simple.py
```

2. 如果缺少依赖，手动安装：
```bash
pip install pyinstaller pandas numpy pyodbc
```

3. 执行打包：
```bash
python build_exe_simple.py
```

## 生成的文件

打包成功后，会在 `dist/` 目录中生成以下文件：

- `wind_import.exe` - 主程序文件
- `使用说明.txt` - 详细使用说明

## 使用方法

### 1. 智能默认模式（推荐）

直接双击运行 `wind_import.exe`，程序会自动：
- 根据当前时间选择合适的数据期
- 显示选择的配置信息
- 开始数据导入

### 2. 命令行模式

在命令行中运行：

```bash
# 使用智能默认值
wind_import.exe

# 指定年份和季度
wind_import.exe --start-year 2024 --end-year 2024 --quarters 0331

# 导入多个季度
wind_import.exe --start-year 2024 --end-year 2025 --quarters 0331 0630 0930 1231

# 只导入年报数据
wind_import.exe --start-year 2024 --end-year 2024 --quarters 1231

# 使用配置文件
wind_import.exe --config-file my_config.py

# 查看帮助
wind_import.exe --help
```

### 3. 配置文件模式

创建 `my_config.py` 文件：
```python
START_YEAR = 2024
END_YEAR = 2024
QUARTERS = ['0331', '0630', '0930', '1231']
```

然后运行：
```bash
wind_import.exe --config-file my_config.py
```

## 测试智能默认值

在打包前，可以运行测试脚本验证智能默认值逻辑：

```bash
python test_smart_defaults.py
```

这会显示：
- 当前时间的智能默认值
- 不同月份的智能默认值逻辑
- 命令行参数测试

## 注意事项

### 打包前检查

1. 确保所有依赖文件存在：
   - `import_wind_data.py`
   - `database/` 目录
   - `logging_config.py`
   - `import_config.py`

2. 确保Python环境正常：
   - Python 3.7+
   - pip 可用

3. 确保网络连接正常（用于安装依赖包）

### 打包后检查

1. 测试exe文件是否能正常启动
2. 测试命令行参数是否正常工作
3. 测试智能默认值是否正确

### 常见问题

1. **打包失败**
   - 检查是否缺少依赖包
   - 检查Python版本是否兼容
   - 检查磁盘空间是否充足

2. **exe文件无法启动**
   - 检查是否被杀毒软件拦截
   - 检查是否有必要的运行时库
   - 检查文件是否完整

3. **智能默认值不正确**
   - 运行 `test_smart_defaults.py` 验证逻辑
   - 检查系统时间是否正确

## 部署说明

1. 将生成的 `wind_import.exe` 复制到目标机器
2. 确保目标机器有Wind终端并已登录
3. 确保数据库连接正常
4. 可以直接运行或通过命令行使用

## 版本信息

- 打包工具：PyInstaller
- 支持平台：Windows
- 文件格式：单文件exe
- 智能默认值：支持

## 更新日志

- v1.0: 初始版本，支持基本打包功能
- v1.1: 添加智能默认值逻辑
- v1.2: 优化打包脚本，添加依赖检查
- v1.3: 添加批处理脚本，简化打包过程 