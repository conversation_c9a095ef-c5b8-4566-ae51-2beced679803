import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './App';
import './index.css';

const rootElement = document.getElementById('root');
if (rootElement) {
  const root = ReactDOM.createRoot(rootElement);
  root.render(
    <React.StrictMode>
      <App />
    </React.StrictMode>
  );
} else {
  console.error('Failed to find the root element');
}

// Add a global error handler to catch unhandled errors
window.onerror = function (message, source, lineno, colno, error) {
  console.log(message, source, lineno, colno, error);
  
  // Return true to prevent the browser's default error handling
  return true;
};

// Catch unhandled promise rejections
window.addEventListener('unhandledrejection', function (event) {
  const errorContainer = document.getElementById('error-container');
  if (errorContainer) {
    const reason = event.reason || '未处理的 Promise 拒绝';
    console.log(reason);
   
  }
});
