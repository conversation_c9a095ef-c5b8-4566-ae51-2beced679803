import pandas as pd
from database.db_handler import DatabaseHandler
from datetime import datetime
from L2.formula_executor import FormulaExecutor


class FinancialMetricsCalculator:
    def __init__(self):
        self.db = DatabaseHandler()
        self.executor = FormulaExecutor()
        
    def _get_company_name(self, stock_code):
        """根据股票代码获取公司名称"""
        query = """
        SELECT CompanyName 
        FROM CompanyInfo 
        WHERE TickerSymbol=?
        """
        with self.db as db:
            cursor = db.connection.cursor()
            cursor.execute(query, (stock_code,))
            row = cursor.fetchone()
            
        return row[0] if row else stock_code
    
    def calculate_metrics(self, stock_code, date_str):
        """
        根据股票代码和日期计算财务指标
        
        参数:
            stock_code (str): 股票代码
            date_str (str): 日期，格式为YYYYMMDD
            
        返回:
            dict: 包含计算结果的字典
        """
        date_obj = datetime.strptime(date_str, '%Y%m%d')
        
        # 从数据库获取基础数据
        current_data, yoy_data = self._get_metrics_data(stock_code, date_obj)
        if not current_data or not yoy_data:
            return None
            
        # 从数据库获取指标公式
        formulas = self._get_metric_formulas_from_db()
        
        # 获取公司名称
        company_name = self._get_company_name(stock_code)
        
        # 计算结果
        results = {
            'stock_code': stock_code,
            'company_name': company_name,
            'metrics': {}
        }
        
        for metric_name, formula_info in formulas.items():
            try:
                # 执行公式计算
                value = self.executor.execute_formula(
                    formula_info['formula'],
                    current_data,
                    yoy_data
                )
                
                results['metrics'][metric_name] = {
                    'value': value,
                    'formula': formula_info['formula'],
                    'lower_bound': formula_info.get('lower_bound'),
                    'upper_bound': formula_info.get('upper_bound'),
                    'range_value': formula_info.get('range_value'),
                    'explanation': formula_info.get('explanation', '')
                }
            except Exception as e:
                print(f"计算指标 {metric_name} 时出错: {str(e)}")
                continue
                
        return results
    
    def _get_metrics_data(self, stock_code, date_obj):
        """从数据库获取指标数据"""
        query = """
        SELECT WindCode, Value, YoYValue 
        FROM WindMetrics 
        WHERE TickerSymbol=? AND Date=?
        """
        with self.db as db:
            cursor = db.connection.cursor()
            cursor.execute(query, (stock_code, date_obj))
            rows = cursor.fetchall()
            
        current_values = {}
        yoy_values = {}
        for row in rows:
            current_values[row[0]] = row[1]
            yoy_values[row[0]] = row[2]
            
        return current_values, yoy_values
    
    def _get_metric_formulas_from_db(self):
        """从L2BenchmarkIndicators表获取指标公式"""
        query = """
        SELECT IndicatorNameCN, FormulaDesc, LowerBound, UpperBound, RangeValue
        FROM L2BenchmarkIndicators
        """
        with self.db as db:
            cursor = db.connection.cursor()
            cursor.execute(query)
            rows = cursor.fetchall()
            
        return {
            row[0]: {
                'formula': row[1],
                'lower_bound': row[2],
                'upper_bound': row[3],
                'range_value': row[4]
            } for row in rows
        }

# 使用示例
if __name__ == "__main__":
    calculator = FinancialMetricsCalculator()
    stock_code = "600850.SH"
    date_str = "20241231"
    
    results = calculator.calculate_metrics(stock_code, date_str)
    print(f"股票代码: {results['stock_code']}")
    print(f"公司名称: {results['company_name']}")
    print("指标结果:")
    for metric, data in results['metrics'].items():
        print(f"{metric}: {data['value']:.4f}")
        print(f"公式: {data['formula']}")
        print(f"解释: {data['explanation']}")
        print("-" * 50)