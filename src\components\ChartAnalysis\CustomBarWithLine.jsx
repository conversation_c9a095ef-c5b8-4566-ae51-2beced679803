import React, { useEffect, useRef, useState } from "react";
import { Chart, } from "@antv/g2";

/**
 * 通用多轴混合图组件（长表自动转宽表，自动识别 source.key/source.name）
 * @param {Array} data - 原始长表结构（company, value, source.key, source.name...）
 * @param {string} xField - 横轴字段名
 * @param {Array} barTypes - 柱状图key数组（英文）
 * @param {number} height - 图表高度
 * @param {string} title - 图表标题
 */
const CustomBarWithLine = ({
  data = [],
  xField = "company",
  barTypes = [],
  height = 320,
  title = "指标",
  isFullscreen = false,
  isIndividualFullscreen = false,
}) => {
  const container = useRef();
  
  // 监听单个全屏状态变化
  useEffect(() => {
    const handleFullscreenChange = () => {
      // setIsIndividualFullscreen(!!document.fullscreenElement); // 移除 useState 和 setIsIndividualFullscreen
    };
    document.addEventListener('fullscreenchange', handleFullscreenChange);
    document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
    document.addEventListener('mozfullscreenchange', handleFullscreenChange);
    document.addEventListener('MSFullscreenChange', handleFullscreenChange);
    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
      document.removeEventListener('webkitfullscreenchange', handleFullscreenChange);
      document.removeEventListener('mozfullscreenchange', handleFullscreenChange);
      document.removeEventListener('MSFullscreenChange', handleFullscreenChange);
    };
  }, []);

  // 自动提取所有指标key和name
  const allKeys = Array.from(new Set(data.map(item => item.source.key)));
  const keyNameMap = {};
  data.forEach(item => {
    keyNameMap[item.source.key] = item.source.name;
  });

  // 自动生成系列配置
  const barSeries = barTypes.map(key => ({ key, name: keyNameMap[key] }));
  const lineSeries = allKeys.filter(key => !barTypes.includes(key)).map(key => ({ key, name: keyNameMap[key] }));
  const allSeries = [...barSeries, ...lineSeries];
   
  // 计算缩放比例，用于响应式调整字体大小 
  const labelFontSize = 24;

  // 长表转宽表，字段名用 item.source.key，同时保留FormulaDesc信息
  function longToWide(data, xField, allSeries) {
    const map = {};
    const formulaDescMap = {}; // 存储每个指标的FormulaDesc
    
    data.forEach(item => {
      const x = item[xField];
      if (!map[x]) map[x] = { [xField]: x };
      map[x][item.source.key] = item.value;
      
      // 如果有displayValue字段，也转换它
      if (item.displayValue !== undefined && item.displayValue !== null) {
        map[x][item.source.key + '_displayValue'] = item.displayValue;
      }
      
      // 保存FormulaDesc信息，从item.source.FormulaDesc获取
      if (item.source && item.source.FormulaDesc) {
        formulaDescMap[item.source.name] = item.source.FormulaDesc;
      }
    });
    
    // 补齐所有key
    allSeries.forEach(s => {
      Object.values(map).forEach(row => {
        if (!(s.key in row)) row[s.key] = null;
        if (!(s.key + '_displayValue' in row)) row[s.key + '_displayValue'] = null;
      });
    });
    
    return { wideData: Object.values(map), formulaDescMap };
  }

  const { wideData, formulaDescMap } = longToWide(data, xField, allSeries);
  
  // 调试：打印公式映射
  console.log('Original data:', data);
  console.log('FormulaDescMap:', formulaDescMap);
  console.log('WideData:', wideData);

  // 计算实际高度
  const computedHeight = isIndividualFullscreen ? window.innerHeight * 0.9 : (isFullscreen ? height : height);

  // 获取label显示值的函数
  const getLabelValue = (d, key) => {
    const displayKey = key + '_displayValue';
    return d[displayKey] !== null && d[displayKey] !== undefined ? d[displayKey] : d[key];
  };

  useEffect(() => {
    if (!container.current) return;
    const chart = new Chart({
      container: container.current,
      autoFit: true,
      height: computedHeight, title: {
        title,
        titleFontSize: 24,
      },
      style:{

          maxColumnWidth: 50,
          
      },
      
    });

    chart.data(wideData);

    // 柱状图
    barSeries.forEach((s, idx) => {
      chart
        .interval()
        .encode('x', xField)
        .encode('y', s.key)
        .encode('color', () => s.name)
        .encode('size', 50)
        .label({
          text: d => getLabelValue(d, s.key),
          position: 'top',  
          style: { fontSize: 14 }
        }) 
        .axis('y', false)
        .axis('x', { title: false,labelFill:'#000000',labelFillOpacity:2 })
        .scale('y', { nice: true, key: idx === 0 ? '1' : String(idx + 1) })
        .tooltip({
          items: [
            (datum) => ({
              name: s.name,
              value: getLabelValue(datum, s.key)
            })
          ]
        }).style({  'minWidth': 20, 'maxWidth': 55 })
        .interaction('tooltip', {
          render: (event, { title, items }) => {
            console.log('Bar Tooltip event:', event);
            console.log('Bar Tooltip items:', items);
            console.log('Bar Tooltip title:', title);
            
            // 获取当前数据点的公司名称
            let companyName = title;
            
            // 构建所有指标的HTML
            let itemsHtml = '';
            items.forEach(item => {
              console.log('Bar Processing item:', item);
              // 通过name查找公式描述
              const formulaDesc = formulaDescMap[item.name] || formulaDescMap[s.key];
              console.log(`Bar Item ${item.name} formula:`, formulaDesc);
              
              // 使用item.value，现在它应该是displayValue
              const displayValue = item.value || 'N/A';
              
              itemsHtml += `
                <div style="margin-bottom: 3px;">
                  <div style="display: flex; justify-content: space-between; align-items: center;font-size: 16px;">
                    <span style="color: ${item.color}; font-weight: 500;">${item.name}</span>
                    <span style="font-weight: bold; ">${displayValue}</span>
                  </div>
                  ${formulaDesc ? `<div style="font-size: 14px; color: #999; margin-top: 4px; padding-left: 0px;">${formulaDesc}</div>` : ''}
                </div>
              `;
            });
            
            return `<div style="padding: 5px; min-width: 250px;">
              <div style="font-weight: bold; font-size: 16px; margin-bottom: 12px; border-bottom: 1px solid #eee; padding-bottom: 8px;">
                ${companyName}
              </div>
              ${itemsHtml}
            </div>`;
          }
        }); 
    });

    // 折线图
    lineSeries.forEach((s, idx) => {
      chart
        .line()   .encode('shape', 'smooth')
        .encode('x', xField)
        .encode('y', s.key)
        .encode('color', () => s.name)
        .axis('y', false)
        .scale('y', { key: String(barSeries.length + idx + 1), independent: true })
        .style('lineWidth', 4)
        .style('lineDash', [2, 2])
        .label({
          text: d => getLabelValue(d, s.key), 
          style: { fontSize: 14 }
        })
        .tooltip({
          items: [
            (datum) => ({
              name: s.name,
              value: getLabelValue(datum, s.key)
            })
          ]
        })
        .interaction('tooltip', {
          render: (event, { title, items }) => {
            console.log('Line Tooltip event:', event);
            console.log('Line Tooltip items:', items);
            console.log('Line Tooltip title:', title);
            
            // 获取当前数据点的公司名称
            let companyName = title;
            
            // 构建所有指标的HTML
            let itemsHtml = '';
            items.forEach(item => {
              console.log('Line Processing item:', item);
              // 通过name查找公式描述
              const formulaDesc = formulaDescMap[item.name] || formulaDescMap[s.key];
              console.log(`Line Item ${item.name} formula:`, formulaDesc);
              
              // 使用item.value，现在它应该是displayValue
              const displayValue = item.value || 'N/A';
              
              itemsHtml += `
                <div style="margin-bottom: 3px;">
                  <div style="display: flex; justify-content: space-between; align-items: center;font-size: 16px;">
                    <span style="color: ${item.color}; font-weight: 500;">${item.name}</span>
                    <span style="font-weight: bold; ">${displayValue}</span>
                  </div>
                  ${formulaDesc ? `<div style="font-size: 14px; color: #999; margin-top: 4px; padding-left: 0px;">${formulaDesc}</div>` : ''}
                </div>
              `;
            });
            
            return `<div style="padding: 5px; min-width: 250px;">
              <div style="font-weight: bold; font-size: 16px; margin-bottom: 12px; border-bottom: 1px solid #eee; padding-bottom: 8px;">
                ${companyName}
              </div>
              ${itemsHtml}
            </div>`;
          }
        });
    });

    // 统一用 options 配置 legend
    chart.options({
      legend: {
        color: { title: title ,maxRows:1},
        
      }
    });
    
    chart.labelTransform({ type: 'overlapHide' })
    chart.labelTransform({ type: 'contrastReverse' });  
    chart.render();

    // 添加 resize 监听器，当容器尺寸变化时重新渲染
    const resizeObserver = new ResizeObserver(() => {
      if (chart && !chart.destroyed) {
        chart.render();
      }
    });
    
    if (container.current) {
      resizeObserver.observe(container.current);
    }

    return () => {
      resizeObserver.disconnect();
      chart.destroy();
    };
  }, [data, xField, barTypes, height, title, isIndividualFullscreen]);

  return <div ref={container} style={{ width: '100%', height: '100%' }} />;
};
export default CustomBarWithLine; 