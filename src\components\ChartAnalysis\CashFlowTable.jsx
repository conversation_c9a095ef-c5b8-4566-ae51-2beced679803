import React from 'react';

const CashFlowTable = ({ data = [], style }) => {
  if (!data || data.length === 0) return <div style={style}>暂无数据</div>;

  // 按公司分组
  const companyMap = {};
  data.forEach(item => {
    const name = item.company || item.CompanyName;
    if (!companyMap[name]) companyMap[name] = { company: name };
    if (item.source.key === 'net_cash_flows_oper_act') companyMap[name].oper = item.value;
    if (item.source.key === 'net_cash_flows_inv_act') companyMap[name].inv = item.value;
    if (item.source.key === 'net_cash_flows_fnc_act') companyMap[name].fnc = item.value;
  });
  const rows = Object.values(companyMap);

  return (
    <div style={{ width: '100%', height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>
      <table style={{ borderCollapse: 'collapse', width: '100%', height: '100%', background: '#fff', borderRadius: 8, fontSize: 15 }}>
        <thead>
          <tr style={{ background: '#1890ff', color: '#fff' }}>
            <th style={{ padding: 8,width: 100  }}>公司</th>
            <th style={{ padding: 8,  }}>经营活动产生的现金流量净额(亿元)</th>
            <th style={{ padding: 8,}}>投资活动产生的现金流量净额(亿元)</th>
            <th style={{ padding: 8,  }}>筹资活动产生的现金流量净额(亿元)</th>
          </tr>
        </thead>
        <tbody>
          {rows.map(row => (
            <tr key={row.company}>
              <td style={{ padding: 8, textAlign: 'center', color: '#222', fontWeight: 600 }}>{row.company}</td>
              <td style={{ padding: 8, textAlign: 'center' }}>{row.oper ?? '-'}</td>
              <td style={{ padding: 8, textAlign: 'center' }}>{row.inv ?? '-'}</td>
              <td style={{ padding: 8, textAlign: 'center' }}>{row.fnc ?? '-'}</td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default CashFlowTable; 