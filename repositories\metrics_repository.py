import sqlite3
import pyodbc
from ..config.database_config import get_db_connection

class MetricsRepository:
    def __init__(self):
        self.connection = get_db_connection()

    def get_company_metrics(self, ticker_symbol, indicator_name):
        cursor = self.connection.cursor()
        query = '''SELECT * FROM L2Metrics WHERE TickerSymbol = ? AND IndicatorNameEN = ?'''
        cursor.execute(query, (ticker_symbol, indicator_name))
        
        # 将结果转换为字典列表
        columns = [column[0] for column in cursor.description]
        results_as_dict = []
        for row in cursor.fetchall():
            results_as_dict.append(dict(zip(columns, row)))
        return results_as_dict

    def get_metrics_for_selection(self, ticker_symbols, indicator_names_en):
        if not ticker_symbols or not indicator_names_en:
            return []

        cursor = self.connection.cursor()
        
        # 构建占位符
        ticker_placeholders = ','.join(['?'] * len(ticker_symbols))
        indicator_placeholders = ','.join(['?'] * len(indicator_names_en))
        
        query = f'''
            SELECT * 
            FROM L2Metrics 
            WHERE TickerSymbol IN ({ticker_placeholders}) 
            AND IndicatorNameEN IN ({indicator_placeholders})
            ORDER BY TickerSymbol, ReportDate, IndicatorNameEN
        '''
        
        params = tuple(ticker_symbols) + tuple(indicator_names_en)
        cursor.execute(query, params)
        
        # 将结果转换为字典列表
        columns = [column[0] for column in cursor.description]
        results_as_dict = []
        for row in cursor.fetchall():
            results_as_dict.append(dict(zip(columns, row)))
            
        return results_as_dict
