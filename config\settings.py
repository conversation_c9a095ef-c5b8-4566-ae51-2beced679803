import os

class Config:
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'aSKENSwljadfnwisldalf23'

    # 数据库配置 (从 d:\StudyCode\fin\财务数据分析\config.py 借鉴并适应)
    DB_CONFIG = {
        'driver': os.environ.get('DB_DRIVER', 'ODBC Driver 17 for SQL Server'),
        'server': os.environ.get('DB_SERVER', '10.100.0.21'), # 请替换为实际服务器地址或使用环境变量
        'database': os.environ.get('DB_NAME', 'FinancialReport'),    # 请替换为实际数据库名或使用环境变量
        'user': os.environ.get('DB_USER', 'FinancialReportWeb'),        # 请替换为实际用户名或使用环境变量
        'password': os.environ.get('DB_PASSWORD', 'lS1+kL9@rO')           # 请替换为实际密码或使用环境变量
    }

    # 示例：SQLAlchemy 配置 (如果使用)
    # SQLALCHEMY_DATABASE_URI = (
    #     f"mssql+pyodbc://{DB_CONFIG['user']}:{DB_CONFIG['password']}"
    #     f"@{DB_CONFIG['server']}/{DB_CONFIG['database']}"
    #     f"?driver={DB_CONFIG['driver'].replace(' ', '+')}"
    # )
    # SQLALCHEMY_TRACK_MODIFICATIONS = False

    # 其他应用配置可以放在这里
    # API_URL = "https://example.com/api"

class DevelopmentConfig(Config):
    DEBUG = True

class TestingConfig(Config):
    TESTING = True
    # 示例：测试时使用内存数据库或特定测试数据库
    # DB_CONFIG = { ... test db config ... }
    # SQLALCHEMY_DATABASE_URI = 'sqlite:///:memory:'

class ProductionConfig(Config):
    DEBUG = False
    TESTING = False
    # 生产环境应从环境变量加载敏感配置

config = {
    'development': DevelopmentConfig,
    'testing': TestingConfig,
    'production': ProductionConfig,
    'default': DevelopmentConfig
}