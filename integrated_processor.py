#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
整合数据导入和指标计算的主程序
"""

import argparse
import logging
import sys
import time
from datetime import datetime

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(f'integrated_processor_{datetime.now().strftime("%Y%m%d")}.log', encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger('IntegratedProcessor')

def run_import_wind_data(start_year, end_year, quarters):
    """运行Wind数据导入"""
    logger = logging.getLogger('IntegratedProcessor')
    logger.info("=" * 60)
    logger.info("开始Wind数据导入阶段")
    logger.info("=" * 60)
    
    try:
        import sys
        import os
        
        # 处理EXE打包后的路径问题
        if getattr(sys, 'frozen', False):
            # 如果是打包后的EXE，添加当前目录到sys.path
            current_dir = os.path.dirname(sys.executable)
            if current_dir not in sys.path:
                sys.path.insert(0, current_dir)
        
        # 直接导入模块
        from import_wind_data import WindDataImporter
        
        # 创建导入器并执行导入
        wind_importer = WindDataImporter(
            start_year=start_year,
            end_year=end_year,
            quarters=quarters
        )
        
        wind_importer.import_data()
        logger.info("Wind数据导入完成")
        return True
        
    except Exception as e:
        logger.error(f"Wind数据导入失败: {e}")
        return False

def run_metrics_calculator(start_year, end_year, quarters, stock_code=None, 
                          save_to_db=True, force_recalculate=False, check_only=False):
    """运行指标计算"""
    logger = logging.getLogger('IntegratedProcessor')
    logger.info("=" * 60)
    logger.info("开始财务指标计算阶段")
    logger.info("=" * 60)
    
    try:
        import sys
        import os
        
        # 处理EXE打包后的路径问题
        if getattr(sys, 'frozen', False):
            # 如果是打包后的EXE，添加当前目录到sys.path
            current_dir = os.path.dirname(sys.executable)
            if current_dir not in sys.path:
                sys.path.insert(0, current_dir)
        
        # 直接导入模块
        from metrics_calculator import main as metrics_main
        
        # 构建命令行参数
        sys.argv = ['metrics_calculator.py']
        
        # 添加年份参数
        for year in range(start_year, end_year + 1):
            sys.argv.extend(['--years', str(year)])
        
        # 添加季度参数
        sys.argv.extend(['--quarters'] + quarters)
        
        # 添加其他参数
        if stock_code:
            sys.argv.extend(['--stock_code', stock_code])
        
        if not save_to_db:
            sys.argv.append('--no_save')
        
        if force_recalculate:
            sys.argv.append('--force_recalculate')
        
        if check_only:
            sys.argv.append('--check_only')
        
        # 执行指标计算
        metrics_main()
        logger.info("财务指标计算完成")
        return True
        
    except Exception as e:
        logger.error(f"财务指标计算失败: {e}")
        return False

def get_smart_defaults():
    """获取智能默认值：当前2季度查询一季度，一季度查询上一年4季度"""
    from datetime import datetime
    now = datetime.now()
    current_year = now.year
    current_month = now.month
    
    # 确定当前季度
    if current_month <= 3:
        current_quarter = 1
        quarter_code = '0331'
    elif current_month <= 6:
        current_quarter = 2
        quarter_code = '0630'
    elif current_month <= 9:
        current_quarter = 3
        quarter_code = '0930'
    else:
        current_quarter = 4
        quarter_code = '1231'
    
    # 智能默认逻辑
    if current_quarter == 2:  # 当前是2季度，查询1季度
        target_year = current_year
        target_quarter = '0331'
    elif current_quarter == 1:  # 当前是1季度，查询上一年4季度
        target_year = current_year - 1
        target_quarter = '1231'
    else:  # 其他情况，查询上一个季度
        if current_quarter == 3:
            target_year = current_year
            target_quarter = '0630'
        else:  # current_quarter == 4
            target_year = current_year
            target_quarter = '0930'
    
    return target_year, target_quarter

def main():
    """主函数"""
    # 获取智能默认值
    default_year, default_quarter = get_smart_defaults()
    
    parser = argparse.ArgumentParser(
        description='整合Wind数据导入和财务指标计算 - 智能默认值版本',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog=f"""
智能默认值说明:
  当前时间: {datetime.now().strftime('%Y年%m月')}
  智能默认: {default_year}年{default_quarter}季度数据

使用示例:
  # 使用智能默认值（推荐）
  python integrated_processor.py
  
  # 指定年份和季度
  python integrated_processor.py --start-year 2024 --end-year 2024 --quarters 0331
  
  # 导入多个季度
  python integrated_processor.py --start-year 2024 --end-year 2025 --quarters 0331 0630 0930 1231
  
  # 只导入年报数据
  python integrated_processor.py --start-year 2024 --end-year 2024 --quarters 1231
        """
    )
    
    # 时间参数
    parser.add_argument('--start-year', type=int, default=default_year, help=f'开始年份 (智能默认: {default_year})')
    parser.add_argument('--end-year', type=int, default=default_year, help=f'结束年份 (智能默认: {default_year})')
    parser.add_argument('--quarters', nargs='+', default=[default_quarter],
                       choices=['0331', '0630', '0930', '1231'], help=f'季度列表 (智能默认: {default_quarter})')
    
    # 股票参数
    parser.add_argument('--stock-code', type=str, help='指定单个股票代码')
    
    # 处理选项
    parser.add_argument('--skip-import', action='store_true', help='跳过数据导入阶段')
    parser.add_argument('--force-recalculate', action='store_true', help='强制重新计算指标')
    parser.add_argument('--no-save', action='store_true', help='不保存结果到数据库')
    parser.add_argument('--check-only', action='store_true', help='仅检查未处理的数据')
    
    args = parser.parse_args()
    
    # 设置日志
    logger = setup_logging()
    
    logger.info("=" * 80)
    logger.info("开始整合数据处理")
    logger.info("=" * 80)
    logger.info(f"处理配置:")
    logger.info(f"  年份范围: {args.start_year}-{args.end_year}")
    logger.info(f"  季度: {args.quarters}")
    logger.info(f"  跳过导入: {args.skip_import}")
    logger.info(f"  强制重新计算: {args.force_recalculate}")
    logger.info(f"  不保存到数据库: {args.no_save}")
    logger.info(f"  仅检查: {args.check_only}")
    
    overall_start_time = time.time()
    
    # 阶段1: 数据导入
    import_success = True
    if not args.skip_import:
        import_success = run_import_wind_data(
            args.start_year, 
            args.end_year, 
            args.quarters
        )
        if not import_success:
            logger.error("数据导入失败，停止处理")
            return
    else:
        logger.info("跳过数据导入阶段")
    
    # 阶段2: 指标计算
    calculation_success = run_metrics_calculator(
        args.start_year,
        args.end_year,
        args.quarters,
        stock_code=args.stock_code,
        save_to_db=not args.no_save,
        force_recalculate=args.force_recalculate,
        check_only=args.check_only
    )
    
    # 总结
    overall_time = time.time() - overall_start_time
    
    logger.info("=" * 80)
    logger.info("整合数据处理完成")
    logger.info("=" * 80)
    logger.info(f"总耗时: {overall_time:.2f} 秒")
    logger.info(f"数据导入: {'成功' if import_success else '失败'}")
    logger.info(f"指标计算: {'成功' if calculation_success else '失败'}")

if __name__ == "__main__":
    main() 