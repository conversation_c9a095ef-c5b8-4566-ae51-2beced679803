# example_routes.py
from flask import Blueprint, request, jsonify
from ..services.chart_config_service import ChartConfigService
from ..utils.decorators import api_error_handler, validate_required_params
from ..utils.error_handler import ValidationError

example_bp = Blueprint('example', __name__, url_prefix='/api/example')
chart_config_service = ChartConfigService()

@example_bp.route('/chart-data', methods=['POST'])
@api_error_handler("获取图表数据")
@validate_required_params('report_date', 'company_ids')
def get_chart_data():
    """
    使用装饰器的示例路由
    自动处理错误和参数验证
    """
    request_data = request.get_json()
    
    # 参数验证已由装饰器处理
    report_date = request_data['report_date']
    company_ids = request_data['company_ids']
    
    # 额外的业务逻辑验证
    if isinstance(company_ids, str):
        company_ids = [id.strip() for id in company_ids.split(',') if id.strip()]
    
    if not company_ids:
        raise ValidationError('company_ids不能为空')
    
    # 执行业务逻辑
    data = chart_config_service.get_chart_data_for_dashboard(report_date, company_ids)
    
    return jsonify({
        'success': True,
        'data': data
    })

@example_bp.route('/simple', methods=['GET'])
@api_error_handler("简单示例")
def simple_example():
    """
    简单的示例，只使用错误处理装饰器
    """
    # 模拟可能出错的业务逻辑
    result = {"message": "操作成功"}
    return jsonify(result) 