# L2/db_structure_manager.py
from database.db_handler import DatabaseHandler

class DatabaseStructureManager:
    """
    负责管理和确保数据库表结构的正确性。
    """
    def __init__(self):
        self.db = DatabaseHandler()

    def ensure_database_structure(self):
        """
        确保数据库表结构正确，如果表不存在则创建，如果缺少列则添加
        """
        try:
            with self.db as db:
                cursor = db.connection.cursor()

                # 检查L2BenchmarkIndicators表
                self._ensure_l2_benchmark_indicators_table(cursor)

                # 检查L2Metrics表
                self._ensure_l2_metrics_table(cursor)

                # 检查L2MetricsErrorLog表
                self._ensure_l2_metrics_error_log_table(cursor)

                # 检查L2MetricsCalculationLog表
                self._ensure_l2_metrics_calculation_log_table(cursor)

                # 提交事务
                db.connection.commit()

                print("数据库表结构检查完成")
        except Exception as e:
            print(f"确保数据库表结构正确时出错: {str(e)}")

    def _ensure_l2_benchmark_indicators_table(self, cursor):
        """确保L2BenchmarkIndicators表结构正确"""
        cursor.execute("""
        SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES
        WHERE TABLE_NAME = 'L2BenchmarkIndicators'
        """)
        table_exists = cursor.fetchone()[0] > 0

        if not table_exists:
            cursor.execute("""
            CREATE TABLE L2BenchmarkIndicators (
                NO INT IDENTITY(1,1) PRIMARY KEY,
                IndicatorNameCN NVARCHAR(100) NOT NULL,
                IndicatorNameEN NVARCHAR(100) NOT NULL,
                IndicatorDesc NVARCHAR(MAX),
                FormulaDesc NVARCHAR(MAX),
                Formula NVARCHAR(MAX),
                LowerBound DECIMAL(38, 6),
                UpperBound DECIMAL(38, 6),
                RangeValue NVARCHAR(100),
                Category NVARCHAR(50),
                IsActive BIT DEFAULT 1,
                CreateTime DATETIME DEFAULT GETDATE(),
                UpdateTime DATETIME DEFAULT GETDATE(),
                CONSTRAINT UK_L2BenchmarkIndicators UNIQUE (IndicatorNameEN, IndicatorNameCN)
            )
            """)
            print("已创建L2BenchmarkIndicators表")
        else:
            # 检查是否有Category列
            cursor.execute("""
            SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_NAME = 'L2BenchmarkIndicators' AND COLUMN_NAME = 'Category'
            """)
            column_exists = cursor.fetchone()[0] > 0
            if not column_exists:
                cursor.execute("ALTER TABLE L2BenchmarkIndicators ADD Category NVARCHAR(50)")
                print("已添加Category列到L2BenchmarkIndicators表")
            
            # 检查是否有LowerBoundLabel列
            cursor.execute("""
            SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_NAME = 'L2BenchmarkIndicators' AND COLUMN_NAME = 'LowerBoundLabel'
            """)
            column_exists = cursor.fetchone()[0] > 0
            if not column_exists:
                cursor.execute("ALTER TABLE L2BenchmarkIndicators ADD LowerBoundLabel NVARCHAR(50)")
                print("已添加LowerBoundLabel列到L2BenchmarkIndicators表")
            
            # 检查是否有UpperBoundLabel列
            cursor.execute("""
            SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_NAME = 'L2BenchmarkIndicators' AND COLUMN_NAME = 'UpperBoundLabel'
            """)
            column_exists = cursor.fetchone()[0] > 0
            if not column_exists:
                cursor.execute("ALTER TABLE L2BenchmarkIndicators ADD UpperBoundLabel NVARCHAR(50)")
                print("已添加UpperBoundLabel列到L2BenchmarkIndicators表")

    def _ensure_l2_metrics_table(self, cursor):
        """确保L2Metrics表结构正确"""
        cursor.execute("""
        SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES
        WHERE TABLE_NAME = 'L2Metrics'
        """)
        table_exists = cursor.fetchone()[0] > 0

        if not table_exists:
            cursor.execute("""
            CREATE TABLE L2Metrics (
                NO INT IDENTITY(1,1) PRIMARY KEY,
                TickerSymbol NVARCHAR(20) NOT NULL,
                CompanyName NVARCHAR(100),
                ReportDate DATE NOT NULL,
                IndicatorNameCN NVARCHAR(100) NOT NULL,
                IndicatorNameEN NVARCHAR(100) NOT NULL,
                IndicatorDesc NVARCHAR(MAX),
                FormulaDesc NVARCHAR(MAX),
                Formula NVARCHAR(MAX),
                FormulaEN NVARCHAR(MAX),
                CalculatedValue DECIMAL(38, 6),
                LowerBound DECIMAL(38, 6),
                UpperBound DECIMAL(38, 6),
                RangeValue NVARCHAR(100),
                EvaluationResult NVARCHAR(50),
                Remarks NVARCHAR(MAX),
                CreateTime DATETIME DEFAULT GETDATE(),
                UpdateTime DATETIME DEFAULT GETDATE(),
                CONSTRAINT UK_L2Metrics UNIQUE (TickerSymbol, ReportDate, IndicatorNameEN)
            )
            """)
            print("已创建L2Metrics表")
        else:
            # 检查是否有IndicatorDesc列
            cursor.execute("""
            SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_NAME = 'L2Metrics' AND COLUMN_NAME = 'IndicatorDesc'
            """)
            column_exists = cursor.fetchone()[0] > 0
            if not column_exists:
                cursor.execute("ALTER TABLE L2Metrics ADD IndicatorDesc NVARCHAR(MAX)")
                print("已添加IndicatorDesc列到L2Metrics表")

            # 检查是否有Formula列
            cursor.execute("""
            SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_NAME = 'L2Metrics' AND COLUMN_NAME = 'Formula'
            """)
            column_exists = cursor.fetchone()[0] > 0
            if not column_exists:
                cursor.execute("ALTER TABLE L2Metrics ADD Formula NVARCHAR(MAX)")
                print("已添加Formula列到L2Metrics表")

    def _ensure_l2_metrics_error_log_table(self, cursor):
        """确保L2MetricsErrorLog表结构正确"""
        cursor.execute("""
        SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES
        WHERE TABLE_NAME = 'L2MetricsErrorLog'
        """)
        table_exists = cursor.fetchone()[0] > 0

        if not table_exists:
            cursor.execute("""
            CREATE TABLE L2MetricsErrorLog (
                NO INT IDENTITY(1,1) PRIMARY KEY,
                TickerSymbol NVARCHAR(20) NOT NULL,
                ReportDate DATE NOT NULL,
                IndicatorNameCN NVARCHAR(100) NOT NULL,
                IndicatorNameEN NVARCHAR(100) NOT NULL,
                IndicatorDesc NVARCHAR(MAX),
                FormulaDesc NVARCHAR(MAX),
                Formula NVARCHAR(MAX),
                FormulaEN NVARCHAR(MAX),
                ErrorMessage NVARCHAR(MAX),
                ProcessedFormula NVARCHAR(MAX),
                CreateTime DATETIME DEFAULT GETDATE()
            )
            """)
            print("已创建L2MetricsErrorLog表")
        else:
            # 检查是否有IndicatorDesc列
            cursor.execute("""
            SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_NAME = 'L2MetricsErrorLog' AND COLUMN_NAME = 'IndicatorDesc'
            """)
            column_exists = cursor.fetchone()[0] > 0
            if not column_exists:
                cursor.execute("ALTER TABLE L2MetricsErrorLog ADD IndicatorDesc NVARCHAR(MAX)")
                print("已添加IndicatorDesc列到L2MetricsErrorLog表")

            # 检查是否有Formula列
            cursor.execute("""
            SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_NAME = 'L2MetricsErrorLog' AND COLUMN_NAME = 'Formula'
            """)
            column_exists = cursor.fetchone()[0] > 0
            if not column_exists:
                cursor.execute("ALTER TABLE L2MetricsErrorLog ADD Formula NVARCHAR(MAX)")
                print("已添加Formula列到L2MetricsErrorLog表")

    def _ensure_l2_metrics_calculation_log_table(self, cursor):
        """确保L2MetricsCalculationLog表结构正确"""
        cursor.execute("""
        SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES
        WHERE TABLE_NAME = 'L2MetricsCalculationLog'
        """)
        table_exists = cursor.fetchone()[0] > 0

        if not table_exists:
            try:
                cursor.execute("""
                CREATE TABLE L2MetricsCalculationLog (
                    ID INT IDENTITY(1,1) PRIMARY KEY,
                    TickerSymbol NVARCHAR(20) NOT NULL,
                    ReportDate DATE NOT NULL,
                    CalculationTime DATETIME DEFAULT GETDATE(),
                    SuccessCount INT,
                    FailureCount INT,
                    ExecutionTime DECIMAL(10, 3),
                    LogMessage NVARCHAR(MAX)
                )
                """)
                print("已创建L2MetricsCalculationLog表")
            except Exception as e:
                print(f"创建 L2MetricsCalculationLog 表时出错: {str(e)}")
        # 如果表已存在，则不进行操作
