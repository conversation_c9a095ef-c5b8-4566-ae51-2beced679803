('D:\\Codes\\Fin\\windgather\\build\\integrated_processor\\PYZ-00.pyz',
 [('IPython',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\__init__.py',
   'PYMODULE'),
  ('IPython.core',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\core\\__init__.py',
   'PYMODULE'),
  ('IPython.core.alias',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\core\\alias.py',
   'PYMODULE'),
  ('IPython.core.application',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\core\\application.py',
   'PYMODULE'),
  ('IPython.core.async_helpers',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\core\\async_helpers.py',
   'PYMODULE'),
  ('IPython.core.autocall',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\core\\autocall.py',
   'PYMODULE'),
  ('IPython.core.builtin_trap',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\core\\builtin_trap.py',
   'PYMODULE'),
  ('IPython.core.compilerop',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\core\\compilerop.py',
   'PYMODULE'),
  ('IPython.core.completer',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\core\\completer.py',
   'PYMODULE'),
  ('IPython.core.completerlib',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\core\\completerlib.py',
   'PYMODULE'),
  ('IPython.core.crashhandler',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\core\\crashhandler.py',
   'PYMODULE'),
  ('IPython.core.debugger',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\core\\debugger.py',
   'PYMODULE'),
  ('IPython.core.display',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\core\\display.py',
   'PYMODULE'),
  ('IPython.core.display_functions',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\core\\display_functions.py',
   'PYMODULE'),
  ('IPython.core.display_trap',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\core\\display_trap.py',
   'PYMODULE'),
  ('IPython.core.displayhook',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\core\\displayhook.py',
   'PYMODULE'),
  ('IPython.core.displaypub',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\core\\displaypub.py',
   'PYMODULE'),
  ('IPython.core.error',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\core\\error.py',
   'PYMODULE'),
  ('IPython.core.events',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\core\\events.py',
   'PYMODULE'),
  ('IPython.core.excolors',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\core\\excolors.py',
   'PYMODULE'),
  ('IPython.core.extensions',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\core\\extensions.py',
   'PYMODULE'),
  ('IPython.core.formatters',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\core\\formatters.py',
   'PYMODULE'),
  ('IPython.core.getipython',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\core\\getipython.py',
   'PYMODULE'),
  ('IPython.core.guarded_eval',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\core\\guarded_eval.py',
   'PYMODULE'),
  ('IPython.core.history',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\core\\history.py',
   'PYMODULE'),
  ('IPython.core.hooks',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\core\\hooks.py',
   'PYMODULE'),
  ('IPython.core.inputtransformer2',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\core\\inputtransformer2.py',
   'PYMODULE'),
  ('IPython.core.interactiveshell',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\core\\interactiveshell.py',
   'PYMODULE'),
  ('IPython.core.latex_symbols',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\core\\latex_symbols.py',
   'PYMODULE'),
  ('IPython.core.logger',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\core\\logger.py',
   'PYMODULE'),
  ('IPython.core.macro',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\core\\macro.py',
   'PYMODULE'),
  ('IPython.core.magic',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\core\\magic.py',
   'PYMODULE'),
  ('IPython.core.magic_arguments',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\core\\magic_arguments.py',
   'PYMODULE'),
  ('IPython.core.magics',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\core\\magics\\__init__.py',
   'PYMODULE'),
  ('IPython.core.magics.auto',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\core\\magics\\auto.py',
   'PYMODULE'),
  ('IPython.core.magics.basic',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\core\\magics\\basic.py',
   'PYMODULE'),
  ('IPython.core.magics.code',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\core\\magics\\code.py',
   'PYMODULE'),
  ('IPython.core.magics.config',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\core\\magics\\config.py',
   'PYMODULE'),
  ('IPython.core.magics.display',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\core\\magics\\display.py',
   'PYMODULE'),
  ('IPython.core.magics.execution',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\core\\magics\\execution.py',
   'PYMODULE'),
  ('IPython.core.magics.extension',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\core\\magics\\extension.py',
   'PYMODULE'),
  ('IPython.core.magics.history',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\core\\magics\\history.py',
   'PYMODULE'),
  ('IPython.core.magics.logging',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\core\\magics\\logging.py',
   'PYMODULE'),
  ('IPython.core.magics.namespace',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\core\\magics\\namespace.py',
   'PYMODULE'),
  ('IPython.core.magics.osm',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\core\\magics\\osm.py',
   'PYMODULE'),
  ('IPython.core.magics.packaging',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\core\\magics\\packaging.py',
   'PYMODULE'),
  ('IPython.core.magics.pylab',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\core\\magics\\pylab.py',
   'PYMODULE'),
  ('IPython.core.magics.script',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\core\\magics\\script.py',
   'PYMODULE'),
  ('IPython.core.oinspect',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\core\\oinspect.py',
   'PYMODULE'),
  ('IPython.core.page',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\core\\page.py',
   'PYMODULE'),
  ('IPython.core.payload',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\core\\payload.py',
   'PYMODULE'),
  ('IPython.core.prefilter',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\core\\prefilter.py',
   'PYMODULE'),
  ('IPython.core.profiledir',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\core\\profiledir.py',
   'PYMODULE'),
  ('IPython.core.pylabtools',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\core\\pylabtools.py',
   'PYMODULE'),
  ('IPython.core.release',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\core\\release.py',
   'PYMODULE'),
  ('IPython.core.shellapp',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\core\\shellapp.py',
   'PYMODULE'),
  ('IPython.core.splitinput',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\core\\splitinput.py',
   'PYMODULE'),
  ('IPython.core.ultratb',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\core\\ultratb.py',
   'PYMODULE'),
  ('IPython.core.usage',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\core\\usage.py',
   'PYMODULE'),
  ('IPython.display',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\display.py',
   'PYMODULE'),
  ('IPython.extensions',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\extensions\\__init__.py',
   'PYMODULE'),
  ('IPython.extensions.storemagic',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\extensions\\storemagic.py',
   'PYMODULE'),
  ('IPython.external',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\external\\__init__.py',
   'PYMODULE'),
  ('IPython.external.qt_for_kernel',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\external\\qt_for_kernel.py',
   'PYMODULE'),
  ('IPython.external.qt_loaders',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\external\\qt_loaders.py',
   'PYMODULE'),
  ('IPython.lib',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\lib\\__init__.py',
   'PYMODULE'),
  ('IPython.lib.clipboard',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\lib\\clipboard.py',
   'PYMODULE'),
  ('IPython.lib.display',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\lib\\display.py',
   'PYMODULE'),
  ('IPython.lib.pretty',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\lib\\pretty.py',
   'PYMODULE'),
  ('IPython.paths',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\paths.py',
   'PYMODULE'),
  ('IPython.terminal',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\terminal\\__init__.py',
   'PYMODULE'),
  ('IPython.terminal.debugger',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\terminal\\debugger.py',
   'PYMODULE'),
  ('IPython.terminal.embed',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\terminal\\embed.py',
   'PYMODULE'),
  ('IPython.terminal.interactiveshell',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\terminal\\interactiveshell.py',
   'PYMODULE'),
  ('IPython.terminal.ipapp',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\terminal\\ipapp.py',
   'PYMODULE'),
  ('IPython.terminal.magics',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\terminal\\magics.py',
   'PYMODULE'),
  ('IPython.terminal.prompts',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\terminal\\prompts.py',
   'PYMODULE'),
  ('IPython.terminal.pt_inputhooks',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\terminal\\pt_inputhooks\\__init__.py',
   'PYMODULE'),
  ('IPython.terminal.ptutils',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\terminal\\ptutils.py',
   'PYMODULE'),
  ('IPython.terminal.shortcuts',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\terminal\\shortcuts\\__init__.py',
   'PYMODULE'),
  ('IPython.terminal.shortcuts.auto_match',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\terminal\\shortcuts\\auto_match.py',
   'PYMODULE'),
  ('IPython.terminal.shortcuts.auto_suggest',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\terminal\\shortcuts\\auto_suggest.py',
   'PYMODULE'),
  ('IPython.terminal.shortcuts.filters',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\terminal\\shortcuts\\filters.py',
   'PYMODULE'),
  ('IPython.testing',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\testing\\__init__.py',
   'PYMODULE'),
  ('IPython.testing.skipdoctest',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\testing\\skipdoctest.py',
   'PYMODULE'),
  ('IPython.utils',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\utils\\__init__.py',
   'PYMODULE'),
  ('IPython.utils.PyColorize',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\utils\\PyColorize.py',
   'PYMODULE'),
  ('IPython.utils._process_cli',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\utils\\_process_cli.py',
   'PYMODULE'),
  ('IPython.utils._process_common',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\utils\\_process_common.py',
   'PYMODULE'),
  ('IPython.utils._process_posix',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\utils\\_process_posix.py',
   'PYMODULE'),
  ('IPython.utils._process_win32',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\utils\\_process_win32.py',
   'PYMODULE'),
  ('IPython.utils._sysinfo',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\utils\\_sysinfo.py',
   'PYMODULE'),
  ('IPython.utils.capture',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\utils\\capture.py',
   'PYMODULE'),
  ('IPython.utils.colorable',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\utils\\colorable.py',
   'PYMODULE'),
  ('IPython.utils.coloransi',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\utils\\coloransi.py',
   'PYMODULE'),
  ('IPython.utils.contexts',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\utils\\contexts.py',
   'PYMODULE'),
  ('IPython.utils.data',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\utils\\data.py',
   'PYMODULE'),
  ('IPython.utils.decorators',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\utils\\decorators.py',
   'PYMODULE'),
  ('IPython.utils.dir2',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\utils\\dir2.py',
   'PYMODULE'),
  ('IPython.utils.docs',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\utils\\docs.py',
   'PYMODULE'),
  ('IPython.utils.encoding',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\utils\\encoding.py',
   'PYMODULE'),
  ('IPython.utils.frame',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\utils\\frame.py',
   'PYMODULE'),
  ('IPython.utils.generics',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\utils\\generics.py',
   'PYMODULE'),
  ('IPython.utils.importstring',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\utils\\importstring.py',
   'PYMODULE'),
  ('IPython.utils.io',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\utils\\io.py',
   'PYMODULE'),
  ('IPython.utils.ipstruct',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\utils\\ipstruct.py',
   'PYMODULE'),
  ('IPython.utils.module_paths',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\utils\\module_paths.py',
   'PYMODULE'),
  ('IPython.utils.openpy',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\utils\\openpy.py',
   'PYMODULE'),
  ('IPython.utils.path',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\utils\\path.py',
   'PYMODULE'),
  ('IPython.utils.process',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\utils\\process.py',
   'PYMODULE'),
  ('IPython.utils.py3compat',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\utils\\py3compat.py',
   'PYMODULE'),
  ('IPython.utils.sentinel',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\utils\\sentinel.py',
   'PYMODULE'),
  ('IPython.utils.strdispatch',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\utils\\strdispatch.py',
   'PYMODULE'),
  ('IPython.utils.sysinfo',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\utils\\sysinfo.py',
   'PYMODULE'),
  ('IPython.utils.syspathcontext',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\utils\\syspathcontext.py',
   'PYMODULE'),
  ('IPython.utils.terminal',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\utils\\terminal.py',
   'PYMODULE'),
  ('IPython.utils.text',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\utils\\text.py',
   'PYMODULE'),
  ('IPython.utils.timing',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\utils\\timing.py',
   'PYMODULE'),
  ('IPython.utils.tokenutil',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\utils\\tokenutil.py',
   'PYMODULE'),
  ('IPython.utils.wildcard',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\IPython\\utils\\wildcard.py',
   'PYMODULE'),
  ('WindPy', 'C:\\Wind\\Wind.NET.Client\\WindNET\\x64\\WindPy.py', 'PYMODULE'),
  ('__future__',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\__future__.py',
   'PYMODULE'),
  ('_aix_support',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\_aix_support.py',
   'PYMODULE'),
  ('_bootsubprocess',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\_bootsubprocess.py',
   'PYMODULE'),
  ('_compat_pickle',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\_compat_pickle.py',
   'PYMODULE'),
  ('_compression',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\_compression.py',
   'PYMODULE'),
  ('_markupbase',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\_markupbase.py',
   'PYMODULE'),
  ('_py_abc',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\_py_abc.py',
   'PYMODULE'),
  ('_pydecimal',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\_pydecimal.py',
   'PYMODULE'),
  ('_sitebuiltins',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\_sitebuiltins.py',
   'PYMODULE'),
  ('_strptime',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\_strptime.py',
   'PYMODULE'),
  ('_threading_local',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\_threading_local.py',
   'PYMODULE'),
  ('argparse',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\argparse.py',
   'PYMODULE'),
  ('ast',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\ast.py',
   'PYMODULE'),
  ('asttokens',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\asttokens\\__init__.py',
   'PYMODULE'),
  ('asttokens.astroid_compat',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\asttokens\\astroid_compat.py',
   'PYMODULE'),
  ('asttokens.asttokens',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\asttokens\\asttokens.py',
   'PYMODULE'),
  ('asttokens.line_numbers',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\asttokens\\line_numbers.py',
   'PYMODULE'),
  ('asttokens.mark_tokens',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\asttokens\\mark_tokens.py',
   'PYMODULE'),
  ('asttokens.util',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\asttokens\\util.py',
   'PYMODULE'),
  ('asyncio',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\asyncio\\__init__.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.constants',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.events',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.locks',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.log',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\asyncio\\log.py',
   'PYMODULE'),
  ('asyncio.mixins',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\asyncio\\mixins.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.queues',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.streams',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.taskgroups',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\asyncio\\taskgroups.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('asyncio.threads',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\asyncio\\threads.py',
   'PYMODULE'),
  ('asyncio.timeouts',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\asyncio\\timeouts.py',
   'PYMODULE'),
  ('asyncio.transports',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('attr',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\attr\\__init__.py',
   'PYMODULE'),
  ('attr._cmp',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\attr\\_cmp.py',
   'PYMODULE'),
  ('attr._compat',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\attr\\_compat.py',
   'PYMODULE'),
  ('attr._config',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\attr\\_config.py',
   'PYMODULE'),
  ('attr._funcs',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\attr\\_funcs.py',
   'PYMODULE'),
  ('attr._make',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\attr\\_make.py',
   'PYMODULE'),
  ('attr._next_gen',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\attr\\_next_gen.py',
   'PYMODULE'),
  ('attr._version_info',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\attr\\_version_info.py',
   'PYMODULE'),
  ('attr.converters',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\attr\\converters.py',
   'PYMODULE'),
  ('attr.exceptions',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\attr\\exceptions.py',
   'PYMODULE'),
  ('attr.filters',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\attr\\filters.py',
   'PYMODULE'),
  ('attr.setters',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\attr\\setters.py',
   'PYMODULE'),
  ('attr.validators',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\attr\\validators.py',
   'PYMODULE'),
  ('attrs',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\attrs\\__init__.py',
   'PYMODULE'),
  ('attrs.converters',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\attrs\\converters.py',
   'PYMODULE'),
  ('attrs.exceptions',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\attrs\\exceptions.py',
   'PYMODULE'),
  ('attrs.filters',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\attrs\\filters.py',
   'PYMODULE'),
  ('attrs.setters',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\attrs\\setters.py',
   'PYMODULE'),
  ('attrs.validators',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\attrs\\validators.py',
   'PYMODULE'),
  ('backcall',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\backcall\\__init__.py',
   'PYMODULE'),
  ('backcall._signatures',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\backcall\\_signatures.py',
   'PYMODULE'),
  ('backcall.backcall',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\backcall\\backcall.py',
   'PYMODULE'),
  ('base64',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\base64.py',
   'PYMODULE'),
  ('bdb',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\bdb.py',
   'PYMODULE'),
  ('bisect',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\bisect.py',
   'PYMODULE'),
  ('bs4',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\bs4\\__init__.py',
   'PYMODULE'),
  ('bs4._deprecation',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\bs4\\_deprecation.py',
   'PYMODULE'),
  ('bs4._typing',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\bs4\\_typing.py',
   'PYMODULE'),
  ('bs4._warnings',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\bs4\\_warnings.py',
   'PYMODULE'),
  ('bs4.builder',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\bs4\\builder\\__init__.py',
   'PYMODULE'),
  ('bs4.builder._html5lib',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\bs4\\builder\\_html5lib.py',
   'PYMODULE'),
  ('bs4.builder._htmlparser',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\bs4\\builder\\_htmlparser.py',
   'PYMODULE'),
  ('bs4.builder._lxml',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\bs4\\builder\\_lxml.py',
   'PYMODULE'),
  ('bs4.css',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\bs4\\css.py',
   'PYMODULE'),
  ('bs4.dammit',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\bs4\\dammit.py',
   'PYMODULE'),
  ('bs4.element',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\bs4\\element.py',
   'PYMODULE'),
  ('bs4.exceptions',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\bs4\\exceptions.py',
   'PYMODULE'),
  ('bs4.filter',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\bs4\\filter.py',
   'PYMODULE'),
  ('bs4.formatter',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\bs4\\formatter.py',
   'PYMODULE'),
  ('bz2',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\bz2.py',
   'PYMODULE'),
  ('cProfile',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\cProfile.py',
   'PYMODULE'),
  ('calendar',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\calendar.py',
   'PYMODULE'),
  ('certifi',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('charset_normalizer',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('cmd',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\cmd.py',
   'PYMODULE'),
  ('code',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\code.py',
   'PYMODULE'),
  ('codeop',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\codeop.py',
   'PYMODULE'),
  ('colorama',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\colorama\\__init__.py',
   'PYMODULE'),
  ('colorama.ansi',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\colorama\\ansi.py',
   'PYMODULE'),
  ('colorama.ansitowin32',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\colorama\\ansitowin32.py',
   'PYMODULE'),
  ('colorama.initialise',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\colorama\\initialise.py',
   'PYMODULE'),
  ('colorama.win32',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\colorama\\win32.py',
   'PYMODULE'),
  ('colorama.winterm',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\colorama\\winterm.py',
   'PYMODULE'),
  ('colorsys',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\colorsys.py',
   'PYMODULE'),
  ('commctrl',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\win32\\lib\\commctrl.py',
   'PYMODULE'),
  ('concurrent',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('configparser',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\configparser.py',
   'PYMODULE'),
  ('contextlib',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\contextlib.py',
   'PYMODULE'),
  ('contextvars',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\contextvars.py',
   'PYMODULE'),
  ('copy',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\copy.py',
   'PYMODULE'),
  ('csv',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\csv.py',
   'PYMODULE'),
  ('ctypes',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes._endian',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('curses',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\curses\\__init__.py',
   'PYMODULE'),
  ('curses.has_key',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\curses\\has_key.py',
   'PYMODULE'),
  ('dataclasses',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\dataclasses.py',
   'PYMODULE'),
  ('datetime',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\datetime.py',
   'PYMODULE'),
  ('dateutil',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\dateutil\\__init__.py',
   'PYMODULE'),
  ('dateutil._common',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\dateutil\\_common.py',
   'PYMODULE'),
  ('dateutil._version',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\dateutil\\_version.py',
   'PYMODULE'),
  ('dateutil.easter',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\dateutil\\easter.py',
   'PYMODULE'),
  ('dateutil.parser',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\dateutil\\parser\\__init__.py',
   'PYMODULE'),
  ('dateutil.parser._parser',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\dateutil\\parser\\_parser.py',
   'PYMODULE'),
  ('dateutil.parser.isoparser',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\dateutil\\parser\\isoparser.py',
   'PYMODULE'),
  ('dateutil.relativedelta',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\dateutil\\relativedelta.py',
   'PYMODULE'),
  ('dateutil.rrule',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\dateutil\\rrule.py',
   'PYMODULE'),
  ('dateutil.tz',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\dateutil\\tz\\__init__.py',
   'PYMODULE'),
  ('dateutil.tz._common',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\dateutil\\tz\\_common.py',
   'PYMODULE'),
  ('dateutil.tz._factories',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\dateutil\\tz\\_factories.py',
   'PYMODULE'),
  ('dateutil.tz.tz',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\dateutil\\tz\\tz.py',
   'PYMODULE'),
  ('dateutil.tz.win',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\dateutil\\tz\\win.py',
   'PYMODULE'),
  ('dateutil.zoneinfo',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\dateutil\\zoneinfo\\__init__.py',
   'PYMODULE'),
  ('decimal',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\decimal.py',
   'PYMODULE'),
  ('decorator',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\decorator.py',
   'PYMODULE'),
  ('difflib',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\difflib.py',
   'PYMODULE'),
  ('dis',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\dis.py',
   'PYMODULE'),
  ('doctest',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\doctest.py',
   'PYMODULE'),
  ('email',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\email\\__init__.py',
   'PYMODULE'),
  ('email._encoded_words',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\email\\charset.py',
   'PYMODULE'),
  ('email.contentmanager',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\email\\errors.py',
   'PYMODULE'),
  ('email.feedparser',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.generator',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\email\\generator.py',
   'PYMODULE'),
  ('email.header',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\email\\header.py',
   'PYMODULE'),
  ('email.headerregistry',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.message',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\email\\message.py',
   'PYMODULE'),
  ('email.parser',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\email\\parser.py',
   'PYMODULE'),
  ('email.policy',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\email\\policy.py',
   'PYMODULE'),
  ('email.quoprimime',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.utils',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\email\\utils.py',
   'PYMODULE'),
  ('executing',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\executing\\__init__.py',
   'PYMODULE'),
  ('executing._exceptions',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\executing\\_exceptions.py',
   'PYMODULE'),
  ('executing._position_node_finder',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\executing\\_position_node_finder.py',
   'PYMODULE'),
  ('executing._pytest_utils',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\executing\\_pytest_utils.py',
   'PYMODULE'),
  ('executing.executing',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\executing\\executing.py',
   'PYMODULE'),
  ('executing.version',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\executing\\version.py',
   'PYMODULE'),
  ('fastjsonschema',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\fastjsonschema\\__init__.py',
   'PYMODULE'),
  ('fastjsonschema.draft04',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\fastjsonschema\\draft04.py',
   'PYMODULE'),
  ('fastjsonschema.draft06',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\fastjsonschema\\draft06.py',
   'PYMODULE'),
  ('fastjsonschema.draft07',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\fastjsonschema\\draft07.py',
   'PYMODULE'),
  ('fastjsonschema.exceptions',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\fastjsonschema\\exceptions.py',
   'PYMODULE'),
  ('fastjsonschema.generator',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\fastjsonschema\\generator.py',
   'PYMODULE'),
  ('fastjsonschema.indent',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\fastjsonschema\\indent.py',
   'PYMODULE'),
  ('fastjsonschema.ref_resolver',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\fastjsonschema\\ref_resolver.py',
   'PYMODULE'),
  ('fastjsonschema.version',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\fastjsonschema\\version.py',
   'PYMODULE'),
  ('filecmp',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\filecmp.py',
   'PYMODULE'),
  ('fileinput',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\fileinput.py',
   'PYMODULE'),
  ('fnmatch',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\fnmatch.py',
   'PYMODULE'),
  ('fractions',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\fractions.py',
   'PYMODULE'),
  ('ftplib',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\ftplib.py',
   'PYMODULE'),
  ('getopt',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\getopt.py',
   'PYMODULE'),
  ('getpass',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\getpass.py',
   'PYMODULE'),
  ('gettext',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\gettext.py',
   'PYMODULE'),
  ('glob',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\glob.py',
   'PYMODULE'),
  ('gzip',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\gzip.py',
   'PYMODULE'),
  ('hashlib',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\hashlib.py',
   'PYMODULE'),
  ('hmac',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\hmac.py',
   'PYMODULE'),
  ('html',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\html\\__init__.py',
   'PYMODULE'),
  ('html.entities',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\html\\entities.py',
   'PYMODULE'),
  ('html.parser',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\html\\parser.py',
   'PYMODULE'),
  ('http',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\http\\__init__.py',
   'PYMODULE'),
  ('http.client',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\http\\client.py',
   'PYMODULE'),
  ('http.cookiejar',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http.cookies',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\http\\cookies.py',
   'PYMODULE'),
  ('http.server',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\http\\server.py',
   'PYMODULE'),
  ('idna',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\idna\\__init__.py',
   'PYMODULE'),
  ('idna.core',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\idna\\core.py',
   'PYMODULE'),
  ('idna.idnadata',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('idna.intranges',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.package_data',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('idna.uts46data',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('imp',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\imp.py',
   'PYMODULE'),
  ('importlib',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._abc',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('importlib.readers',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\importlib\\resources\\_legacy.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.util',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\importlib\\util.py',
   'PYMODULE'),
  ('inspect',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\inspect.py',
   'PYMODULE'),
  ('ipaddress',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\ipaddress.py',
   'PYMODULE'),
  ('jedi',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jedi\\__init__.py',
   'PYMODULE'),
  ('jedi._compatibility',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jedi\\_compatibility.py',
   'PYMODULE'),
  ('jedi.api',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jedi\\api\\__init__.py',
   'PYMODULE'),
  ('jedi.api.classes',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jedi\\api\\classes.py',
   'PYMODULE'),
  ('jedi.api.completion',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jedi\\api\\completion.py',
   'PYMODULE'),
  ('jedi.api.completion_cache',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jedi\\api\\completion_cache.py',
   'PYMODULE'),
  ('jedi.api.environment',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jedi\\api\\environment.py',
   'PYMODULE'),
  ('jedi.api.errors',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jedi\\api\\errors.py',
   'PYMODULE'),
  ('jedi.api.exceptions',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jedi\\api\\exceptions.py',
   'PYMODULE'),
  ('jedi.api.file_name',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jedi\\api\\file_name.py',
   'PYMODULE'),
  ('jedi.api.helpers',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jedi\\api\\helpers.py',
   'PYMODULE'),
  ('jedi.api.interpreter',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jedi\\api\\interpreter.py',
   'PYMODULE'),
  ('jedi.api.keywords',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jedi\\api\\keywords.py',
   'PYMODULE'),
  ('jedi.api.project',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jedi\\api\\project.py',
   'PYMODULE'),
  ('jedi.api.refactoring',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jedi\\api\\refactoring\\__init__.py',
   'PYMODULE'),
  ('jedi.api.refactoring.extract',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jedi\\api\\refactoring\\extract.py',
   'PYMODULE'),
  ('jedi.api.strings',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jedi\\api\\strings.py',
   'PYMODULE'),
  ('jedi.cache',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jedi\\cache.py',
   'PYMODULE'),
  ('jedi.common',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jedi\\common.py',
   'PYMODULE'),
  ('jedi.debug',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jedi\\debug.py',
   'PYMODULE'),
  ('jedi.file_io',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jedi\\file_io.py',
   'PYMODULE'),
  ('jedi.inference',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jedi\\inference\\__init__.py',
   'PYMODULE'),
  ('jedi.inference.analysis',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jedi\\inference\\analysis.py',
   'PYMODULE'),
  ('jedi.inference.arguments',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jedi\\inference\\arguments.py',
   'PYMODULE'),
  ('jedi.inference.base_value',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jedi\\inference\\base_value.py',
   'PYMODULE'),
  ('jedi.inference.cache',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jedi\\inference\\cache.py',
   'PYMODULE'),
  ('jedi.inference.compiled',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jedi\\inference\\compiled\\__init__.py',
   'PYMODULE'),
  ('jedi.inference.compiled.access',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jedi\\inference\\compiled\\access.py',
   'PYMODULE'),
  ('jedi.inference.compiled.getattr_static',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jedi\\inference\\compiled\\getattr_static.py',
   'PYMODULE'),
  ('jedi.inference.compiled.mixed',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jedi\\inference\\compiled\\mixed.py',
   'PYMODULE'),
  ('jedi.inference.compiled.subprocess',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jedi\\inference\\compiled\\subprocess\\__init__.py',
   'PYMODULE'),
  ('jedi.inference.compiled.subprocess.functions',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jedi\\inference\\compiled\\subprocess\\functions.py',
   'PYMODULE'),
  ('jedi.inference.compiled.value',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jedi\\inference\\compiled\\value.py',
   'PYMODULE'),
  ('jedi.inference.context',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jedi\\inference\\context.py',
   'PYMODULE'),
  ('jedi.inference.docstring_utils',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jedi\\inference\\docstring_utils.py',
   'PYMODULE'),
  ('jedi.inference.docstrings',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jedi\\inference\\docstrings.py',
   'PYMODULE'),
  ('jedi.inference.dynamic_params',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jedi\\inference\\dynamic_params.py',
   'PYMODULE'),
  ('jedi.inference.filters',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jedi\\inference\\filters.py',
   'PYMODULE'),
  ('jedi.inference.finder',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jedi\\inference\\finder.py',
   'PYMODULE'),
  ('jedi.inference.flow_analysis',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jedi\\inference\\flow_analysis.py',
   'PYMODULE'),
  ('jedi.inference.gradual',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jedi\\inference\\gradual\\__init__.py',
   'PYMODULE'),
  ('jedi.inference.gradual.annotation',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jedi\\inference\\gradual\\annotation.py',
   'PYMODULE'),
  ('jedi.inference.gradual.base',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jedi\\inference\\gradual\\base.py',
   'PYMODULE'),
  ('jedi.inference.gradual.conversion',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jedi\\inference\\gradual\\conversion.py',
   'PYMODULE'),
  ('jedi.inference.gradual.generics',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jedi\\inference\\gradual\\generics.py',
   'PYMODULE'),
  ('jedi.inference.gradual.stub_value',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jedi\\inference\\gradual\\stub_value.py',
   'PYMODULE'),
  ('jedi.inference.gradual.type_var',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jedi\\inference\\gradual\\type_var.py',
   'PYMODULE'),
  ('jedi.inference.gradual.typeshed',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jedi\\inference\\gradual\\typeshed.py',
   'PYMODULE'),
  ('jedi.inference.gradual.typing',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jedi\\inference\\gradual\\typing.py',
   'PYMODULE'),
  ('jedi.inference.gradual.utils',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jedi\\inference\\gradual\\utils.py',
   'PYMODULE'),
  ('jedi.inference.helpers',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jedi\\inference\\helpers.py',
   'PYMODULE'),
  ('jedi.inference.imports',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jedi\\inference\\imports.py',
   'PYMODULE'),
  ('jedi.inference.lazy_value',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jedi\\inference\\lazy_value.py',
   'PYMODULE'),
  ('jedi.inference.names',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jedi\\inference\\names.py',
   'PYMODULE'),
  ('jedi.inference.param',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jedi\\inference\\param.py',
   'PYMODULE'),
  ('jedi.inference.parser_cache',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jedi\\inference\\parser_cache.py',
   'PYMODULE'),
  ('jedi.inference.recursion',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jedi\\inference\\recursion.py',
   'PYMODULE'),
  ('jedi.inference.references',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jedi\\inference\\references.py',
   'PYMODULE'),
  ('jedi.inference.signature',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jedi\\inference\\signature.py',
   'PYMODULE'),
  ('jedi.inference.star_args',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jedi\\inference\\star_args.py',
   'PYMODULE'),
  ('jedi.inference.syntax_tree',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jedi\\inference\\syntax_tree.py',
   'PYMODULE'),
  ('jedi.inference.sys_path',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jedi\\inference\\sys_path.py',
   'PYMODULE'),
  ('jedi.inference.utils',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jedi\\inference\\utils.py',
   'PYMODULE'),
  ('jedi.inference.value',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jedi\\inference\\value\\__init__.py',
   'PYMODULE'),
  ('jedi.inference.value.decorator',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jedi\\inference\\value\\decorator.py',
   'PYMODULE'),
  ('jedi.inference.value.dynamic_arrays',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jedi\\inference\\value\\dynamic_arrays.py',
   'PYMODULE'),
  ('jedi.inference.value.function',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jedi\\inference\\value\\function.py',
   'PYMODULE'),
  ('jedi.inference.value.instance',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jedi\\inference\\value\\instance.py',
   'PYMODULE'),
  ('jedi.inference.value.iterable',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jedi\\inference\\value\\iterable.py',
   'PYMODULE'),
  ('jedi.inference.value.klass',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jedi\\inference\\value\\klass.py',
   'PYMODULE'),
  ('jedi.inference.value.module',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jedi\\inference\\value\\module.py',
   'PYMODULE'),
  ('jedi.inference.value.namespace',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jedi\\inference\\value\\namespace.py',
   'PYMODULE'),
  ('jedi.parser_utils',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jedi\\parser_utils.py',
   'PYMODULE'),
  ('jedi.plugins',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jedi\\plugins\\__init__.py',
   'PYMODULE'),
  ('jedi.plugins.django',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jedi\\plugins\\django.py',
   'PYMODULE'),
  ('jedi.plugins.flask',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jedi\\plugins\\flask.py',
   'PYMODULE'),
  ('jedi.plugins.pytest',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jedi\\plugins\\pytest.py',
   'PYMODULE'),
  ('jedi.plugins.registry',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jedi\\plugins\\registry.py',
   'PYMODULE'),
  ('jedi.plugins.stdlib',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jedi\\plugins\\stdlib.py',
   'PYMODULE'),
  ('jedi.settings',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jedi\\settings.py',
   'PYMODULE'),
  ('jinja2',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jinja2\\__init__.py',
   'PYMODULE'),
  ('jinja2._identifier',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jinja2\\_identifier.py',
   'PYMODULE'),
  ('jinja2.async_utils',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jinja2\\async_utils.py',
   'PYMODULE'),
  ('jinja2.bccache',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jinja2\\bccache.py',
   'PYMODULE'),
  ('jinja2.compiler',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jinja2\\compiler.py',
   'PYMODULE'),
  ('jinja2.constants',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jinja2\\constants.py',
   'PYMODULE'),
  ('jinja2.debug',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jinja2\\debug.py',
   'PYMODULE'),
  ('jinja2.defaults',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jinja2\\defaults.py',
   'PYMODULE'),
  ('jinja2.environment',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jinja2\\environment.py',
   'PYMODULE'),
  ('jinja2.exceptions',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jinja2\\exceptions.py',
   'PYMODULE'),
  ('jinja2.ext',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jinja2\\ext.py',
   'PYMODULE'),
  ('jinja2.filters',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jinja2\\filters.py',
   'PYMODULE'),
  ('jinja2.idtracking',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jinja2\\idtracking.py',
   'PYMODULE'),
  ('jinja2.lexer',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jinja2\\lexer.py',
   'PYMODULE'),
  ('jinja2.loaders',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jinja2\\loaders.py',
   'PYMODULE'),
  ('jinja2.nodes',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jinja2\\nodes.py',
   'PYMODULE'),
  ('jinja2.optimizer',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jinja2\\optimizer.py',
   'PYMODULE'),
  ('jinja2.parser',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jinja2\\parser.py',
   'PYMODULE'),
  ('jinja2.runtime',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jinja2\\runtime.py',
   'PYMODULE'),
  ('jinja2.sandbox',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jinja2\\sandbox.py',
   'PYMODULE'),
  ('jinja2.tests',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jinja2\\tests.py',
   'PYMODULE'),
  ('jinja2.utils',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jinja2\\utils.py',
   'PYMODULE'),
  ('jinja2.visitor',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jinja2\\visitor.py',
   'PYMODULE'),
  ('json',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\json\\__init__.py',
   'PYMODULE'),
  ('json.decoder',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.encoder',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.scanner',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\json\\scanner.py',
   'PYMODULE'),
  ('jsonschema',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jsonschema\\__init__.py',
   'PYMODULE'),
  ('jsonschema._format',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jsonschema\\_format.py',
   'PYMODULE'),
  ('jsonschema._keywords',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jsonschema\\_keywords.py',
   'PYMODULE'),
  ('jsonschema._legacy_keywords',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jsonschema\\_legacy_keywords.py',
   'PYMODULE'),
  ('jsonschema._types',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jsonschema\\_types.py',
   'PYMODULE'),
  ('jsonschema._typing',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jsonschema\\_typing.py',
   'PYMODULE'),
  ('jsonschema._utils',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jsonschema\\_utils.py',
   'PYMODULE'),
  ('jsonschema.exceptions',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jsonschema\\exceptions.py',
   'PYMODULE'),
  ('jsonschema.protocols',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jsonschema\\protocols.py',
   'PYMODULE'),
  ('jsonschema.validators',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jsonschema\\validators.py',
   'PYMODULE'),
  ('jsonschema_specifications',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jsonschema_specifications\\__init__.py',
   'PYMODULE'),
  ('jsonschema_specifications._core',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\jsonschema_specifications\\_core.py',
   'PYMODULE'),
  ('logging',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\logging\\__init__.py',
   'PYMODULE'),
  ('logging.config',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\logging\\config.py',
   'PYMODULE'),
  ('logging.handlers',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\logging\\handlers.py',
   'PYMODULE'),
  ('lzma',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\lzma.py',
   'PYMODULE'),
  ('markupsafe',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\markupsafe\\__init__.py',
   'PYMODULE'),
  ('markupsafe._native',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\markupsafe\\_native.py',
   'PYMODULE'),
  ('matplotlib_inline',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\matplotlib_inline\\__init__.py',
   'PYMODULE'),
  ('matplotlib_inline.backend_inline',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\matplotlib_inline\\backend_inline.py',
   'PYMODULE'),
  ('matplotlib_inline.config',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\matplotlib_inline\\config.py',
   'PYMODULE'),
  ('mimetypes',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\mimetypes.py',
   'PYMODULE'),
  ('multiprocessing',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('nbformat',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\nbformat\\__init__.py',
   'PYMODULE'),
  ('nbformat._imports',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\nbformat\\_imports.py',
   'PYMODULE'),
  ('nbformat._struct',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\nbformat\\_struct.py',
   'PYMODULE'),
  ('nbformat._version',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\nbformat\\_version.py',
   'PYMODULE'),
  ('nbformat.converter',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\nbformat\\converter.py',
   'PYMODULE'),
  ('nbformat.corpus',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\nbformat\\corpus\\__init__.py',
   'PYMODULE'),
  ('nbformat.corpus.words',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\nbformat\\corpus\\words.py',
   'PYMODULE'),
  ('nbformat.json_compat',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\nbformat\\json_compat.py',
   'PYMODULE'),
  ('nbformat.notebooknode',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\nbformat\\notebooknode.py',
   'PYMODULE'),
  ('nbformat.reader',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\nbformat\\reader.py',
   'PYMODULE'),
  ('nbformat.sentinel',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\nbformat\\sentinel.py',
   'PYMODULE'),
  ('nbformat.v1',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\nbformat\\v1\\__init__.py',
   'PYMODULE'),
  ('nbformat.v1.convert',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\nbformat\\v1\\convert.py',
   'PYMODULE'),
  ('nbformat.v1.nbbase',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\nbformat\\v1\\nbbase.py',
   'PYMODULE'),
  ('nbformat.v1.nbjson',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\nbformat\\v1\\nbjson.py',
   'PYMODULE'),
  ('nbformat.v1.rwbase',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\nbformat\\v1\\rwbase.py',
   'PYMODULE'),
  ('nbformat.v2',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\nbformat\\v2\\__init__.py',
   'PYMODULE'),
  ('nbformat.v2.convert',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\nbformat\\v2\\convert.py',
   'PYMODULE'),
  ('nbformat.v2.nbbase',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\nbformat\\v2\\nbbase.py',
   'PYMODULE'),
  ('nbformat.v2.nbjson',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\nbformat\\v2\\nbjson.py',
   'PYMODULE'),
  ('nbformat.v2.nbpy',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\nbformat\\v2\\nbpy.py',
   'PYMODULE'),
  ('nbformat.v2.nbxml',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\nbformat\\v2\\nbxml.py',
   'PYMODULE'),
  ('nbformat.v2.rwbase',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\nbformat\\v2\\rwbase.py',
   'PYMODULE'),
  ('nbformat.v3',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\nbformat\\v3\\__init__.py',
   'PYMODULE'),
  ('nbformat.v3.convert',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\nbformat\\v3\\convert.py',
   'PYMODULE'),
  ('nbformat.v3.nbbase',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\nbformat\\v3\\nbbase.py',
   'PYMODULE'),
  ('nbformat.v3.nbjson',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\nbformat\\v3\\nbjson.py',
   'PYMODULE'),
  ('nbformat.v3.nbpy',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\nbformat\\v3\\nbpy.py',
   'PYMODULE'),
  ('nbformat.v3.rwbase',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\nbformat\\v3\\rwbase.py',
   'PYMODULE'),
  ('nbformat.v4',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\nbformat\\v4\\__init__.py',
   'PYMODULE'),
  ('nbformat.v4.convert',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\nbformat\\v4\\convert.py',
   'PYMODULE'),
  ('nbformat.v4.nbbase',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\nbformat\\v4\\nbbase.py',
   'PYMODULE'),
  ('nbformat.v4.nbjson',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\nbformat\\v4\\nbjson.py',
   'PYMODULE'),
  ('nbformat.v4.rwbase',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\nbformat\\v4\\rwbase.py',
   'PYMODULE'),
  ('nbformat.validator',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\nbformat\\validator.py',
   'PYMODULE'),
  ('nbformat.warnings',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\nbformat\\warnings.py',
   'PYMODULE'),
  ('netrc',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\netrc.py',
   'PYMODULE'),
  ('nturl2path',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\nturl2path.py',
   'PYMODULE'),
  ('numbers',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\numbers.py',
   'PYMODULE'),
  ('numpy',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\__init__.py',
   'PYMODULE'),
  ('numpy.__config__',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE'),
  ('numpy._array_api_info',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\_array_api_info.py',
   'PYMODULE'),
  ('numpy._core',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\_core\\__init__.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\_core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs_scalars',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\_core\\_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy._core._asarray',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\_core\\_asarray.py',
   'PYMODULE'),
  ('numpy._core._dtype',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\_core\\_dtype.py',
   'PYMODULE'),
  ('numpy._core._dtype_ctypes',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\_core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy._core._exceptions',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\_core\\_exceptions.py',
   'PYMODULE'),
  ('numpy._core._internal',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\_core\\_internal.py',
   'PYMODULE'),
  ('numpy._core._machar',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\_core\\_machar.py',
   'PYMODULE'),
  ('numpy._core._methods',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\_core\\_methods.py',
   'PYMODULE'),
  ('numpy._core._string_helpers',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\_core\\_string_helpers.py',
   'PYMODULE'),
  ('numpy._core._type_aliases',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\_core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy._core._ufunc_config',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\_core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy._core.arrayprint',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\_core\\arrayprint.py',
   'PYMODULE'),
  ('numpy._core.defchararray',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\_core\\defchararray.py',
   'PYMODULE'),
  ('numpy._core.einsumfunc',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\_core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy._core.fromnumeric',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\_core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy._core.function_base',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\_core\\function_base.py',
   'PYMODULE'),
  ('numpy._core.getlimits',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\_core\\getlimits.py',
   'PYMODULE'),
  ('numpy._core.memmap',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\_core\\memmap.py',
   'PYMODULE'),
  ('numpy._core.multiarray',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\_core\\multiarray.py',
   'PYMODULE'),
  ('numpy._core.numeric',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\_core\\numeric.py',
   'PYMODULE'),
  ('numpy._core.numerictypes',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\_core\\numerictypes.py',
   'PYMODULE'),
  ('numpy._core.overrides',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\_core\\overrides.py',
   'PYMODULE'),
  ('numpy._core.printoptions',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\_core\\printoptions.py',
   'PYMODULE'),
  ('numpy._core.records',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\_core\\records.py',
   'PYMODULE'),
  ('numpy._core.shape_base',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\_core\\shape_base.py',
   'PYMODULE'),
  ('numpy._core.strings',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\_core\\strings.py',
   'PYMODULE'),
  ('numpy._core.umath',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\_core\\umath.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('numpy._expired_attrs_2_0',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\_expired_attrs_2_0.py',
   'PYMODULE'),
  ('numpy._globals',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy._typing',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE'),
  ('numpy._typing._nbit_base',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\_typing\\_nbit_base.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE'),
  ('numpy._typing._ufunc',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\_typing\\_ufunc.py',
   'PYMODULE'),
  ('numpy._utils',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\_utils\\__init__.py',
   'PYMODULE'),
  ('numpy._utils._convertions',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\_utils\\_convertions.py',
   'PYMODULE'),
  ('numpy._utils._inspect',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\_utils\\_inspect.py',
   'PYMODULE'),
  ('numpy.char',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\char\\__init__.py',
   'PYMODULE'),
  ('numpy.core',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy.core._utils',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\core\\_utils.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\ctypeslib\\__init__.py',
   'PYMODULE'),
  ('numpy.ctypeslib._ctypeslib',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\ctypeslib\\_ctypeslib.py',
   'PYMODULE'),
  ('numpy.dtypes',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\dtypes.py',
   'PYMODULE'),
  ('numpy.exceptions',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\exceptions.py',
   'PYMODULE'),
  ('numpy.f2py',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\f2py\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py.__version__',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\f2py\\__version__.py',
   'PYMODULE'),
  ('numpy.f2py._backends',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\f2py\\_backends\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py._backends._backend',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\f2py\\_backends\\_backend.py',
   'PYMODULE'),
  ('numpy.f2py._backends._distutils',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\f2py\\_backends\\_distutils.py',
   'PYMODULE'),
  ('numpy.f2py._backends._meson',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\f2py\\_backends\\_meson.py',
   'PYMODULE'),
  ('numpy.f2py._isocbind',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\f2py\\_isocbind.py',
   'PYMODULE'),
  ('numpy.f2py.auxfuncs',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\f2py\\auxfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.capi_maps',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\f2py\\capi_maps.py',
   'PYMODULE'),
  ('numpy.f2py.cb_rules',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\f2py\\cb_rules.py',
   'PYMODULE'),
  ('numpy.f2py.cfuncs',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\f2py\\cfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.common_rules',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\f2py\\common_rules.py',
   'PYMODULE'),
  ('numpy.f2py.crackfortran',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\f2py\\crackfortran.py',
   'PYMODULE'),
  ('numpy.f2py.diagnose',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\f2py\\diagnose.py',
   'PYMODULE'),
  ('numpy.f2py.f2py2e',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\f2py\\f2py2e.py',
   'PYMODULE'),
  ('numpy.f2py.f90mod_rules',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\f2py\\f90mod_rules.py',
   'PYMODULE'),
  ('numpy.f2py.func2subr',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\f2py\\func2subr.py',
   'PYMODULE'),
  ('numpy.f2py.rules',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\f2py\\rules.py',
   'PYMODULE'),
  ('numpy.f2py.symbolic',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\f2py\\symbolic.py',
   'PYMODULE'),
  ('numpy.f2py.use_rules',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\f2py\\use_rules.py',
   'PYMODULE'),
  ('numpy.fft',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft._helper',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\fft\\_helper.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.lib',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib._array_utils_impl',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\lib\\_array_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraypad_impl',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\lib\\_arraypad_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraysetops_impl',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\lib\\_arraysetops_impl.py',
   'PYMODULE'),
  ('numpy.lib._arrayterator_impl',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\lib\\_arrayterator_impl.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('numpy.lib._format_impl',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\lib\\_format_impl.py',
   'PYMODULE'),
  ('numpy.lib._function_base_impl',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\lib\\_function_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._histograms_impl',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\lib\\_histograms_impl.py',
   'PYMODULE'),
  ('numpy.lib._index_tricks_impl',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\lib\\_index_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy.lib._nanfunctions_impl',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\lib\\_nanfunctions_impl.py',
   'PYMODULE'),
  ('numpy.lib._npyio_impl',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\lib\\_npyio_impl.py',
   'PYMODULE'),
  ('numpy.lib._polynomial_impl',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\lib\\_polynomial_impl.py',
   'PYMODULE'),
  ('numpy.lib._scimath_impl',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\lib\\_scimath_impl.py',
   'PYMODULE'),
  ('numpy.lib._shape_base_impl',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\lib\\_shape_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._stride_tricks_impl',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\lib\\_stride_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib._twodim_base_impl',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\lib\\_twodim_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._type_check_impl',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\lib\\_type_check_impl.py',
   'PYMODULE'),
  ('numpy.lib._ufunclike_impl',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\lib\\_ufunclike_impl.py',
   'PYMODULE'),
  ('numpy.lib._utils_impl',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\lib\\_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib.array_utils',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\lib\\array_utils.py',
   'PYMODULE'),
  ('numpy.lib.format',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('numpy.lib.introspect',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\lib\\introspect.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.lib.recfunctions',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\lib\\recfunctions.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.linalg',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy.linalg._linalg',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\linalg\\_linalg.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('numpy.ma',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.matlib',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\matlib.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('numpy.random',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE'),
  ('numpy.rec',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\rec\\__init__.py',
   'PYMODULE'),
  ('numpy.strings',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\strings\\__init__.py',
   'PYMODULE'),
  ('numpy.testing',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private.extbuild',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\testing\\_private\\extbuild.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE'),
  ('numpy.testing.overrides',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\testing\\overrides.py',
   'PYMODULE'),
  ('numpy.typing',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE'),
  ('numpy.version',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('opcode',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\opcode.py',
   'PYMODULE'),
  ('optparse',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\optparse.py',
   'PYMODULE'),
  ('packaging',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('packaging._elffile',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\packaging\\_elffile.py',
   'PYMODULE'),
  ('packaging._manylinux',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('packaging._musllinux',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('packaging._parser',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\packaging\\_parser.py',
   'PYMODULE'),
  ('packaging._structures',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('packaging.licenses',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\packaging\\licenses\\__init__.py',
   'PYMODULE'),
  ('packaging.licenses._spdx',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\packaging\\licenses\\_spdx.py',
   'PYMODULE'),
  ('packaging.markers',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\packaging\\markers.py',
   'PYMODULE'),
  ('packaging.metadata',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\packaging\\metadata.py',
   'PYMODULE'),
  ('packaging.requirements',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\packaging\\requirements.py',
   'PYMODULE'),
  ('packaging.specifiers',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\packaging\\specifiers.py',
   'PYMODULE'),
  ('packaging.tags',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\packaging\\tags.py',
   'PYMODULE'),
  ('packaging.utils',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\packaging\\utils.py',
   'PYMODULE'),
  ('packaging.version',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('pandas',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\__init__.py',
   'PYMODULE'),
  ('pandas._config',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\_config\\__init__.py',
   'PYMODULE'),
  ('pandas._config.config',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\_config\\config.py',
   'PYMODULE'),
  ('pandas._config.dates',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\_config\\dates.py',
   'PYMODULE'),
  ('pandas._config.display',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\_config\\display.py',
   'PYMODULE'),
  ('pandas._config.localization',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\_config\\localization.py',
   'PYMODULE'),
  ('pandas._libs',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\_libs\\__init__.py',
   'PYMODULE'),
  ('pandas._libs.tslibs',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\_libs\\tslibs\\__init__.py',
   'PYMODULE'),
  ('pandas._libs.window',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\_libs\\window\\__init__.py',
   'PYMODULE'),
  ('pandas._testing',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\_testing\\__init__.py',
   'PYMODULE'),
  ('pandas._testing._io',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\_testing\\_io.py',
   'PYMODULE'),
  ('pandas._testing._warnings',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\_testing\\_warnings.py',
   'PYMODULE'),
  ('pandas._testing.asserters',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\_testing\\asserters.py',
   'PYMODULE'),
  ('pandas._testing.compat',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\_testing\\compat.py',
   'PYMODULE'),
  ('pandas._testing.contexts',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\_testing\\contexts.py',
   'PYMODULE'),
  ('pandas._typing',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\_typing.py',
   'PYMODULE'),
  ('pandas._version',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\_version.py',
   'PYMODULE'),
  ('pandas._version_meson',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\_version_meson.py',
   'PYMODULE'),
  ('pandas.api',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\api\\__init__.py',
   'PYMODULE'),
  ('pandas.api.extensions',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\api\\extensions\\__init__.py',
   'PYMODULE'),
  ('pandas.api.indexers',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\api\\indexers\\__init__.py',
   'PYMODULE'),
  ('pandas.api.interchange',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\api\\interchange\\__init__.py',
   'PYMODULE'),
  ('pandas.api.types',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\api\\types\\__init__.py',
   'PYMODULE'),
  ('pandas.api.typing',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\api\\typing\\__init__.py',
   'PYMODULE'),
  ('pandas.arrays',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\arrays\\__init__.py',
   'PYMODULE'),
  ('pandas.compat',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\compat\\__init__.py',
   'PYMODULE'),
  ('pandas.compat._constants',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\compat\\_constants.py',
   'PYMODULE'),
  ('pandas.compat._optional',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\compat\\_optional.py',
   'PYMODULE'),
  ('pandas.compat.compressors',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\compat\\compressors.py',
   'PYMODULE'),
  ('pandas.compat.numpy',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\compat\\numpy\\__init__.py',
   'PYMODULE'),
  ('pandas.compat.numpy.function',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\compat\\numpy\\function.py',
   'PYMODULE'),
  ('pandas.compat.pickle_compat',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\compat\\pickle_compat.py',
   'PYMODULE'),
  ('pandas.compat.pyarrow',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\compat\\pyarrow.py',
   'PYMODULE'),
  ('pandas.core',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\__init__.py',
   'PYMODULE'),
  ('pandas.core._numba',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\_numba\\__init__.py',
   'PYMODULE'),
  ('pandas.core._numba.executor',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\_numba\\executor.py',
   'PYMODULE'),
  ('pandas.core._numba.extensions',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\_numba\\extensions.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\__init__.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.mean_',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\mean_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.min_max_',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\min_max_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.shared',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\shared.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.sum_',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\sum_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.var_',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\var_.py',
   'PYMODULE'),
  ('pandas.core.accessor',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\accessor.py',
   'PYMODULE'),
  ('pandas.core.algorithms',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\algorithms.py',
   'PYMODULE'),
  ('pandas.core.api',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\api.py',
   'PYMODULE'),
  ('pandas.core.apply',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\apply.py',
   'PYMODULE'),
  ('pandas.core.array_algos',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\array_algos\\__init__.py',
   'PYMODULE'),
  ('pandas.core.array_algos.datetimelike_accumulations',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\array_algos\\datetimelike_accumulations.py',
   'PYMODULE'),
  ('pandas.core.array_algos.masked_accumulations',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\array_algos\\masked_accumulations.py',
   'PYMODULE'),
  ('pandas.core.array_algos.masked_reductions',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\array_algos\\masked_reductions.py',
   'PYMODULE'),
  ('pandas.core.array_algos.putmask',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\array_algos\\putmask.py',
   'PYMODULE'),
  ('pandas.core.array_algos.quantile',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\array_algos\\quantile.py',
   'PYMODULE'),
  ('pandas.core.array_algos.replace',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\array_algos\\replace.py',
   'PYMODULE'),
  ('pandas.core.array_algos.take',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\array_algos\\take.py',
   'PYMODULE'),
  ('pandas.core.array_algos.transforms',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\array_algos\\transforms.py',
   'PYMODULE'),
  ('pandas.core.arraylike',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\arraylike.py',
   'PYMODULE'),
  ('pandas.core.arrays',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\arrays\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays._arrow_string_mixins',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\arrays\\_arrow_string_mixins.py',
   'PYMODULE'),
  ('pandas.core.arrays._mixins',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\arrays\\_mixins.py',
   'PYMODULE'),
  ('pandas.core.arrays._ranges',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\arrays\\_ranges.py',
   'PYMODULE'),
  ('pandas.core.arrays._utils',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\arrays\\_utils.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow._arrow_utils',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\_arrow_utils.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.accessors',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\accessors.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.array',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\array.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.extension_types',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\extension_types.py',
   'PYMODULE'),
  ('pandas.core.arrays.base',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\arrays\\base.py',
   'PYMODULE'),
  ('pandas.core.arrays.boolean',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\arrays\\boolean.py',
   'PYMODULE'),
  ('pandas.core.arrays.categorical',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\arrays\\categorical.py',
   'PYMODULE'),
  ('pandas.core.arrays.datetimelike',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\arrays\\datetimelike.py',
   'PYMODULE'),
  ('pandas.core.arrays.datetimes',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\arrays\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.arrays.floating',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\arrays\\floating.py',
   'PYMODULE'),
  ('pandas.core.arrays.integer',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\arrays\\integer.py',
   'PYMODULE'),
  ('pandas.core.arrays.interval',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\arrays\\interval.py',
   'PYMODULE'),
  ('pandas.core.arrays.masked',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\arrays\\masked.py',
   'PYMODULE'),
  ('pandas.core.arrays.numeric',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\arrays\\numeric.py',
   'PYMODULE'),
  ('pandas.core.arrays.numpy_',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\arrays\\numpy_.py',
   'PYMODULE'),
  ('pandas.core.arrays.period',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\arrays\\period.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.accessor',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\accessor.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.array',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\array.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.scipy_sparse',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\scipy_sparse.py',
   'PYMODULE'),
  ('pandas.core.arrays.string_',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\arrays\\string_.py',
   'PYMODULE'),
  ('pandas.core.arrays.string_arrow',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\arrays\\string_arrow.py',
   'PYMODULE'),
  ('pandas.core.arrays.timedeltas',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\arrays\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.base',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\base.py',
   'PYMODULE'),
  ('pandas.core.common',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\common.py',
   'PYMODULE'),
  ('pandas.core.computation',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\computation\\__init__.py',
   'PYMODULE'),
  ('pandas.core.computation.align',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\computation\\align.py',
   'PYMODULE'),
  ('pandas.core.computation.api',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\computation\\api.py',
   'PYMODULE'),
  ('pandas.core.computation.check',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\computation\\check.py',
   'PYMODULE'),
  ('pandas.core.computation.common',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\computation\\common.py',
   'PYMODULE'),
  ('pandas.core.computation.engines',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\computation\\engines.py',
   'PYMODULE'),
  ('pandas.core.computation.eval',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\computation\\eval.py',
   'PYMODULE'),
  ('pandas.core.computation.expr',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\computation\\expr.py',
   'PYMODULE'),
  ('pandas.core.computation.expressions',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\computation\\expressions.py',
   'PYMODULE'),
  ('pandas.core.computation.ops',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\computation\\ops.py',
   'PYMODULE'),
  ('pandas.core.computation.parsing',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\computation\\parsing.py',
   'PYMODULE'),
  ('pandas.core.computation.pytables',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\computation\\pytables.py',
   'PYMODULE'),
  ('pandas.core.computation.scope',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\computation\\scope.py',
   'PYMODULE'),
  ('pandas.core.config_init',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\config_init.py',
   'PYMODULE'),
  ('pandas.core.construction',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\construction.py',
   'PYMODULE'),
  ('pandas.core.dtypes',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\dtypes\\__init__.py',
   'PYMODULE'),
  ('pandas.core.dtypes.api',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\dtypes\\api.py',
   'PYMODULE'),
  ('pandas.core.dtypes.astype',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\dtypes\\astype.py',
   'PYMODULE'),
  ('pandas.core.dtypes.base',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\dtypes\\base.py',
   'PYMODULE'),
  ('pandas.core.dtypes.cast',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\dtypes\\cast.py',
   'PYMODULE'),
  ('pandas.core.dtypes.common',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\dtypes\\common.py',
   'PYMODULE'),
  ('pandas.core.dtypes.concat',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\dtypes\\concat.py',
   'PYMODULE'),
  ('pandas.core.dtypes.dtypes',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\dtypes\\dtypes.py',
   'PYMODULE'),
  ('pandas.core.dtypes.generic',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\dtypes\\generic.py',
   'PYMODULE'),
  ('pandas.core.dtypes.inference',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\dtypes\\inference.py',
   'PYMODULE'),
  ('pandas.core.dtypes.missing',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\dtypes\\missing.py',
   'PYMODULE'),
  ('pandas.core.flags',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\flags.py',
   'PYMODULE'),
  ('pandas.core.frame',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\frame.py',
   'PYMODULE'),
  ('pandas.core.generic',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\generic.py',
   'PYMODULE'),
  ('pandas.core.groupby',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\groupby\\__init__.py',
   'PYMODULE'),
  ('pandas.core.groupby.base',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\groupby\\base.py',
   'PYMODULE'),
  ('pandas.core.groupby.categorical',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\groupby\\categorical.py',
   'PYMODULE'),
  ('pandas.core.groupby.generic',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\groupby\\generic.py',
   'PYMODULE'),
  ('pandas.core.groupby.groupby',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\groupby\\groupby.py',
   'PYMODULE'),
  ('pandas.core.groupby.grouper',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\groupby\\grouper.py',
   'PYMODULE'),
  ('pandas.core.groupby.indexing',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\groupby\\indexing.py',
   'PYMODULE'),
  ('pandas.core.groupby.numba_',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\groupby\\numba_.py',
   'PYMODULE'),
  ('pandas.core.groupby.ops',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\groupby\\ops.py',
   'PYMODULE'),
  ('pandas.core.indexers',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\indexers\\__init__.py',
   'PYMODULE'),
  ('pandas.core.indexers.objects',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\indexers\\objects.py',
   'PYMODULE'),
  ('pandas.core.indexers.utils',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\indexers\\utils.py',
   'PYMODULE'),
  ('pandas.core.indexes',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\indexes\\__init__.py',
   'PYMODULE'),
  ('pandas.core.indexes.accessors',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\indexes\\accessors.py',
   'PYMODULE'),
  ('pandas.core.indexes.api',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\indexes\\api.py',
   'PYMODULE'),
  ('pandas.core.indexes.base',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\indexes\\base.py',
   'PYMODULE'),
  ('pandas.core.indexes.category',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\indexes\\category.py',
   'PYMODULE'),
  ('pandas.core.indexes.datetimelike',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\indexes\\datetimelike.py',
   'PYMODULE'),
  ('pandas.core.indexes.datetimes',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\indexes\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.indexes.extension',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\indexes\\extension.py',
   'PYMODULE'),
  ('pandas.core.indexes.frozen',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\indexes\\frozen.py',
   'PYMODULE'),
  ('pandas.core.indexes.interval',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\indexes\\interval.py',
   'PYMODULE'),
  ('pandas.core.indexes.multi',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\indexes\\multi.py',
   'PYMODULE'),
  ('pandas.core.indexes.period',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\indexes\\period.py',
   'PYMODULE'),
  ('pandas.core.indexes.range',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\indexes\\range.py',
   'PYMODULE'),
  ('pandas.core.indexes.timedeltas',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\indexes\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.indexing',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\indexing.py',
   'PYMODULE'),
  ('pandas.core.interchange',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\interchange\\__init__.py',
   'PYMODULE'),
  ('pandas.core.interchange.buffer',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\interchange\\buffer.py',
   'PYMODULE'),
  ('pandas.core.interchange.column',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\interchange\\column.py',
   'PYMODULE'),
  ('pandas.core.interchange.dataframe',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\interchange\\dataframe.py',
   'PYMODULE'),
  ('pandas.core.interchange.dataframe_protocol',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\interchange\\dataframe_protocol.py',
   'PYMODULE'),
  ('pandas.core.interchange.from_dataframe',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\interchange\\from_dataframe.py',
   'PYMODULE'),
  ('pandas.core.interchange.utils',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\interchange\\utils.py',
   'PYMODULE'),
  ('pandas.core.internals',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\internals\\__init__.py',
   'PYMODULE'),
  ('pandas.core.internals.api',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\internals\\api.py',
   'PYMODULE'),
  ('pandas.core.internals.array_manager',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\internals\\array_manager.py',
   'PYMODULE'),
  ('pandas.core.internals.base',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\internals\\base.py',
   'PYMODULE'),
  ('pandas.core.internals.blocks',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\internals\\blocks.py',
   'PYMODULE'),
  ('pandas.core.internals.concat',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\internals\\concat.py',
   'PYMODULE'),
  ('pandas.core.internals.construction',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\internals\\construction.py',
   'PYMODULE'),
  ('pandas.core.internals.managers',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\internals\\managers.py',
   'PYMODULE'),
  ('pandas.core.internals.ops',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\internals\\ops.py',
   'PYMODULE'),
  ('pandas.core.methods',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\methods\\__init__.py',
   'PYMODULE'),
  ('pandas.core.methods.describe',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\methods\\describe.py',
   'PYMODULE'),
  ('pandas.core.methods.selectn',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\methods\\selectn.py',
   'PYMODULE'),
  ('pandas.core.methods.to_dict',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\methods\\to_dict.py',
   'PYMODULE'),
  ('pandas.core.missing',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\missing.py',
   'PYMODULE'),
  ('pandas.core.nanops',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\nanops.py',
   'PYMODULE'),
  ('pandas.core.ops',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\ops\\__init__.py',
   'PYMODULE'),
  ('pandas.core.ops.array_ops',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\ops\\array_ops.py',
   'PYMODULE'),
  ('pandas.core.ops.common',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\ops\\common.py',
   'PYMODULE'),
  ('pandas.core.ops.dispatch',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\ops\\dispatch.py',
   'PYMODULE'),
  ('pandas.core.ops.docstrings',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\ops\\docstrings.py',
   'PYMODULE'),
  ('pandas.core.ops.invalid',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\ops\\invalid.py',
   'PYMODULE'),
  ('pandas.core.ops.mask_ops',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\ops\\mask_ops.py',
   'PYMODULE'),
  ('pandas.core.ops.missing',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\ops\\missing.py',
   'PYMODULE'),
  ('pandas.core.resample',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\resample.py',
   'PYMODULE'),
  ('pandas.core.reshape',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\reshape\\__init__.py',
   'PYMODULE'),
  ('pandas.core.reshape.api',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\reshape\\api.py',
   'PYMODULE'),
  ('pandas.core.reshape.concat',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\reshape\\concat.py',
   'PYMODULE'),
  ('pandas.core.reshape.encoding',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\reshape\\encoding.py',
   'PYMODULE'),
  ('pandas.core.reshape.melt',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\reshape\\melt.py',
   'PYMODULE'),
  ('pandas.core.reshape.merge',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\reshape\\merge.py',
   'PYMODULE'),
  ('pandas.core.reshape.pivot',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\reshape\\pivot.py',
   'PYMODULE'),
  ('pandas.core.reshape.reshape',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\reshape\\reshape.py',
   'PYMODULE'),
  ('pandas.core.reshape.tile',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\reshape\\tile.py',
   'PYMODULE'),
  ('pandas.core.reshape.util',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\reshape\\util.py',
   'PYMODULE'),
  ('pandas.core.roperator',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\roperator.py',
   'PYMODULE'),
  ('pandas.core.sample',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\sample.py',
   'PYMODULE'),
  ('pandas.core.series',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\series.py',
   'PYMODULE'),
  ('pandas.core.shared_docs',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\shared_docs.py',
   'PYMODULE'),
  ('pandas.core.sorting',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\sorting.py',
   'PYMODULE'),
  ('pandas.core.strings',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\strings\\__init__.py',
   'PYMODULE'),
  ('pandas.core.strings.accessor',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\strings\\accessor.py',
   'PYMODULE'),
  ('pandas.core.strings.base',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\strings\\base.py',
   'PYMODULE'),
  ('pandas.core.strings.object_array',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\strings\\object_array.py',
   'PYMODULE'),
  ('pandas.core.tools',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\tools\\__init__.py',
   'PYMODULE'),
  ('pandas.core.tools.datetimes',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\tools\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.tools.numeric',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\tools\\numeric.py',
   'PYMODULE'),
  ('pandas.core.tools.timedeltas',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\tools\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.tools.times',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\tools\\times.py',
   'PYMODULE'),
  ('pandas.core.util',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\util\\__init__.py',
   'PYMODULE'),
  ('pandas.core.util.hashing',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\util\\hashing.py',
   'PYMODULE'),
  ('pandas.core.util.numba_',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\util\\numba_.py',
   'PYMODULE'),
  ('pandas.core.window',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\window\\__init__.py',
   'PYMODULE'),
  ('pandas.core.window.common',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\window\\common.py',
   'PYMODULE'),
  ('pandas.core.window.doc',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\window\\doc.py',
   'PYMODULE'),
  ('pandas.core.window.ewm',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\window\\ewm.py',
   'PYMODULE'),
  ('pandas.core.window.expanding',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\window\\expanding.py',
   'PYMODULE'),
  ('pandas.core.window.numba_',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\window\\numba_.py',
   'PYMODULE'),
  ('pandas.core.window.online',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\window\\online.py',
   'PYMODULE'),
  ('pandas.core.window.rolling',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\core\\window\\rolling.py',
   'PYMODULE'),
  ('pandas.errors',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\errors\\__init__.py',
   'PYMODULE'),
  ('pandas.io',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\io\\__init__.py',
   'PYMODULE'),
  ('pandas.io._util',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\io\\_util.py',
   'PYMODULE'),
  ('pandas.io.api',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\io\\api.py',
   'PYMODULE'),
  ('pandas.io.clipboard',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\io\\clipboard\\__init__.py',
   'PYMODULE'),
  ('pandas.io.clipboards',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\io\\clipboards.py',
   'PYMODULE'),
  ('pandas.io.common',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\io\\common.py',
   'PYMODULE'),
  ('pandas.io.excel',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\io\\excel\\__init__.py',
   'PYMODULE'),
  ('pandas.io.excel._base',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\io\\excel\\_base.py',
   'PYMODULE'),
  ('pandas.io.excel._calamine',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\io\\excel\\_calamine.py',
   'PYMODULE'),
  ('pandas.io.excel._odfreader',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\io\\excel\\_odfreader.py',
   'PYMODULE'),
  ('pandas.io.excel._odswriter',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\io\\excel\\_odswriter.py',
   'PYMODULE'),
  ('pandas.io.excel._openpyxl',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\io\\excel\\_openpyxl.py',
   'PYMODULE'),
  ('pandas.io.excel._pyxlsb',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\io\\excel\\_pyxlsb.py',
   'PYMODULE'),
  ('pandas.io.excel._util',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\io\\excel\\_util.py',
   'PYMODULE'),
  ('pandas.io.excel._xlrd',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\io\\excel\\_xlrd.py',
   'PYMODULE'),
  ('pandas.io.excel._xlsxwriter',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\io\\excel\\_xlsxwriter.py',
   'PYMODULE'),
  ('pandas.io.feather_format',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\io\\feather_format.py',
   'PYMODULE'),
  ('pandas.io.formats',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\io\\formats\\__init__.py',
   'PYMODULE'),
  ('pandas.io.formats._color_data',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\io\\formats\\_color_data.py',
   'PYMODULE'),
  ('pandas.io.formats.console',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\io\\formats\\console.py',
   'PYMODULE'),
  ('pandas.io.formats.css',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\io\\formats\\css.py',
   'PYMODULE'),
  ('pandas.io.formats.csvs',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\io\\formats\\csvs.py',
   'PYMODULE'),
  ('pandas.io.formats.excel',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\io\\formats\\excel.py',
   'PYMODULE'),
  ('pandas.io.formats.format',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\io\\formats\\format.py',
   'PYMODULE'),
  ('pandas.io.formats.html',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\io\\formats\\html.py',
   'PYMODULE'),
  ('pandas.io.formats.info',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\io\\formats\\info.py',
   'PYMODULE'),
  ('pandas.io.formats.printing',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\io\\formats\\printing.py',
   'PYMODULE'),
  ('pandas.io.formats.string',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\io\\formats\\string.py',
   'PYMODULE'),
  ('pandas.io.formats.style',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\io\\formats\\style.py',
   'PYMODULE'),
  ('pandas.io.formats.style_render',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\io\\formats\\style_render.py',
   'PYMODULE'),
  ('pandas.io.formats.xml',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\io\\formats\\xml.py',
   'PYMODULE'),
  ('pandas.io.gbq',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\io\\gbq.py',
   'PYMODULE'),
  ('pandas.io.html',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\io\\html.py',
   'PYMODULE'),
  ('pandas.io.json',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\io\\json\\__init__.py',
   'PYMODULE'),
  ('pandas.io.json._json',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\io\\json\\_json.py',
   'PYMODULE'),
  ('pandas.io.json._normalize',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\io\\json\\_normalize.py',
   'PYMODULE'),
  ('pandas.io.json._table_schema',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\io\\json\\_table_schema.py',
   'PYMODULE'),
  ('pandas.io.orc',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\io\\orc.py',
   'PYMODULE'),
  ('pandas.io.parquet',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\io\\parquet.py',
   'PYMODULE'),
  ('pandas.io.parsers',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\io\\parsers\\__init__.py',
   'PYMODULE'),
  ('pandas.io.parsers.arrow_parser_wrapper',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\io\\parsers\\arrow_parser_wrapper.py',
   'PYMODULE'),
  ('pandas.io.parsers.base_parser',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\io\\parsers\\base_parser.py',
   'PYMODULE'),
  ('pandas.io.parsers.c_parser_wrapper',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\io\\parsers\\c_parser_wrapper.py',
   'PYMODULE'),
  ('pandas.io.parsers.python_parser',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\io\\parsers\\python_parser.py',
   'PYMODULE'),
  ('pandas.io.parsers.readers',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\io\\parsers\\readers.py',
   'PYMODULE'),
  ('pandas.io.pickle',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\io\\pickle.py',
   'PYMODULE'),
  ('pandas.io.pytables',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\io\\pytables.py',
   'PYMODULE'),
  ('pandas.io.sas',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\io\\sas\\__init__.py',
   'PYMODULE'),
  ('pandas.io.sas.sas7bdat',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\io\\sas\\sas7bdat.py',
   'PYMODULE'),
  ('pandas.io.sas.sas_constants',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\io\\sas\\sas_constants.py',
   'PYMODULE'),
  ('pandas.io.sas.sas_xport',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\io\\sas\\sas_xport.py',
   'PYMODULE'),
  ('pandas.io.sas.sasreader',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\io\\sas\\sasreader.py',
   'PYMODULE'),
  ('pandas.io.spss',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\io\\spss.py',
   'PYMODULE'),
  ('pandas.io.sql',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\io\\sql.py',
   'PYMODULE'),
  ('pandas.io.stata',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\io\\stata.py',
   'PYMODULE'),
  ('pandas.io.xml',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\io\\xml.py',
   'PYMODULE'),
  ('pandas.plotting',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\plotting\\__init__.py',
   'PYMODULE'),
  ('pandas.plotting._core',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\plotting\\_core.py',
   'PYMODULE'),
  ('pandas.plotting._misc',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\plotting\\_misc.py',
   'PYMODULE'),
  ('pandas.testing',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\testing.py',
   'PYMODULE'),
  ('pandas.tseries',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\tseries\\__init__.py',
   'PYMODULE'),
  ('pandas.tseries.api',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\tseries\\api.py',
   'PYMODULE'),
  ('pandas.tseries.frequencies',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\tseries\\frequencies.py',
   'PYMODULE'),
  ('pandas.tseries.holiday',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\tseries\\holiday.py',
   'PYMODULE'),
  ('pandas.tseries.offsets',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\tseries\\offsets.py',
   'PYMODULE'),
  ('pandas.util',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\util\\__init__.py',
   'PYMODULE'),
  ('pandas.util._decorators',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\util\\_decorators.py',
   'PYMODULE'),
  ('pandas.util._exceptions',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\util\\_exceptions.py',
   'PYMODULE'),
  ('pandas.util._print_versions',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\util\\_print_versions.py',
   'PYMODULE'),
  ('pandas.util._tester',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\util\\_tester.py',
   'PYMODULE'),
  ('pandas.util._validators',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\util\\_validators.py',
   'PYMODULE'),
  ('pandas.util.version',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pandas\\util\\version\\__init__.py',
   'PYMODULE'),
  ('parso',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\parso\\__init__.py',
   'PYMODULE'),
  ('parso._compatibility',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\parso\\_compatibility.py',
   'PYMODULE'),
  ('parso.cache',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\parso\\cache.py',
   'PYMODULE'),
  ('parso.file_io',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\parso\\file_io.py',
   'PYMODULE'),
  ('parso.grammar',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\parso\\grammar.py',
   'PYMODULE'),
  ('parso.normalizer',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\parso\\normalizer.py',
   'PYMODULE'),
  ('parso.parser',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\parso\\parser.py',
   'PYMODULE'),
  ('parso.pgen2',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\parso\\pgen2\\__init__.py',
   'PYMODULE'),
  ('parso.pgen2.generator',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\parso\\pgen2\\generator.py',
   'PYMODULE'),
  ('parso.pgen2.grammar_parser',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\parso\\pgen2\\grammar_parser.py',
   'PYMODULE'),
  ('parso.python',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\parso\\python\\__init__.py',
   'PYMODULE'),
  ('parso.python.diff',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\parso\\python\\diff.py',
   'PYMODULE'),
  ('parso.python.errors',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\parso\\python\\errors.py',
   'PYMODULE'),
  ('parso.python.parser',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\parso\\python\\parser.py',
   'PYMODULE'),
  ('parso.python.pep8',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\parso\\python\\pep8.py',
   'PYMODULE'),
  ('parso.python.prefix',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\parso\\python\\prefix.py',
   'PYMODULE'),
  ('parso.python.token',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\parso\\python\\token.py',
   'PYMODULE'),
  ('parso.python.tokenize',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\parso\\python\\tokenize.py',
   'PYMODULE'),
  ('parso.python.tree',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\parso\\python\\tree.py',
   'PYMODULE'),
  ('parso.tree',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\parso\\tree.py',
   'PYMODULE'),
  ('parso.utils',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\parso\\utils.py',
   'PYMODULE'),
  ('pathlib',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\pathlib.py',
   'PYMODULE'),
  ('pdb',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\pdb.py',
   'PYMODULE'),
  ('pickle',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\pickle.py',
   'PYMODULE'),
  ('pickleshare',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pickleshare.py',
   'PYMODULE'),
  ('pickletools',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\pickletools.py',
   'PYMODULE'),
  ('pkg_resources',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pkg_resources\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pkg_resources\\_vendor\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.appdirs',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pkg_resources\\_vendor\\appdirs.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._adapters',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_adapters.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._common',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_common.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._compat',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_compat.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._itertools',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_itertools.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._legacy',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_legacy.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources.abc',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\abc.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources.readers',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\readers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources.simple',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\simple.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco.context',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco.functools',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\functools.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco.text',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.more_itertools',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pkg_resources\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.more_itertools.more',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pkg_resources\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('pkg_resources._vendor.more_itertools.recipes',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pkg_resources\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.__about__',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\__about__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._manylinux',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._musllinux',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._structures',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.markers',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.requirements',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.specifiers',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.tags',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.utils',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.version',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.actions',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\actions.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.common',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\common.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.core',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\core.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.diagram',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\diagram\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.exceptions',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\exceptions.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.helpers',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\helpers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.results',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\results.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.testing',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\testing.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.unicode',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\unicode.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.util',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\util.py',
   'PYMODULE'),
  ('pkg_resources._vendor.zipp',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pkg_resources\\_vendor\\zipp.py',
   'PYMODULE'),
  ('pkg_resources.extern',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pkg_resources\\extern\\__init__.py',
   'PYMODULE'),
  ('pkgutil',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\pkgutil.py',
   'PYMODULE'),
  ('platform',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\platform.py',
   'PYMODULE'),
  ('plistlib',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\plistlib.py',
   'PYMODULE'),
  ('pprint',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\pprint.py',
   'PYMODULE'),
  ('profile',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\profile.py',
   'PYMODULE'),
  ('prompt_toolkit',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.application',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\application\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.application.application',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\application\\application.py',
   'PYMODULE'),
  ('prompt_toolkit.application.current',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\application\\current.py',
   'PYMODULE'),
  ('prompt_toolkit.application.dummy',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\application\\dummy.py',
   'PYMODULE'),
  ('prompt_toolkit.application.run_in_terminal',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\application\\run_in_terminal.py',
   'PYMODULE'),
  ('prompt_toolkit.auto_suggest',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\auto_suggest.py',
   'PYMODULE'),
  ('prompt_toolkit.buffer',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\buffer.py',
   'PYMODULE'),
  ('prompt_toolkit.cache',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\cache.py',
   'PYMODULE'),
  ('prompt_toolkit.clipboard',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\clipboard\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.clipboard.base',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\clipboard\\base.py',
   'PYMODULE'),
  ('prompt_toolkit.clipboard.in_memory',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\clipboard\\in_memory.py',
   'PYMODULE'),
  ('prompt_toolkit.completion',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\completion\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.completion.base',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\completion\\base.py',
   'PYMODULE'),
  ('prompt_toolkit.completion.deduplicate',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\completion\\deduplicate.py',
   'PYMODULE'),
  ('prompt_toolkit.completion.filesystem',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\completion\\filesystem.py',
   'PYMODULE'),
  ('prompt_toolkit.completion.fuzzy_completer',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\completion\\fuzzy_completer.py',
   'PYMODULE'),
  ('prompt_toolkit.completion.nested',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\completion\\nested.py',
   'PYMODULE'),
  ('prompt_toolkit.completion.word_completer',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\completion\\word_completer.py',
   'PYMODULE'),
  ('prompt_toolkit.cursor_shapes',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\cursor_shapes.py',
   'PYMODULE'),
  ('prompt_toolkit.data_structures',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\data_structures.py',
   'PYMODULE'),
  ('prompt_toolkit.document',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\document.py',
   'PYMODULE'),
  ('prompt_toolkit.enums',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\enums.py',
   'PYMODULE'),
  ('prompt_toolkit.eventloop',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\eventloop\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.eventloop.async_generator',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\eventloop\\async_generator.py',
   'PYMODULE'),
  ('prompt_toolkit.eventloop.inputhook',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\eventloop\\inputhook.py',
   'PYMODULE'),
  ('prompt_toolkit.eventloop.utils',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\eventloop\\utils.py',
   'PYMODULE'),
  ('prompt_toolkit.eventloop.win32',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\eventloop\\win32.py',
   'PYMODULE'),
  ('prompt_toolkit.filters',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\filters\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.filters.app',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\filters\\app.py',
   'PYMODULE'),
  ('prompt_toolkit.filters.base',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\filters\\base.py',
   'PYMODULE'),
  ('prompt_toolkit.filters.cli',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\filters\\cli.py',
   'PYMODULE'),
  ('prompt_toolkit.filters.utils',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\filters\\utils.py',
   'PYMODULE'),
  ('prompt_toolkit.formatted_text',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\formatted_text\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.formatted_text.ansi',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\formatted_text\\ansi.py',
   'PYMODULE'),
  ('prompt_toolkit.formatted_text.base',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\formatted_text\\base.py',
   'PYMODULE'),
  ('prompt_toolkit.formatted_text.html',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\formatted_text\\html.py',
   'PYMODULE'),
  ('prompt_toolkit.formatted_text.pygments',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\formatted_text\\pygments.py',
   'PYMODULE'),
  ('prompt_toolkit.formatted_text.utils',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\formatted_text\\utils.py',
   'PYMODULE'),
  ('prompt_toolkit.history',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\history.py',
   'PYMODULE'),
  ('prompt_toolkit.input',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\input\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.input.ansi_escape_sequences',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\input\\ansi_escape_sequences.py',
   'PYMODULE'),
  ('prompt_toolkit.input.base',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\input\\base.py',
   'PYMODULE'),
  ('prompt_toolkit.input.defaults',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\input\\defaults.py',
   'PYMODULE'),
  ('prompt_toolkit.input.posix_pipe',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\input\\posix_pipe.py',
   'PYMODULE'),
  ('prompt_toolkit.input.posix_utils',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\input\\posix_utils.py',
   'PYMODULE'),
  ('prompt_toolkit.input.typeahead',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\input\\typeahead.py',
   'PYMODULE'),
  ('prompt_toolkit.input.vt100',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\input\\vt100.py',
   'PYMODULE'),
  ('prompt_toolkit.input.vt100_parser',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\input\\vt100_parser.py',
   'PYMODULE'),
  ('prompt_toolkit.input.win32',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\input\\win32.py',
   'PYMODULE'),
  ('prompt_toolkit.input.win32_pipe',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\input\\win32_pipe.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\key_binding\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\key_binding\\bindings\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.auto_suggest',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\key_binding\\bindings\\auto_suggest.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.basic',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\key_binding\\bindings\\basic.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.completion',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\key_binding\\bindings\\completion.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.cpr',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\key_binding\\bindings\\cpr.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.emacs',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\key_binding\\bindings\\emacs.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.focus',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\key_binding\\bindings\\focus.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.mouse',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\key_binding\\bindings\\mouse.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.named_commands',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\key_binding\\bindings\\named_commands.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.open_in_editor',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\key_binding\\bindings\\open_in_editor.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.page_navigation',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\key_binding\\bindings\\page_navigation.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.scroll',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\key_binding\\bindings\\scroll.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.search',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\key_binding\\bindings\\search.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.vi',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\key_binding\\bindings\\vi.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.defaults',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\key_binding\\defaults.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.digraphs',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\key_binding\\digraphs.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.emacs_state',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\key_binding\\emacs_state.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.key_bindings',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\key_binding\\key_bindings.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.key_processor',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\key_binding\\key_processor.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.vi_state',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\key_binding\\vi_state.py',
   'PYMODULE'),
  ('prompt_toolkit.keys',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\keys.py',
   'PYMODULE'),
  ('prompt_toolkit.layout',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\layout\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.layout.containers',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\layout\\containers.py',
   'PYMODULE'),
  ('prompt_toolkit.layout.controls',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\layout\\controls.py',
   'PYMODULE'),
  ('prompt_toolkit.layout.dimension',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\layout\\dimension.py',
   'PYMODULE'),
  ('prompt_toolkit.layout.dummy',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\layout\\dummy.py',
   'PYMODULE'),
  ('prompt_toolkit.layout.layout',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\layout\\layout.py',
   'PYMODULE'),
  ('prompt_toolkit.layout.margins',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\layout\\margins.py',
   'PYMODULE'),
  ('prompt_toolkit.layout.menus',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\layout\\menus.py',
   'PYMODULE'),
  ('prompt_toolkit.layout.mouse_handlers',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\layout\\mouse_handlers.py',
   'PYMODULE'),
  ('prompt_toolkit.layout.processors',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\layout\\processors.py',
   'PYMODULE'),
  ('prompt_toolkit.layout.screen',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\layout\\screen.py',
   'PYMODULE'),
  ('prompt_toolkit.layout.scrollable_pane',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\layout\\scrollable_pane.py',
   'PYMODULE'),
  ('prompt_toolkit.layout.utils',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\layout\\utils.py',
   'PYMODULE'),
  ('prompt_toolkit.lexers',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\lexers\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.lexers.base',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\lexers\\base.py',
   'PYMODULE'),
  ('prompt_toolkit.lexers.pygments',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\lexers\\pygments.py',
   'PYMODULE'),
  ('prompt_toolkit.mouse_events',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\mouse_events.py',
   'PYMODULE'),
  ('prompt_toolkit.output',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\output\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.output.base',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\output\\base.py',
   'PYMODULE'),
  ('prompt_toolkit.output.color_depth',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\output\\color_depth.py',
   'PYMODULE'),
  ('prompt_toolkit.output.conemu',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\output\\conemu.py',
   'PYMODULE'),
  ('prompt_toolkit.output.defaults',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\output\\defaults.py',
   'PYMODULE'),
  ('prompt_toolkit.output.flush_stdout',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\output\\flush_stdout.py',
   'PYMODULE'),
  ('prompt_toolkit.output.plain_text',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\output\\plain_text.py',
   'PYMODULE'),
  ('prompt_toolkit.output.vt100',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\output\\vt100.py',
   'PYMODULE'),
  ('prompt_toolkit.output.win32',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\output\\win32.py',
   'PYMODULE'),
  ('prompt_toolkit.output.windows10',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\output\\windows10.py',
   'PYMODULE'),
  ('prompt_toolkit.patch_stdout',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\patch_stdout.py',
   'PYMODULE'),
  ('prompt_toolkit.renderer',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\renderer.py',
   'PYMODULE'),
  ('prompt_toolkit.search',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\search.py',
   'PYMODULE'),
  ('prompt_toolkit.selection',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\selection.py',
   'PYMODULE'),
  ('prompt_toolkit.shortcuts',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\shortcuts\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.shortcuts.dialogs',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\shortcuts\\dialogs.py',
   'PYMODULE'),
  ('prompt_toolkit.shortcuts.progress_bar',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\shortcuts\\progress_bar\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.shortcuts.progress_bar.base',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\shortcuts\\progress_bar\\base.py',
   'PYMODULE'),
  ('prompt_toolkit.shortcuts.progress_bar.formatters',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\shortcuts\\progress_bar\\formatters.py',
   'PYMODULE'),
  ('prompt_toolkit.shortcuts.prompt',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\shortcuts\\prompt.py',
   'PYMODULE'),
  ('prompt_toolkit.shortcuts.utils',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\shortcuts\\utils.py',
   'PYMODULE'),
  ('prompt_toolkit.styles',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\styles\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.styles.base',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\styles\\base.py',
   'PYMODULE'),
  ('prompt_toolkit.styles.defaults',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\styles\\defaults.py',
   'PYMODULE'),
  ('prompt_toolkit.styles.named_colors',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\styles\\named_colors.py',
   'PYMODULE'),
  ('prompt_toolkit.styles.pygments',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\styles\\pygments.py',
   'PYMODULE'),
  ('prompt_toolkit.styles.style',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\styles\\style.py',
   'PYMODULE'),
  ('prompt_toolkit.styles.style_transformation',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\styles\\style_transformation.py',
   'PYMODULE'),
  ('prompt_toolkit.utils',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\utils.py',
   'PYMODULE'),
  ('prompt_toolkit.validation',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\validation.py',
   'PYMODULE'),
  ('prompt_toolkit.widgets',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\widgets\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.widgets.base',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\widgets\\base.py',
   'PYMODULE'),
  ('prompt_toolkit.widgets.dialogs',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\widgets\\dialogs.py',
   'PYMODULE'),
  ('prompt_toolkit.widgets.menus',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\widgets\\menus.py',
   'PYMODULE'),
  ('prompt_toolkit.widgets.toolbars',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\widgets\\toolbars.py',
   'PYMODULE'),
  ('prompt_toolkit.win32_types',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\prompt_toolkit\\win32_types.py',
   'PYMODULE'),
  ('pstats',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\pstats.py',
   'PYMODULE'),
  ('pure_eval',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pure_eval\\__init__.py',
   'PYMODULE'),
  ('pure_eval.core',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pure_eval\\core.py',
   'PYMODULE'),
  ('pure_eval.my_getattr_static',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pure_eval\\my_getattr_static.py',
   'PYMODULE'),
  ('pure_eval.utils',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pure_eval\\utils.py',
   'PYMODULE'),
  ('pure_eval.version',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pure_eval\\version.py',
   'PYMODULE'),
  ('py_compile',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\py_compile.py',
   'PYMODULE'),
  ('pydoc',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\pydoc.py',
   'PYMODULE'),
  ('pydoc_data',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pygments',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\__init__.py',
   'PYMODULE'),
  ('pygments.console',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\console.py',
   'PYMODULE'),
  ('pygments.filter',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\filter.py',
   'PYMODULE'),
  ('pygments.filters',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\filters\\__init__.py',
   'PYMODULE'),
  ('pygments.formatter',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\formatter.py',
   'PYMODULE'),
  ('pygments.formatters',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\formatters\\__init__.py',
   'PYMODULE'),
  ('pygments.formatters._mapping',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\formatters\\_mapping.py',
   'PYMODULE'),
  ('pygments.formatters.bbcode',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\formatters\\bbcode.py',
   'PYMODULE'),
  ('pygments.formatters.groff',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\formatters\\groff.py',
   'PYMODULE'),
  ('pygments.formatters.html',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\formatters\\html.py',
   'PYMODULE'),
  ('pygments.formatters.img',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\formatters\\img.py',
   'PYMODULE'),
  ('pygments.formatters.irc',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\formatters\\irc.py',
   'PYMODULE'),
  ('pygments.formatters.latex',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\formatters\\latex.py',
   'PYMODULE'),
  ('pygments.formatters.other',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\formatters\\other.py',
   'PYMODULE'),
  ('pygments.formatters.pangomarkup',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\formatters\\pangomarkup.py',
   'PYMODULE'),
  ('pygments.formatters.rtf',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\formatters\\rtf.py',
   'PYMODULE'),
  ('pygments.formatters.svg',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\formatters\\svg.py',
   'PYMODULE'),
  ('pygments.formatters.terminal',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\formatters\\terminal.py',
   'PYMODULE'),
  ('pygments.formatters.terminal256',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\formatters\\terminal256.py',
   'PYMODULE'),
  ('pygments.lexer',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexer.py',
   'PYMODULE'),
  ('pygments.lexers',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\__init__.py',
   'PYMODULE'),
  ('pygments.lexers._ada_builtins',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\_ada_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._asy_builtins',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\_asy_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._cl_builtins',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\_cl_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._cocoa_builtins',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\_cocoa_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._csound_builtins',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\_csound_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._css_builtins',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\_css_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._googlesql_builtins',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\_googlesql_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._julia_builtins',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\_julia_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._lasso_builtins',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\_lasso_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._lilypond_builtins',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\_lilypond_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._lua_builtins',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\_lua_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._luau_builtins',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\_luau_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._mapping',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\_mapping.py',
   'PYMODULE'),
  ('pygments.lexers._mql_builtins',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\_mql_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._mysql_builtins',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\_mysql_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._openedge_builtins',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\_openedge_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._php_builtins',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\_php_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._postgres_builtins',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\_postgres_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._qlik_builtins',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\_qlik_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._scheme_builtins',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\_scheme_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._scilab_builtins',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\_scilab_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._sourcemod_builtins',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\_sourcemod_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._sql_builtins',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\_sql_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._stan_builtins',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\_stan_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._stata_builtins',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\_stata_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._tsql_builtins',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\_tsql_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._usd_builtins',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\_usd_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._vbscript_builtins',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\_vbscript_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._vim_builtins',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\_vim_builtins.py',
   'PYMODULE'),
  ('pygments.lexers.actionscript',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\actionscript.py',
   'PYMODULE'),
  ('pygments.lexers.ada',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\ada.py',
   'PYMODULE'),
  ('pygments.lexers.agile',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\agile.py',
   'PYMODULE'),
  ('pygments.lexers.algebra',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\algebra.py',
   'PYMODULE'),
  ('pygments.lexers.ambient',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\ambient.py',
   'PYMODULE'),
  ('pygments.lexers.amdgpu',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\amdgpu.py',
   'PYMODULE'),
  ('pygments.lexers.ampl',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\ampl.py',
   'PYMODULE'),
  ('pygments.lexers.apdlexer',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\apdlexer.py',
   'PYMODULE'),
  ('pygments.lexers.apl',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\apl.py',
   'PYMODULE'),
  ('pygments.lexers.archetype',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\archetype.py',
   'PYMODULE'),
  ('pygments.lexers.arrow',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\arrow.py',
   'PYMODULE'),
  ('pygments.lexers.arturo',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\arturo.py',
   'PYMODULE'),
  ('pygments.lexers.asc',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\asc.py',
   'PYMODULE'),
  ('pygments.lexers.asm',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\asm.py',
   'PYMODULE'),
  ('pygments.lexers.asn1',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\asn1.py',
   'PYMODULE'),
  ('pygments.lexers.automation',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\automation.py',
   'PYMODULE'),
  ('pygments.lexers.bare',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\bare.py',
   'PYMODULE'),
  ('pygments.lexers.basic',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\basic.py',
   'PYMODULE'),
  ('pygments.lexers.bdd',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\bdd.py',
   'PYMODULE'),
  ('pygments.lexers.berry',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\berry.py',
   'PYMODULE'),
  ('pygments.lexers.bibtex',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\bibtex.py',
   'PYMODULE'),
  ('pygments.lexers.blueprint',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\blueprint.py',
   'PYMODULE'),
  ('pygments.lexers.boa',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\boa.py',
   'PYMODULE'),
  ('pygments.lexers.bqn',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\bqn.py',
   'PYMODULE'),
  ('pygments.lexers.business',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\business.py',
   'PYMODULE'),
  ('pygments.lexers.c_cpp',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\c_cpp.py',
   'PYMODULE'),
  ('pygments.lexers.c_like',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\c_like.py',
   'PYMODULE'),
  ('pygments.lexers.capnproto',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\capnproto.py',
   'PYMODULE'),
  ('pygments.lexers.carbon',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\carbon.py',
   'PYMODULE'),
  ('pygments.lexers.cddl',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\cddl.py',
   'PYMODULE'),
  ('pygments.lexers.chapel',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\chapel.py',
   'PYMODULE'),
  ('pygments.lexers.clean',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\clean.py',
   'PYMODULE'),
  ('pygments.lexers.codeql',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\codeql.py',
   'PYMODULE'),
  ('pygments.lexers.comal',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\comal.py',
   'PYMODULE'),
  ('pygments.lexers.compiled',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\compiled.py',
   'PYMODULE'),
  ('pygments.lexers.configs',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\configs.py',
   'PYMODULE'),
  ('pygments.lexers.console',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\console.py',
   'PYMODULE'),
  ('pygments.lexers.cplint',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\cplint.py',
   'PYMODULE'),
  ('pygments.lexers.crystal',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\crystal.py',
   'PYMODULE'),
  ('pygments.lexers.csound',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\csound.py',
   'PYMODULE'),
  ('pygments.lexers.css',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\css.py',
   'PYMODULE'),
  ('pygments.lexers.d',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\d.py',
   'PYMODULE'),
  ('pygments.lexers.dalvik',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\dalvik.py',
   'PYMODULE'),
  ('pygments.lexers.data',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\data.py',
   'PYMODULE'),
  ('pygments.lexers.dax',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\dax.py',
   'PYMODULE'),
  ('pygments.lexers.devicetree',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\devicetree.py',
   'PYMODULE'),
  ('pygments.lexers.diff',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\diff.py',
   'PYMODULE'),
  ('pygments.lexers.dns',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\dns.py',
   'PYMODULE'),
  ('pygments.lexers.dotnet',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\dotnet.py',
   'PYMODULE'),
  ('pygments.lexers.dsls',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\dsls.py',
   'PYMODULE'),
  ('pygments.lexers.dylan',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\dylan.py',
   'PYMODULE'),
  ('pygments.lexers.ecl',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\ecl.py',
   'PYMODULE'),
  ('pygments.lexers.eiffel',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\eiffel.py',
   'PYMODULE'),
  ('pygments.lexers.elm',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\elm.py',
   'PYMODULE'),
  ('pygments.lexers.elpi',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\elpi.py',
   'PYMODULE'),
  ('pygments.lexers.email',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\email.py',
   'PYMODULE'),
  ('pygments.lexers.erlang',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\erlang.py',
   'PYMODULE'),
  ('pygments.lexers.esoteric',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\esoteric.py',
   'PYMODULE'),
  ('pygments.lexers.ezhil',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\ezhil.py',
   'PYMODULE'),
  ('pygments.lexers.factor',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\factor.py',
   'PYMODULE'),
  ('pygments.lexers.fantom',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\fantom.py',
   'PYMODULE'),
  ('pygments.lexers.felix',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\felix.py',
   'PYMODULE'),
  ('pygments.lexers.fift',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\fift.py',
   'PYMODULE'),
  ('pygments.lexers.floscript',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\floscript.py',
   'PYMODULE'),
  ('pygments.lexers.forth',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\forth.py',
   'PYMODULE'),
  ('pygments.lexers.fortran',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\fortran.py',
   'PYMODULE'),
  ('pygments.lexers.foxpro',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\foxpro.py',
   'PYMODULE'),
  ('pygments.lexers.freefem',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\freefem.py',
   'PYMODULE'),
  ('pygments.lexers.func',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\func.py',
   'PYMODULE'),
  ('pygments.lexers.functional',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\functional.py',
   'PYMODULE'),
  ('pygments.lexers.futhark',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\futhark.py',
   'PYMODULE'),
  ('pygments.lexers.gcodelexer',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\gcodelexer.py',
   'PYMODULE'),
  ('pygments.lexers.gdscript',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\gdscript.py',
   'PYMODULE'),
  ('pygments.lexers.gleam',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\gleam.py',
   'PYMODULE'),
  ('pygments.lexers.go',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\go.py',
   'PYMODULE'),
  ('pygments.lexers.grammar_notation',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\grammar_notation.py',
   'PYMODULE'),
  ('pygments.lexers.graph',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\graph.py',
   'PYMODULE'),
  ('pygments.lexers.graphics',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\graphics.py',
   'PYMODULE'),
  ('pygments.lexers.graphql',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\graphql.py',
   'PYMODULE'),
  ('pygments.lexers.graphviz',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\graphviz.py',
   'PYMODULE'),
  ('pygments.lexers.gsql',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\gsql.py',
   'PYMODULE'),
  ('pygments.lexers.hare',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\hare.py',
   'PYMODULE'),
  ('pygments.lexers.haskell',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\haskell.py',
   'PYMODULE'),
  ('pygments.lexers.haxe',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\haxe.py',
   'PYMODULE'),
  ('pygments.lexers.hdl',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\hdl.py',
   'PYMODULE'),
  ('pygments.lexers.hexdump',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\hexdump.py',
   'PYMODULE'),
  ('pygments.lexers.html',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\html.py',
   'PYMODULE'),
  ('pygments.lexers.idl',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\idl.py',
   'PYMODULE'),
  ('pygments.lexers.igor',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\igor.py',
   'PYMODULE'),
  ('pygments.lexers.inferno',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\inferno.py',
   'PYMODULE'),
  ('pygments.lexers.installers',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\installers.py',
   'PYMODULE'),
  ('pygments.lexers.int_fiction',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\int_fiction.py',
   'PYMODULE'),
  ('pygments.lexers.iolang',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\iolang.py',
   'PYMODULE'),
  ('pygments.lexers.j',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\j.py',
   'PYMODULE'),
  ('pygments.lexers.javascript',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\javascript.py',
   'PYMODULE'),
  ('pygments.lexers.jmespath',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\jmespath.py',
   'PYMODULE'),
  ('pygments.lexers.jslt',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\jslt.py',
   'PYMODULE'),
  ('pygments.lexers.json5',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\json5.py',
   'PYMODULE'),
  ('pygments.lexers.jsonnet',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\jsonnet.py',
   'PYMODULE'),
  ('pygments.lexers.jsx',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\jsx.py',
   'PYMODULE'),
  ('pygments.lexers.julia',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\julia.py',
   'PYMODULE'),
  ('pygments.lexers.jvm',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\jvm.py',
   'PYMODULE'),
  ('pygments.lexers.kuin',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\kuin.py',
   'PYMODULE'),
  ('pygments.lexers.kusto',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\kusto.py',
   'PYMODULE'),
  ('pygments.lexers.ldap',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\ldap.py',
   'PYMODULE'),
  ('pygments.lexers.lean',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\lean.py',
   'PYMODULE'),
  ('pygments.lexers.lilypond',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\lilypond.py',
   'PYMODULE'),
  ('pygments.lexers.lisp',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\lisp.py',
   'PYMODULE'),
  ('pygments.lexers.macaulay2',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\macaulay2.py',
   'PYMODULE'),
  ('pygments.lexers.make',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\make.py',
   'PYMODULE'),
  ('pygments.lexers.maple',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\maple.py',
   'PYMODULE'),
  ('pygments.lexers.markup',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\markup.py',
   'PYMODULE'),
  ('pygments.lexers.math',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\math.py',
   'PYMODULE'),
  ('pygments.lexers.matlab',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\matlab.py',
   'PYMODULE'),
  ('pygments.lexers.maxima',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\maxima.py',
   'PYMODULE'),
  ('pygments.lexers.meson',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\meson.py',
   'PYMODULE'),
  ('pygments.lexers.mime',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\mime.py',
   'PYMODULE'),
  ('pygments.lexers.minecraft',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\minecraft.py',
   'PYMODULE'),
  ('pygments.lexers.mips',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\mips.py',
   'PYMODULE'),
  ('pygments.lexers.ml',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\ml.py',
   'PYMODULE'),
  ('pygments.lexers.modeling',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\modeling.py',
   'PYMODULE'),
  ('pygments.lexers.modula2',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\modula2.py',
   'PYMODULE'),
  ('pygments.lexers.mojo',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\mojo.py',
   'PYMODULE'),
  ('pygments.lexers.monte',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\monte.py',
   'PYMODULE'),
  ('pygments.lexers.mosel',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\mosel.py',
   'PYMODULE'),
  ('pygments.lexers.ncl',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\ncl.py',
   'PYMODULE'),
  ('pygments.lexers.nimrod',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\nimrod.py',
   'PYMODULE'),
  ('pygments.lexers.nit',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\nit.py',
   'PYMODULE'),
  ('pygments.lexers.nix',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\nix.py',
   'PYMODULE'),
  ('pygments.lexers.numbair',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\numbair.py',
   'PYMODULE'),
  ('pygments.lexers.oberon',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\oberon.py',
   'PYMODULE'),
  ('pygments.lexers.objective',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\objective.py',
   'PYMODULE'),
  ('pygments.lexers.ooc',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\ooc.py',
   'PYMODULE'),
  ('pygments.lexers.openscad',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\openscad.py',
   'PYMODULE'),
  ('pygments.lexers.other',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\other.py',
   'PYMODULE'),
  ('pygments.lexers.parasail',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\parasail.py',
   'PYMODULE'),
  ('pygments.lexers.parsers',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\parsers.py',
   'PYMODULE'),
  ('pygments.lexers.pascal',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\pascal.py',
   'PYMODULE'),
  ('pygments.lexers.pawn',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\pawn.py',
   'PYMODULE'),
  ('pygments.lexers.pddl',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\pddl.py',
   'PYMODULE'),
  ('pygments.lexers.perl',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\perl.py',
   'PYMODULE'),
  ('pygments.lexers.phix',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\phix.py',
   'PYMODULE'),
  ('pygments.lexers.php',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\php.py',
   'PYMODULE'),
  ('pygments.lexers.pointless',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\pointless.py',
   'PYMODULE'),
  ('pygments.lexers.pony',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\pony.py',
   'PYMODULE'),
  ('pygments.lexers.praat',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\praat.py',
   'PYMODULE'),
  ('pygments.lexers.procfile',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\procfile.py',
   'PYMODULE'),
  ('pygments.lexers.prolog',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\prolog.py',
   'PYMODULE'),
  ('pygments.lexers.promql',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\promql.py',
   'PYMODULE'),
  ('pygments.lexers.prql',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\prql.py',
   'PYMODULE'),
  ('pygments.lexers.ptx',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\ptx.py',
   'PYMODULE'),
  ('pygments.lexers.python',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\python.py',
   'PYMODULE'),
  ('pygments.lexers.q',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\q.py',
   'PYMODULE'),
  ('pygments.lexers.qlik',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\qlik.py',
   'PYMODULE'),
  ('pygments.lexers.qvt',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\qvt.py',
   'PYMODULE'),
  ('pygments.lexers.r',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\r.py',
   'PYMODULE'),
  ('pygments.lexers.rdf',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\rdf.py',
   'PYMODULE'),
  ('pygments.lexers.rebol',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\rebol.py',
   'PYMODULE'),
  ('pygments.lexers.rego',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\rego.py',
   'PYMODULE'),
  ('pygments.lexers.resource',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\resource.py',
   'PYMODULE'),
  ('pygments.lexers.ride',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\ride.py',
   'PYMODULE'),
  ('pygments.lexers.rita',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\rita.py',
   'PYMODULE'),
  ('pygments.lexers.rnc',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\rnc.py',
   'PYMODULE'),
  ('pygments.lexers.roboconf',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\roboconf.py',
   'PYMODULE'),
  ('pygments.lexers.robotframework',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\robotframework.py',
   'PYMODULE'),
  ('pygments.lexers.ruby',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\ruby.py',
   'PYMODULE'),
  ('pygments.lexers.rust',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\rust.py',
   'PYMODULE'),
  ('pygments.lexers.sas',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\sas.py',
   'PYMODULE'),
  ('pygments.lexers.savi',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\savi.py',
   'PYMODULE'),
  ('pygments.lexers.scdoc',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\scdoc.py',
   'PYMODULE'),
  ('pygments.lexers.scripting',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\scripting.py',
   'PYMODULE'),
  ('pygments.lexers.sgf',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\sgf.py',
   'PYMODULE'),
  ('pygments.lexers.shell',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\shell.py',
   'PYMODULE'),
  ('pygments.lexers.sieve',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\sieve.py',
   'PYMODULE'),
  ('pygments.lexers.slash',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\slash.py',
   'PYMODULE'),
  ('pygments.lexers.smalltalk',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\smalltalk.py',
   'PYMODULE'),
  ('pygments.lexers.smithy',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\smithy.py',
   'PYMODULE'),
  ('pygments.lexers.smv',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\smv.py',
   'PYMODULE'),
  ('pygments.lexers.snobol',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\snobol.py',
   'PYMODULE'),
  ('pygments.lexers.solidity',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\solidity.py',
   'PYMODULE'),
  ('pygments.lexers.soong',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\soong.py',
   'PYMODULE'),
  ('pygments.lexers.sophia',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\sophia.py',
   'PYMODULE'),
  ('pygments.lexers.special',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\special.py',
   'PYMODULE'),
  ('pygments.lexers.spice',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\spice.py',
   'PYMODULE'),
  ('pygments.lexers.sql',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\sql.py',
   'PYMODULE'),
  ('pygments.lexers.srcinfo',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\srcinfo.py',
   'PYMODULE'),
  ('pygments.lexers.stata',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\stata.py',
   'PYMODULE'),
  ('pygments.lexers.supercollider',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\supercollider.py',
   'PYMODULE'),
  ('pygments.lexers.tablegen',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\tablegen.py',
   'PYMODULE'),
  ('pygments.lexers.tact',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\tact.py',
   'PYMODULE'),
  ('pygments.lexers.tal',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\tal.py',
   'PYMODULE'),
  ('pygments.lexers.tcl',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\tcl.py',
   'PYMODULE'),
  ('pygments.lexers.teal',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\teal.py',
   'PYMODULE'),
  ('pygments.lexers.templates',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\templates.py',
   'PYMODULE'),
  ('pygments.lexers.teraterm',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\teraterm.py',
   'PYMODULE'),
  ('pygments.lexers.testing',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\testing.py',
   'PYMODULE'),
  ('pygments.lexers.text',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\text.py',
   'PYMODULE'),
  ('pygments.lexers.textedit',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\textedit.py',
   'PYMODULE'),
  ('pygments.lexers.textfmts',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\textfmts.py',
   'PYMODULE'),
  ('pygments.lexers.theorem',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\theorem.py',
   'PYMODULE'),
  ('pygments.lexers.thingsdb',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\thingsdb.py',
   'PYMODULE'),
  ('pygments.lexers.tlb',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\tlb.py',
   'PYMODULE'),
  ('pygments.lexers.tls',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\tls.py',
   'PYMODULE'),
  ('pygments.lexers.tnt',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\tnt.py',
   'PYMODULE'),
  ('pygments.lexers.trafficscript',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\trafficscript.py',
   'PYMODULE'),
  ('pygments.lexers.typoscript',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\typoscript.py',
   'PYMODULE'),
  ('pygments.lexers.typst',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\typst.py',
   'PYMODULE'),
  ('pygments.lexers.ul4',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\ul4.py',
   'PYMODULE'),
  ('pygments.lexers.unicon',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\unicon.py',
   'PYMODULE'),
  ('pygments.lexers.urbi',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\urbi.py',
   'PYMODULE'),
  ('pygments.lexers.usd',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\usd.py',
   'PYMODULE'),
  ('pygments.lexers.varnish',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\varnish.py',
   'PYMODULE'),
  ('pygments.lexers.verification',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\verification.py',
   'PYMODULE'),
  ('pygments.lexers.verifpal',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\verifpal.py',
   'PYMODULE'),
  ('pygments.lexers.vip',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\vip.py',
   'PYMODULE'),
  ('pygments.lexers.vyper',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\vyper.py',
   'PYMODULE'),
  ('pygments.lexers.web',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\web.py',
   'PYMODULE'),
  ('pygments.lexers.webassembly',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\webassembly.py',
   'PYMODULE'),
  ('pygments.lexers.webidl',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\webidl.py',
   'PYMODULE'),
  ('pygments.lexers.webmisc',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\webmisc.py',
   'PYMODULE'),
  ('pygments.lexers.wgsl',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\wgsl.py',
   'PYMODULE'),
  ('pygments.lexers.whiley',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\whiley.py',
   'PYMODULE'),
  ('pygments.lexers.wowtoc',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\wowtoc.py',
   'PYMODULE'),
  ('pygments.lexers.wren',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\wren.py',
   'PYMODULE'),
  ('pygments.lexers.x10',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\x10.py',
   'PYMODULE'),
  ('pygments.lexers.xorg',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\xorg.py',
   'PYMODULE'),
  ('pygments.lexers.yang',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\yang.py',
   'PYMODULE'),
  ('pygments.lexers.yara',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\yara.py',
   'PYMODULE'),
  ('pygments.lexers.zig',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\lexers\\zig.py',
   'PYMODULE'),
  ('pygments.modeline',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\modeline.py',
   'PYMODULE'),
  ('pygments.plugin',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\plugin.py',
   'PYMODULE'),
  ('pygments.regexopt',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\regexopt.py',
   'PYMODULE'),
  ('pygments.scanner',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\scanner.py',
   'PYMODULE'),
  ('pygments.style',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\style.py',
   'PYMODULE'),
  ('pygments.styles',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\styles\\__init__.py',
   'PYMODULE'),
  ('pygments.styles._mapping',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\styles\\_mapping.py',
   'PYMODULE'),
  ('pygments.styles.abap',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\styles\\abap.py',
   'PYMODULE'),
  ('pygments.styles.algol',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\styles\\algol.py',
   'PYMODULE'),
  ('pygments.styles.algol_nu',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\styles\\algol_nu.py',
   'PYMODULE'),
  ('pygments.styles.arduino',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\styles\\arduino.py',
   'PYMODULE'),
  ('pygments.styles.autumn',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\styles\\autumn.py',
   'PYMODULE'),
  ('pygments.styles.borland',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\styles\\borland.py',
   'PYMODULE'),
  ('pygments.styles.bw',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\styles\\bw.py',
   'PYMODULE'),
  ('pygments.styles.coffee',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\styles\\coffee.py',
   'PYMODULE'),
  ('pygments.styles.colorful',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\styles\\colorful.py',
   'PYMODULE'),
  ('pygments.styles.default',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\styles\\default.py',
   'PYMODULE'),
  ('pygments.styles.dracula',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\styles\\dracula.py',
   'PYMODULE'),
  ('pygments.styles.emacs',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\styles\\emacs.py',
   'PYMODULE'),
  ('pygments.styles.friendly',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\styles\\friendly.py',
   'PYMODULE'),
  ('pygments.styles.friendly_grayscale',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\styles\\friendly_grayscale.py',
   'PYMODULE'),
  ('pygments.styles.fruity',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\styles\\fruity.py',
   'PYMODULE'),
  ('pygments.styles.gh_dark',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\styles\\gh_dark.py',
   'PYMODULE'),
  ('pygments.styles.gruvbox',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\styles\\gruvbox.py',
   'PYMODULE'),
  ('pygments.styles.igor',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\styles\\igor.py',
   'PYMODULE'),
  ('pygments.styles.inkpot',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\styles\\inkpot.py',
   'PYMODULE'),
  ('pygments.styles.lightbulb',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\styles\\lightbulb.py',
   'PYMODULE'),
  ('pygments.styles.lilypond',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\styles\\lilypond.py',
   'PYMODULE'),
  ('pygments.styles.lovelace',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\styles\\lovelace.py',
   'PYMODULE'),
  ('pygments.styles.manni',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\styles\\manni.py',
   'PYMODULE'),
  ('pygments.styles.material',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\styles\\material.py',
   'PYMODULE'),
  ('pygments.styles.monokai',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\styles\\monokai.py',
   'PYMODULE'),
  ('pygments.styles.murphy',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\styles\\murphy.py',
   'PYMODULE'),
  ('pygments.styles.native',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\styles\\native.py',
   'PYMODULE'),
  ('pygments.styles.nord',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\styles\\nord.py',
   'PYMODULE'),
  ('pygments.styles.onedark',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\styles\\onedark.py',
   'PYMODULE'),
  ('pygments.styles.paraiso_dark',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\styles\\paraiso_dark.py',
   'PYMODULE'),
  ('pygments.styles.paraiso_light',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\styles\\paraiso_light.py',
   'PYMODULE'),
  ('pygments.styles.pastie',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\styles\\pastie.py',
   'PYMODULE'),
  ('pygments.styles.perldoc',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\styles\\perldoc.py',
   'PYMODULE'),
  ('pygments.styles.rainbow_dash',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\styles\\rainbow_dash.py',
   'PYMODULE'),
  ('pygments.styles.rrt',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\styles\\rrt.py',
   'PYMODULE'),
  ('pygments.styles.sas',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\styles\\sas.py',
   'PYMODULE'),
  ('pygments.styles.solarized',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\styles\\solarized.py',
   'PYMODULE'),
  ('pygments.styles.staroffice',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\styles\\staroffice.py',
   'PYMODULE'),
  ('pygments.styles.stata_dark',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\styles\\stata_dark.py',
   'PYMODULE'),
  ('pygments.styles.stata_light',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\styles\\stata_light.py',
   'PYMODULE'),
  ('pygments.styles.tango',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\styles\\tango.py',
   'PYMODULE'),
  ('pygments.styles.trac',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\styles\\trac.py',
   'PYMODULE'),
  ('pygments.styles.vim',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\styles\\vim.py',
   'PYMODULE'),
  ('pygments.styles.vs',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\styles\\vs.py',
   'PYMODULE'),
  ('pygments.styles.xcode',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\styles\\xcode.py',
   'PYMODULE'),
  ('pygments.styles.zenburn',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\styles\\zenburn.py',
   'PYMODULE'),
  ('pygments.token',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\token.py',
   'PYMODULE'),
  ('pygments.unistring',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\unistring.py',
   'PYMODULE'),
  ('pygments.util',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pygments\\util.py',
   'PYMODULE'),
  ('pythoncom',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pythoncom.py',
   'PYMODULE'),
  ('pytz',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pytz\\__init__.py',
   'PYMODULE'),
  ('pytz.exceptions',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pytz\\exceptions.py',
   'PYMODULE'),
  ('pytz.lazy',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pytz\\lazy.py',
   'PYMODULE'),
  ('pytz.tzfile',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pytz\\tzfile.py',
   'PYMODULE'),
  ('pytz.tzinfo',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\pytz\\tzinfo.py',
   'PYMODULE'),
  ('pywin',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\Pythonwin\\pywin\\__init__.py',
   'PYMODULE'),
  ('pywin.dialogs',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\Pythonwin\\pywin\\dialogs\\__init__.py',
   'PYMODULE'),
  ('pywin.dialogs.list',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\Pythonwin\\pywin\\dialogs\\list.py',
   'PYMODULE'),
  ('pywin.dialogs.status',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\Pythonwin\\pywin\\dialogs\\status.py',
   'PYMODULE'),
  ('pywin.mfc',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\Pythonwin\\pywin\\mfc\\__init__.py',
   'PYMODULE'),
  ('pywin.mfc.dialog',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\Pythonwin\\pywin\\mfc\\dialog.py',
   'PYMODULE'),
  ('pywin.mfc.object',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\Pythonwin\\pywin\\mfc\\object.py',
   'PYMODULE'),
  ('pywin.mfc.thread',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\Pythonwin\\pywin\\mfc\\thread.py',
   'PYMODULE'),
  ('pywin.mfc.window',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\Pythonwin\\pywin\\mfc\\window.py',
   'PYMODULE'),
  ('pywin32_system32', '-', 'PYMODULE'),
  ('pywintypes',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\win32\\lib\\pywintypes.py',
   'PYMODULE'),
  ('queue',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\queue.py',
   'PYMODULE'),
  ('quopri',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\quopri.py',
   'PYMODULE'),
  ('random',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\random.py',
   'PYMODULE'),
  ('referencing',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\referencing\\__init__.py',
   'PYMODULE'),
  ('referencing._attrs',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\referencing\\_attrs.py',
   'PYMODULE'),
  ('referencing._core',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\referencing\\_core.py',
   'PYMODULE'),
  ('referencing.exceptions',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\referencing\\exceptions.py',
   'PYMODULE'),
  ('referencing.jsonschema',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\referencing\\jsonschema.py',
   'PYMODULE'),
  ('referencing.typing',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\referencing\\typing.py',
   'PYMODULE'),
  ('requests',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\requests\\__init__.py',
   'PYMODULE'),
  ('requests.__version__',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\requests\\__version__.py',
   'PYMODULE'),
  ('requests._internal_utils',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\requests\\_internal_utils.py',
   'PYMODULE'),
  ('requests.adapters',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\requests\\adapters.py',
   'PYMODULE'),
  ('requests.api',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\requests\\api.py',
   'PYMODULE'),
  ('requests.auth',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\requests\\auth.py',
   'PYMODULE'),
  ('requests.certs',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\requests\\certs.py',
   'PYMODULE'),
  ('requests.compat',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\requests\\compat.py',
   'PYMODULE'),
  ('requests.cookies',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\requests\\cookies.py',
   'PYMODULE'),
  ('requests.exceptions',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\requests\\exceptions.py',
   'PYMODULE'),
  ('requests.hooks',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\requests\\hooks.py',
   'PYMODULE'),
  ('requests.models',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\requests\\models.py',
   'PYMODULE'),
  ('requests.packages',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\requests\\packages.py',
   'PYMODULE'),
  ('requests.sessions',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\requests\\sessions.py',
   'PYMODULE'),
  ('requests.status_codes',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\requests\\status_codes.py',
   'PYMODULE'),
  ('requests.structures',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\requests\\structures.py',
   'PYMODULE'),
  ('requests.utils',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\requests\\utils.py',
   'PYMODULE'),
  ('rlcompleter',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\rlcompleter.py',
   'PYMODULE'),
  ('rpds',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\rpds\\__init__.py',
   'PYMODULE'),
  ('runpy',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\runpy.py',
   'PYMODULE'),
  ('secrets',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\secrets.py',
   'PYMODULE'),
  ('selectors',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\selectors.py',
   'PYMODULE'),
  ('shlex',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\shlex.py',
   'PYMODULE'),
  ('shutil',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\shutil.py',
   'PYMODULE'),
  ('signal',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\signal.py',
   'PYMODULE'),
  ('site',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site.py',
   'PYMODULE'),
  ('six',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\six.py',
   'PYMODULE'),
  ('smtplib',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\smtplib.py',
   'PYMODULE'),
  ('socket',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\socket.py',
   'PYMODULE'),
  ('socketserver',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\socketserver.py',
   'PYMODULE'),
  ('soupsieve',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\soupsieve\\__init__.py',
   'PYMODULE'),
  ('soupsieve.__meta__',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\soupsieve\\__meta__.py',
   'PYMODULE'),
  ('soupsieve.css_match',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\soupsieve\\css_match.py',
   'PYMODULE'),
  ('soupsieve.css_parser',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\soupsieve\\css_parser.py',
   'PYMODULE'),
  ('soupsieve.css_types',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\soupsieve\\css_types.py',
   'PYMODULE'),
  ('soupsieve.pretty',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\soupsieve\\pretty.py',
   'PYMODULE'),
  ('soupsieve.util',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\soupsieve\\util.py',
   'PYMODULE'),
  ('sqlite3',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\sqlite3\\__init__.py',
   'PYMODULE'),
  ('sqlite3.dbapi2',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\sqlite3\\dbapi2.py',
   'PYMODULE'),
  ('sqlite3.dump',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\sqlite3\\dump.py',
   'PYMODULE'),
  ('ssl',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\ssl.py',
   'PYMODULE'),
  ('stack_data',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\stack_data\\__init__.py',
   'PYMODULE'),
  ('stack_data.core',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\stack_data\\core.py',
   'PYMODULE'),
  ('stack_data.formatting',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\stack_data\\formatting.py',
   'PYMODULE'),
  ('stack_data.serializing',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\stack_data\\serializing.py',
   'PYMODULE'),
  ('stack_data.utils',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\stack_data\\utils.py',
   'PYMODULE'),
  ('stack_data.version',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\stack_data\\version.py',
   'PYMODULE'),
  ('statistics',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\statistics.py',
   'PYMODULE'),
  ('string',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\string.py',
   'PYMODULE'),
  ('stringprep',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\stringprep.py',
   'PYMODULE'),
  ('subprocess',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\subprocess.py',
   'PYMODULE'),
  ('sysconfig',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\sysconfig.py',
   'PYMODULE'),
  ('tarfile',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\tarfile.py',
   'PYMODULE'),
  ('tempfile',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\tempfile.py',
   'PYMODULE'),
  ('textwrap',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\textwrap.py',
   'PYMODULE'),
  ('threading',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\threading.py',
   'PYMODULE'),
  ('timeit',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\timeit.py',
   'PYMODULE'),
  ('token',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\token.py',
   'PYMODULE'),
  ('tokenize',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\tokenize.py',
   'PYMODULE'),
  ('tracemalloc',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\tracemalloc.py',
   'PYMODULE'),
  ('traitlets',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\traitlets\\__init__.py',
   'PYMODULE'),
  ('traitlets._version',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\traitlets\\_version.py',
   'PYMODULE'),
  ('traitlets.config',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\traitlets\\config\\__init__.py',
   'PYMODULE'),
  ('traitlets.config.application',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\traitlets\\config\\application.py',
   'PYMODULE'),
  ('traitlets.config.argcomplete_config',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\traitlets\\config\\argcomplete_config.py',
   'PYMODULE'),
  ('traitlets.config.configurable',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\traitlets\\config\\configurable.py',
   'PYMODULE'),
  ('traitlets.config.loader',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\traitlets\\config\\loader.py',
   'PYMODULE'),
  ('traitlets.log',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\traitlets\\log.py',
   'PYMODULE'),
  ('traitlets.traitlets',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\traitlets\\traitlets.py',
   'PYMODULE'),
  ('traitlets.utils',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\traitlets\\utils\\__init__.py',
   'PYMODULE'),
  ('traitlets.utils.bunch',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\traitlets\\utils\\bunch.py',
   'PYMODULE'),
  ('traitlets.utils.decorators',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\traitlets\\utils\\decorators.py',
   'PYMODULE'),
  ('traitlets.utils.descriptions',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\traitlets\\utils\\descriptions.py',
   'PYMODULE'),
  ('traitlets.utils.getargspec',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\traitlets\\utils\\getargspec.py',
   'PYMODULE'),
  ('traitlets.utils.importstring',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\traitlets\\utils\\importstring.py',
   'PYMODULE'),
  ('traitlets.utils.nested_update',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\traitlets\\utils\\nested_update.py',
   'PYMODULE'),
  ('traitlets.utils.sentinel',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\traitlets\\utils\\sentinel.py',
   'PYMODULE'),
  ('traitlets.utils.text',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\traitlets\\utils\\text.py',
   'PYMODULE'),
  ('traitlets.utils.warnings',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\traitlets\\utils\\warnings.py',
   'PYMODULE'),
  ('tty',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\tty.py',
   'PYMODULE'),
  ('typing',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\typing.py',
   'PYMODULE'),
  ('typing_extensions',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('unittest',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\unittest\\__init__.py',
   'PYMODULE'),
  ('unittest._log',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\unittest\\_log.py',
   'PYMODULE'),
  ('unittest.async_case',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.case',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\unittest\\case.py',
   'PYMODULE'),
  ('unittest.loader',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\unittest\\loader.py',
   'PYMODULE'),
  ('unittest.main',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\unittest\\main.py',
   'PYMODULE'),
  ('unittest.result',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\unittest\\result.py',
   'PYMODULE'),
  ('unittest.runner',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.signals',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.suite',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\unittest\\suite.py',
   'PYMODULE'),
  ('unittest.util',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\unittest\\util.py',
   'PYMODULE'),
  ('urllib',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('urllib.error',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\urllib\\error.py',
   'PYMODULE'),
  ('urllib.parse',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\urllib\\parse.py',
   'PYMODULE'),
  ('urllib.request',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\urllib\\request.py',
   'PYMODULE'),
  ('urllib.response',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\urllib\\response.py',
   'PYMODULE'),
  ('urllib3',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\urllib3\\__init__.py',
   'PYMODULE'),
  ('urllib3._base_connection',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\urllib3\\_base_connection.py',
   'PYMODULE'),
  ('urllib3._collections',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\urllib3\\_collections.py',
   'PYMODULE'),
  ('urllib3._request_methods',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\urllib3\\_request_methods.py',
   'PYMODULE'),
  ('urllib3._version',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\urllib3\\_version.py',
   'PYMODULE'),
  ('urllib3.connection',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\urllib3\\connection.py',
   'PYMODULE'),
  ('urllib3.connectionpool',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE'),
  ('urllib3.contrib',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.connection',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\connection.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.fetch',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\fetch.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.request',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\request.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.response',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\response.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE'),
  ('urllib3.contrib.socks',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\urllib3\\contrib\\socks.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE'),
  ('urllib3.fields',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\urllib3\\fields.py',
   'PYMODULE'),
  ('urllib3.filepost',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\urllib3\\filepost.py',
   'PYMODULE'),
  ('urllib3.http2',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\urllib3\\http2\\__init__.py',
   'PYMODULE'),
  ('urllib3.http2.connection',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\urllib3\\http2\\connection.py',
   'PYMODULE'),
  ('urllib3.http2.probe',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\urllib3\\http2\\probe.py',
   'PYMODULE'),
  ('urllib3.poolmanager',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE'),
  ('urllib3.response',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\urllib3\\response.py',
   'PYMODULE'),
  ('urllib3.util',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE'),
  ('urllib3.util.proxy',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\urllib3\\util\\proxy.py',
   'PYMODULE'),
  ('urllib3.util.request',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE'),
  ('urllib3.util.response',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE'),
  ('urllib3.util.retry',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE'),
  ('urllib3.util.ssl_match_hostname',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\urllib3\\util\\ssl_match_hostname.py',
   'PYMODULE'),
  ('urllib3.util.ssltransport',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\urllib3\\util\\ssltransport.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE'),
  ('urllib3.util.url',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE'),
  ('urllib3.util.util',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\urllib3\\util\\util.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE'),
  ('uuid',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\uuid.py',
   'PYMODULE'),
  ('wave',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\wave.py',
   'PYMODULE'),
  ('wcwidth',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\wcwidth\\__init__.py',
   'PYMODULE'),
  ('wcwidth.table_vs16',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\wcwidth\\table_vs16.py',
   'PYMODULE'),
  ('wcwidth.table_wide',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\wcwidth\\table_wide.py',
   'PYMODULE'),
  ('wcwidth.table_zero',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\wcwidth\\table_zero.py',
   'PYMODULE'),
  ('wcwidth.unicode_versions',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\wcwidth\\unicode_versions.py',
   'PYMODULE'),
  ('wcwidth.wcwidth',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\wcwidth\\wcwidth.py',
   'PYMODULE'),
  ('webbrowser',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\webbrowser.py',
   'PYMODULE'),
  ('win32com',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\win32com\\__init__.py',
   'PYMODULE'),
  ('win32com.client',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\win32com\\client\\__init__.py',
   'PYMODULE'),
  ('win32com.client.CLSIDToClass',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\win32com\\client\\CLSIDToClass.py',
   'PYMODULE'),
  ('win32com.client.build',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\win32com\\client\\build.py',
   'PYMODULE'),
  ('win32com.client.dynamic',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\win32com\\client\\dynamic.py',
   'PYMODULE'),
  ('win32com.client.gencache',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\win32com\\client\\gencache.py',
   'PYMODULE'),
  ('win32com.client.genpy',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\win32com\\client\\genpy.py',
   'PYMODULE'),
  ('win32com.client.makepy',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\win32com\\client\\makepy.py',
   'PYMODULE'),
  ('win32com.client.selecttlb',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\win32com\\client\\selecttlb.py',
   'PYMODULE'),
  ('win32com.client.util',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\win32com\\client\\util.py',
   'PYMODULE'),
  ('win32com.server',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\win32com\\server\\__init__.py',
   'PYMODULE'),
  ('win32com.server.dispatcher',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\win32com\\server\\dispatcher.py',
   'PYMODULE'),
  ('win32com.server.exception',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\win32com\\server\\exception.py',
   'PYMODULE'),
  ('win32com.server.policy',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\win32com\\server\\policy.py',
   'PYMODULE'),
  ('win32com.server.util',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\win32com\\server\\util.py',
   'PYMODULE'),
  ('win32com.shell',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\win32comext\\shell\\__init__.py',
   'PYMODULE'),
  ('win32com.shell.shellcon',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\win32comext\\shell\\shellcon.py',
   'PYMODULE'),
  ('win32com.universal',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\win32com\\universal.py',
   'PYMODULE'),
  ('win32com.util',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\win32com\\util.py',
   'PYMODULE'),
  ('win32con',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\win32\\lib\\win32con.py',
   'PYMODULE'),
  ('win32evtlogutil',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\win32\\lib\\win32evtlogutil.py',
   'PYMODULE'),
  ('win32traceutil',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\win32\\lib\\win32traceutil.py',
   'PYMODULE'),
  ('winerror',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\site-packages\\win32\\lib\\winerror.py',
   'PYMODULE'),
  ('xml',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\xml\\__init__.py',
   'PYMODULE'),
  ('xml.dom',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\xml\\dom\\__init__.py',
   'PYMODULE'),
  ('xml.dom.NodeFilter',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\xml\\dom\\NodeFilter.py',
   'PYMODULE'),
  ('xml.dom.domreg',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\xml\\dom\\domreg.py',
   'PYMODULE'),
  ('xml.dom.expatbuilder',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\xml\\dom\\expatbuilder.py',
   'PYMODULE'),
  ('xml.dom.minicompat',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\xml\\dom\\minicompat.py',
   'PYMODULE'),
  ('xml.dom.minidom',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\xml\\dom\\minidom.py',
   'PYMODULE'),
  ('xml.dom.pulldom',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\xml\\dom\\pulldom.py',
   'PYMODULE'),
  ('xml.dom.xmlbuilder',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\xml\\dom\\xmlbuilder.py',
   'PYMODULE'),
  ('xml.etree',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\xml\\etree\\__init__.py',
   'PYMODULE'),
  ('xml.etree.ElementInclude',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.parsers',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.sax',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('xmlrpc',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\xmlrpc\\__init__.py',
   'PYMODULE'),
  ('xmlrpc.client',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('zipfile',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\zipfile.py',
   'PYMODULE'),
  ('zipimport',
   'C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.5\\Lib\\zipimport.py',
   'PYMODULE')])
