import React, { useState } from 'react';
import { SwapOutlined } from '@ant-design/icons';
import CustomLine from './CustomLine';
import EnlargeableChartWrapper from './EnlargeableChartWrapper';

const CollectionRatioToggleChart = ({ 
  revenuePerRdPersonnelData, 
  cashCollectionRatioData, 
  height, 
  fontSize, 
  isFullscreen 
}) => {
  const [currentMode, setCurrentMode] = useState('salesCollection'); // 'salesCollection' 或 'cashCollection'

  const handleToggle = () => {
    setCurrentMode(currentMode === 'salesCollection' ? 'cashCollection' : 'salesCollection');
  };

  const getCurrentData = () => {
    return currentMode === 'salesCollection' ? revenuePerRdPersonnelData : cashCollectionRatioData;
  };

  const getCurrentTitle = () => {
    return currentMode === 'salesCollection' ? '销售回款率' : '营业收现率';
  };

  return (
    <div style={{ position: 'relative', height: '100%' }}>
      {/* 切换图标按钮 - 放在右上角，避免与放大按钮重叠 */}
      <button
        onClick={handleToggle}
        style={{
          position: 'absolute',
          top: '18px',
          right: '40px', // 向右偏移，避免与放大按钮重叠
          zIndex: 999,
          width: 28,
          height: 28,
          border: '1px solid #ccc',
          borderRadius: '50%',
          background: 'rgba(255,255,255,0.8)',
          cursor: 'pointer',
          transition: 'all 0.3s ease',
          boxShadow: '0 2px 8px rgba(0,0,0,0.2)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          fontSize: '16px',
          color: '#1783ff'
        }}
        onMouseOver={(e) => {
          e.target.style.background = 'rgba(255,255,255,0.9)';
          e.target.style.transform = 'scale(1.05)';
        }}
        onMouseOut={(e) => {
          e.target.style.background = 'rgba(255,255,255,0.8)';
          e.target.style.transform = 'scale(1)';
        }}
        title="切换销售回款率/营业收现率"
      >
        <SwapOutlined style={{ fontSize: 16, color: '#1783ff' }} />
      </button>
      
      <EnlargeableChartWrapper 
        title={getCurrentTitle()}
        style={{ height: '100%' }}
      >
        <CustomLine 
          data={getCurrentData()} 
          title={getCurrentTitle()} 
          height={height} 
          fontSize={fontSize} 
          isFullscreen={isFullscreen} 
        />
      </EnlargeableChartWrapper>
    </div>
  );
};

export default CollectionRatioToggleChart; 