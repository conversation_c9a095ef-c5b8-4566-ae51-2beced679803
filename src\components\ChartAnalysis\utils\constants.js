// 常量配置文件

export const DEFAULT_COMPANY_IDS = ['600850.SH', '002268.SZ', '600877.SH'];
export const COLOR_PALETTE = ['#1783FF', '#FF4D4F', '#FAAD14', '#722ED1', '#13C2C2', '#52C41A'];
export const START_YEAR = 2019;

// 默认值（如果无法获取L2Metrics数据时使用）
export const FALLBACK_DEFAULT_YEAR = 2024;
export const FALLBACK_DEFAULT_QUARTER = 'ANNUAL';

// 动态获取默认年份和季度
export const getDefaultYearAndQuarter = async () => {
  try {
    const response = await fetch('./api/report-times/latest-l2metrics');
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    const data = await response.json();
    if (data && data.ReportDate) {
      const reportDate = new Date(data.ReportDate);
      const year = reportDate.getFullYear();
      const month = reportDate.getMonth() + 1;
      
      // 根据月份确定季度
      let quarter = 'ANNUAL';
      if (month === 3) quarter = 'Q1';
      else if (month === 6) quarter = 'Q2';
      else if (month === 9) quarter = 'Q3';
      else if (month === 12) quarter = 'ANNUAL';
      
      return { year, quarter };
    }
  } catch (error) {
    console.error('获取默认年份和季度失败:', error);
  }
  
  // 返回默认值
  return { year: FALLBACK_DEFAULT_YEAR, quarter: FALLBACK_DEFAULT_QUARTER };
};

export const defaultQuarters = [
  { label: '一季度', value: 'Q1', month: '03', day: '31' },
  { label: '二季度', value: 'Q2', month: '06', day: '30' },
  { label: '三季度', value: 'Q3', month: '09', day: '30' },
  { label: '年度报告', value: 'ANNUAL', month: '12', day: '31' },
]; 