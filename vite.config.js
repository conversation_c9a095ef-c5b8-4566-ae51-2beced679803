import { defineConfig, loadEnv } from 'vite';
import react from '@vitejs/plugin-react';
import { resolve } from 'path';
import envCompatible from 'vite-plugin-env-compatible';

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  // Load env file based on `mode` in the current directory.
  // Set the third parameter to '' to load all env regardless of the `VITE_` prefix.
  const env = loadEnv(mode, process.cwd(), '');

  // Determine which env file to use
  const envFile = process.env.ENV_FILE || `.env.${mode}`;

  // 将 REACT_APP_ 前缀的环境变量添加到 import.meta.env 中
  const importMetaEnv = {};
  Object.keys(env).forEach(key => {
    if (key.startsWith('REACT_APP_')) {
      importMetaEnv[key] = env[key];
    }
  });

  console.log('Environment variables loaded:', Object.keys(env).join(', '));

  return {
    base: './',
    plugins: [
      react(),
      // Make process.env.REACT_APP_* variables available
      envCompatible({
        prefix: 'REACT_APP_'
      })
    ],
    resolve: {
      alias: {
        '@': resolve(__dirname, 'src')
      }
    },
    // Vite options tailored for this project
    build: {
      outDir: 'dist',
      emptyOutDir: true,
      sourcemap: true
    },
    server: {
      port: 8081,
      open: true,
      host: true,
      proxy: {
        '/api': {
          target: 'http://127.0.0.1:5000/',
          changeOrigin: true
        }
      }
    },
    // Define environment variables
    define: {
      // Make process.env available
      'process.env': env,
      // 同时支持 import.meta.env
      __REACT_APP_API_BASE_URL__: JSON.stringify(env.REACT_APP_API_BASE_URL),
      __REACT_APP_DEV_MODE__: JSON.stringify(env.REACT_APP_DEV_MODE),
      __REACT_APP_API_DEV_URL__: JSON.stringify(env.REACT_APP_API_DEV_URL)
    },
    // 自定义环境变量处理
    envPrefix: ['VITE_', 'REACT_APP_']
  };
});
