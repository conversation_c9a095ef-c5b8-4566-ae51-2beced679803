Windows任务计划使用说明
===============================

本脚本用于创建Windows任务计划，自动定时执行整合数据处理程序。

文件说明:
- scheduled_task.bat: 任务执行的批处理文件
- integrated_processor_task.xml: 任务计划配置文件
- 注册任务计划.bat: 注册Windows任务计划
- 删除任务计划.bat: 删除Windows任务计划
- 查看任务状态.bat: 查看任务执行状态
- task_log.txt: 任务执行日志（自动生成）

任务配置:
- 任务名称: 整合数据处理程序
- 执行频率: 每天上午9点开始，每小时执行一次
- 执行程序: integrated_processor.exe
- 工作目录: 当前目录

使用步骤:
1. 以管理员身份运行"注册任务计划.bat"
2. 系统会自动创建Windows任务计划
3. 任务会在每天上午9点开始，每小时自动执行一次
4. 执行日志会记录在task_log.txt文件中

管理任务:
- 查看任务状态: 运行"查看任务状态.bat"
- 删除任务: 运行"删除任务计划.bat"
- 手动管理: 打开"任务计划程序"（taskschd.msc）

注意事项:
1. 必须以管理员身份运行注册脚本
2. 确保Wind金融终端在任务执行时已启动
3. 确保数据库连接正常
4. 任务执行时间较长，请耐心等待
5. 可以通过修改XML文件调整执行频率

自定义配置:
如需修改执行频率，请编辑integrated_processor_task.xml文件：
- PT1H: 每小时执行一次
- PT30M: 每30分钟执行一次
- PT2H: 每2小时执行一次

技术支持:
如有问题，请查看task_log.txt日志文件或联系技术支持。
