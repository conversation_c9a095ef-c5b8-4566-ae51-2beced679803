import React, { useEffect, useRef } from 'react';
import { Chart } from '@antv/g2';

const HorizontalBar = ({ 
  data, 
  title ={
    title: '',
    titleFontSize: 24,
  },
  height = 320, 
  encode = {
    x: 'company',
    y: 'value',
    color: 'type',
    maxSize: 40,
  
  },
  style,
  isFullscreen = false,
  isIndividualFullscreen = false
}) => {
  const container = useRef();
  
  // 监听单个全屏状态变化
  useEffect(() => {
    const handleFullscreenChange = () => {
      // setIsIndividualFullscreen(!!document.fullscreenElement); // 移除 useState 和 setIsIndividualFullscreen
    };
    document.addEventListener('fullscreenchange', handleFullscreenChange);
    document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
    document.addEventListener('mozfullscreenchange', handleFullscreenChange);
    document.addEventListener('MSFullscreenChange', handleFullscreenChange);
    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
      document.removeEventListener('webkitfullscreenchange', handleFullscreenChange);
      document.removeEventListener('mozfullscreenchange', handleFullscreenChange);
      document.removeEventListener('MSFullscreenChange', handleFullscreenChange);
    };
  }, []);

  // 解析style.height，优先使用style.height，否则用props.height
  let computedHeight = height;
  if (isIndividualFullscreen) {
    // 单个全屏时，使用视口高度的90%
    computedHeight = window.innerHeight * 0.9;
  } else if (isFullscreen) {
    // 整体全屏时，直接使用传入的 height（已经通过 scale 计算过）
    computedHeight = height;
  } else if (style && style.height) {
    if (typeof style.height === 'number') {
      computedHeight = style.height;
    } else if (typeof style.height === 'string' && style.height.endsWith('px')) {
      computedHeight = parseInt(style.height);
    } else if (typeof style.height === 'string' && style.height === '100%') {
      // 全屏时使用视口高度
      computedHeight = window.innerHeight * 0.9;
    } else if (typeof style.height === 'string') {
      computedHeight = parseInt(style.height);
    }
  }
  useEffect(() => {
    if (!container.current) return;
    container.current.innerHTML = '';

    if (!Array.isArray(data) || data.length === 0) return;

    const chart = new Chart({
      container: container.current,
      autoFit: true,
      height: computedHeight,
    });

    chart.options({
      type: 'interval',
      data: data, 
      coordinate: { transform: [{ type: 'transpose' }] },
      encode: encode,
      title:{
        title: title,
        titleFontSize: 24,
      },
      transform: [{ type: 'stackY' }],
      axis: {
        x: { title: false,labelFill:'#000000',labelFillOpacity:2 },
        y: false
      },
      legend: {
        color: { 
          position:'top',
          maxRows:1,
        }
      },
      labels:[ {     style: { fontSize: 14 },
        text: (d) => {
          const total = d.displayValue ?? d.value;
          if (total === 0) return '';
          return  (total);
        },
      }],
      style:{ 
        'minWidth': 20, 'maxWidth': 50 
      },
      interaction: {
        tooltip: {
          css: {

            '.g2-tooltip-list-item': {
              'font-size': '18px',
            },

          },

        }
      },
      tooltip: {
        items: [
          (datum) => ({
            name: '年份',
            value: datum.year,
          }), (datum) => ({
            name: datum.type,
            value: datum.displayValue ?? datum.value ?? '',
          }),
        ]
      },
   
    });
    chart
      .labelTransform({ type: 'overlapHide' })
      .labelTransform({ type: 'contrastReverse' });
    chart.render();

    // 添加 resize 监听器，当容器尺寸变化时重新渲染
    const resizeObserver = new ResizeObserver(() => {
      if (chart && !chart.destroyed) {
        chart.render();
      }
    });
    
    if (container.current) {
      resizeObserver.observe(container.current);
    }

    return () => {
      resizeObserver.disconnect();
      chart.destroy();
    };
  }, [data, computedHeight, title, encode, isIndividualFullscreen]);

  return (
    <div style={{ width: '100%', height: '100%', minHeight: computedHeight }}> 
      <div ref={container} style={{ width: '100%', height: '100%' }} />
    </div>
  );
};

export default HorizontalBar; 