import os
import time
import requests
from copy import deepcopy
from database.db_handler import DatabaseHandler
from logging_config import setup_logging

logger = setup_logging()

URL_SSE = "http://www.sse.com.cn/disclosure/listedinfo/announcement/"
# 股票
URL_SSE_STOCK = "http://www.sse.com.cn/js/common/ssesuggestdata.js"
# 查询
URL_QUERY_COMPANY = "http://query.sse.com.cn/security/stock/queryCompanyBulletin.do"

URL_PDF = "http://static.sse.com.cn"

# 报告类型
REPORT_TYPE = {
    '全部': ('ALL', ''),
    '定期公告': ('ALL', 'DQBG'),
    '年报': ('YEARLY', 'DQBG'),
    '第一季度季报': ('QUATER1', 'DQBG'),
    '半年报': ('QUATER2', 'DQBG'),
    '第三季度季报': ('QUATER3', 'DQBG'),
    '临时公告': ('ALL', 'LSGG'),
    '上市公司章程': ('SHGSZC', 'LSGG'),
    '发行上市公告': ('FXSSGG', 'LSGG'),
    '公司治理': ('GSZL', 'LSGG'),
    '股东大会会议': ('GDDH', 'LSGG'),
    'IPO公司公告': ('IPOGG', 'LSGG'),
    '其他': ('QT', 'LSGG'),
}

# 证券类型
SECURITY_TYPE = {
    '全部': '0101,120100,020100,020200,120200',
    '主板': '0101',
    '科创板': '120100,020100,020200,120200',
}

HEADER = {
    'Referer': URL_SSE,
    'User-Agent': "Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/72.0.3626.121 Safari/537.36",
}

URL_PARAM = {
    # 是否分页
    'isPagination': 'false',
    'productId': '600000',
    # 关键字
    'keyWord': '',
    'securityType': SECURITY_TYPE['主板'],
    'reportType2': 'DQBG',
    'reportType': 'YEARLY',
    'beginDate': '2022-01-01',
    'endDate': '2025-04-12',
}

def get_all_codes(url=None):
    """从数据库获取股票代码列表"""
    code, name, pinyin = [], [], []
    try:
        with DatabaseHandler() as db:
            cursor = db.connection.cursor()
            cursor.execute("""
                SELECT TickerSymbol StockCode,ShortName CompanyName 
                FROM CompanyInfo 
                WHERE IsActive=1
            """)
            for row in cursor.fetchall():
                # 转换688375.SH -> 688375
                if "." in row.StockCode:
                    code.append(row.StockCode[:-3])
                else:
                    code.append(row.StockCode)
                name.append(row.CompanyName)
                pinyin.append('')  # 保持接口兼容性
        logger.info(f"从数据库获取{len(code)}条股票代码")
    except Exception as e:
        logger.error(f"获取股票代码失败: {str(e)}")
    return code, name, pinyin


def get_pdf_url(code, begin_date, end_date, security_type='全部', report_type='年报'):
    url_param = deepcopy(URL_PARAM)
    url_param['productId'] = code
    url_param['securityType'] = SECURITY_TYPE[security_type]
    url_param['reportType2'] = REPORT_TYPE[report_type][1]
    url_param['reportType'] = REPORT_TYPE[report_type][0]
    url_param['beginDate'] = begin_date
    url_param['endDate'] = end_date
    result = requests.get(URL_QUERY_COMPANY, params=url_param, headers=HEADER).json()['result']
    return_list = []
    for i in result:
        if "摘要" in i["TITLE"]:
            pass
        else:
            return_list.append((URL_PDF + i['URL'], i['BULLETIN_TYPE'], i['BULLETIN_YEAR'], i['SSEDATE']))
    return return_list

def save_pdf(code, pdf_title_urls, path='./SH/'):
    """保存PDF文件，支持文件存在性检查"""
    file_path = os.path.join(path, code)
    if not os.path.isdir(file_path):
        os.makedirs(file_path)
    
    for url, r_type, year, date in pdf_title_urls:
        date = ''.join(date.split('-'))
        file_name = '_'.join([code, r_type, year, date]) + '.pdf'
        file_full_name = os.path.join(file_path, file_name)
        
        # 检查文件是否已存在
        if os.path.exists(file_full_name):
            file_size = os.path.getsize(file_full_name)
            if file_size > 1024:  # 文件大于1KB认为是有效文件
                logger.info(f'[{code}] 文件已存在，跳过: {file_name}')
                continue
            else:
                # 删除无效的小文件
                os.remove(file_full_name)
                logger.warning(f'[{code}] 删除无效文件: {file_name}')
        
        try:
            # 添加超时设置和更好的错误处理
            rs = requests.get(url, stream=True, timeout=30, headers=HEADER)
            rs.raise_for_status()  # 检查HTTP状态码
            
            with open(file_full_name, "wb") as fp:
                for chunk in rs.iter_content(chunk_size=10240):
                    if chunk:
                        fp.write(chunk)
            
            # 验证下载的文件大小
            if os.path.getsize(file_full_name) < 1024:
                os.remove(file_full_name)
                raise Exception(f"下载的文件过小，可能损坏: {file_name}")
            
            logger.info(f'[{code}] 成功保存文件: {file_name}')
            
        except requests.exceptions.RequestException as e:
            logger.error(f'[{code}] 网络请求失败: {str(e)}')
            raise
        except IOError as e:
            logger.error(f'[{code}] 文件操作失败: {str(e)}')
            raise
        except Exception as e:
            logger.error(f'[{code}] 保存PDF失败: {str(e)}')
            raise


def download_2024_annual_report(code, target_path='./SH/'):
    """智能下载2024年年报，存在则跳过"""
    file_path = os.path.join(target_path, code)
    if not os.path.isdir(file_path):
        os.makedirs(file_path)
    
    # 检查2024年年报是否已存在
    existing_files = os.listdir(file_path) if os.path.exists(file_path) else []
    has_2024_annual = any('2024' in f and ('年度报告' in f or '年报' in f) for f in existing_files)
    
    if has_2024_annual:
        logger.info(f'[{code}] 2024年年报已存在，跳过下载')
        return True
    
    # 精确查询2024年年报
    begin_date = '2024-01-01'
    end_date = '2024-12-31'
    
    try:
        pdf_urls = get_pdf_url(code, begin_date, end_date, report_type='年报')
        # 过滤出年度报告
        annual_reports = [url for url in pdf_urls if '年度报告' in url[1] or '年报' in url[1]]
        
        if annual_reports:
            for i in range(1, 6):  # 最多重试5次
                try:
                    save_pdf(code, annual_reports, target_path)
                    logger.info(f'[{code}] 成功下载2024年年报')
                    return True
                except Exception as e:
                    logger.warning(f'[{code}] 第{i}次尝试下载出错: {str(e)}')
                    if i == 5:
                        logger.error(f'[{code}] 下载失败，已重试5次')
                        return False
                    time.sleep(1)  # 重试前等待1秒
        else:
            logger.warning(f'[{code}] 未找到2024年年报')
            return False
    except Exception as e:
        logger.error(f'[{code}] 获取PDF链接失败: {str(e)}')
        return False


def main():
    """主函数：批量下载2024年年报"""
    stock_codes, _, _ = get_all_codes()
    total_stocks = len(stock_codes)
    
    # 判断目录是否存在，不存在则创建
    if not os.path.exists("./SH"):
        os.makedirs("./SH")
    
    # 统计变量
    success_count = 0
    skip_count = 0
    fail_count = 0
    
    logger.info(f"开始批量下载2024年年报，共{total_stocks}只股票")
    
    for index, code in enumerate(stock_codes, 1):
        print(f'进度: {index}/{total_stocks} ({index/total_stocks*100:.1f}%) - 处理股票: {code}', end=' ')
        
        try:
            result = download_2024_annual_report(code)
            if result:
                # 检查是否是跳过（已存在）还是新下载
                file_path = os.path.join('./SH', code)
                existing_files = os.listdir(file_path) if os.path.exists(file_path) else []
                has_2024_annual = any('2024' in f and ('年度报告' in f or '年报' in f) for f in existing_files)
                
                if has_2024_annual:
                    skip_count += 1
                    print('- 已存在，跳过')
                else:
                    success_count += 1
                    print('- 下载成功')
            else:
                fail_count += 1
                print('- 下载失败')
        except Exception as e:
            fail_count += 1
            logger.error(f'处理股票{code}时发生异常: {str(e)}')
            print('- 异常错误')
    
    # 输出最终统计
    print(f'\n任务完成！')
    print(f'总计: {total_stocks}只股票')
    print(f'成功下载: {success_count}个')
    print(f'已存在跳过: {skip_count}个')
    print(f'下载失败: {fail_count}个')
    logger.info(f"批量下载完成 - 成功:{success_count}, 跳过:{skip_count}, 失败:{fail_count}")
#
#
if __name__ == '__main__':
    result = requests.get(URL_QUERY_COMPANY, params=URL_PARAM, headers=HEADER)
    main()
