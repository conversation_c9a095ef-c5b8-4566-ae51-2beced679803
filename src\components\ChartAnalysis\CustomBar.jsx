import React, { useEffect, useRef } from "react";
import { Chart } from "@antv/g2";
const CustomBar = ({ data, height = 260, title, style, isFullscreen = false, isIndividualFullscreen = false }) => {
  const container = useRef();
  
  // 监听单个全屏状态变化
  useEffect(() => {
    const handleFullscreenChange = () => {
      // setIsIndividualFullscreen(!!document.fullscreenElement); // 移除 useState 和 setIsIndividualFullscreen
    };
    document.addEventListener('fullscreenchange', handleFullscreenChange);
    document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
    document.addEventListener('mozfullscreenchange', handleFullscreenChange);
    document.addEventListener('MSFullscreenChange', handleFullscreenChange);
    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
      document.removeEventListener('webkitfullscreenchange', handleFullscreenChange);
      document.removeEventListener('mozfullscreenchange', handleFullscreenChange);
      document.removeEventListener('MSFullscreenChange', handleFullscreenChange);
    };
  }, []);

  // 解析高度
  let computedHeight = height;
  if (isIndividualFullscreen) {
    // 单个全屏时，使用视口高度的90%
    computedHeight = window.innerHeight * 0.9;
  } else if (isFullscreen) {
    // 整体全屏时，直接使用传入的 height（已经通过 scale 计算过）
    computedHeight = height;
  } else if (style && style.height) {
    if (typeof style.height === 'number') {
      computedHeight = style.height;
    } else if (typeof style.height === 'string' && style.height.endsWith('px')) {
      computedHeight = parseInt(style.height);
    } else if (typeof style.height === 'string') {
      computedHeight = parseInt(style.height);
    }
  }

  // 如果height是字符串100%用容器高度
  if (typeof height === 'string' && height === '100%') {
    computedHeight = '100%';
  }

  // 如果 data 有数据，则取第一行的 year，否则为 ""
  const year = data && data.length > 0 && data[0].year ? data[0].year : "";

  useEffect(() => {
    if (!container.current) return;

    // 如果computedHeight是"100%"，获取容器实际高度
    let finalHeight = computedHeight;
    if (computedHeight === '100%') {
      const containerHeight = container.current.offsetHeight;
      finalHeight = containerHeight > 0 ? containerHeight : 260;
    }

    let chart = null;

    // 使用setTimeout确保DOM完全准备好
    const timer = setTimeout(() => {
      if (!container.current) return;

      try {
        chart = new Chart({
          container: container.current,
          height: finalHeight,

          title: {
            title: title,
            titleFontSize: 22, // 图表主标题的字体大小
            align: 'top',

          },
          autoFit: true,

        });

        // 计算缩放比例，用于响应式调整字体大小
        const baseHeight = 260; // 基准高度
        const scaleRatio = finalHeight / baseHeight;
        const fontSize = Math.max(12, Math.min(24, 14 * scaleRatio)); // 字体大小在12-20之间
        const labelFontSize = Math.max(10, Math.min(16, 14 * scaleRatio)); // 标签字体大小

        chart.options({
          type: 'interval',
          data,
          encode: {
            x: 'company',
            y: 'value',
            color: 'company'
          },
          axis: {
            y: false,
            x: { title: year, tick: false, label: false },
          },
          labels: [
            {
              text: (d) => {
                return d.displayValue ?? d.value ?? '';
              },
              position: 'top',
              style: { 
                fontSize: 14,
              }
            },
            {
              text: (d) => {
                const val = d.extra?.[0]?.value ?? 0;
                const display = d.extra?.[0]?.displayValue ?? val;
                if (val > 0) return `▲${display}`;
                if (val < 0) return `▼${display}`;
                return display;
              },
              position: 'top',
              style: {
                fill: (d) => d.extra?.[0]?.value > 0 ? 'red' : d.extra?.[0]?.value < 0 ? 'limegreen' : '#333',
                fontSize: 14, 
              },
              dy: 25 * scaleRatio,
            },
          ],
          interaction: {
            tooltip: {
              css: {

                '.g2-tooltip-list-item': {
                  'font-size': '18px',
                },

              },
              
            }
          },

          tooltip: {

            items: [
              (datum) => ({
                name: '年份',
                value: datum.year,
              }),
              (datum) => ({
                name: title,
                value: datum.displayValue ?? datum.value ?? '',
              }),
              (datum) => {
                // 防御：extra 可能为 undefined 或空数组
                if (!datum.extra || !Array.isArray(datum.extra) || datum.extra.length === 0) return null;
                if (title === datum.extra[0].title) return null; // 避免重复

                const val = datum.extra[0].value ?? 0;
                const display = datum.extra[0].displayValue ?? val;

                return {
                  name: datum.extra[0].title,
                  value: display,
                };
              },
            ],
          }
          ,
          legend: {
            color: {
              length: 230, maxRows: 1,
              itemSpacing: [0, 0],
            },
            itemMarker: {
              style: {
                r: 4 * scaleRatio, // 图例标记大小
              }
            },
            itemContent: {
              style: {
                fontSize: fontSize, // 图例文字大小
              }
            }
          },
        });
        chart.render();

        // 添加 resize 监听器，当容器尺寸变化时重新渲染
        const resizeObserver = new ResizeObserver(() => {
          if (chart && !chart.destroyed) {
            chart.render();
          }
        });
        
        if (container.current) {
          resizeObserver.observe(container.current);
        }

        return () => {
          clearTimeout(timer);
          resizeObserver.disconnect();
          if (chart) {
            try {
              chart.destroy();
            } catch (error) {
              // 忽略销毁时的错误
            }
          }
        };
      } catch (error) {
        console.error('Error creating chart:', error);
      }
    }, 0);

    return () => {
      clearTimeout(timer);
      if (chart) {
        try {
          chart.destroy();
        } catch (error) {
          // 忽略销毁时的错误
        }
      }
    };
  }, [data, height, title, style, isIndividualFullscreen]);
  return <div ref={container} style={{ width: '100%', height: '100%' }} />;
};
export default CustomBar; 