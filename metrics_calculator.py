# metrics_calculator.py
import argparse
import json
import time
from datetime import datetime
from L2.metrics_calculator_refactored import MetricsCalculatorRefactored
from L2.metric_data_loader import MetricDataLoader # 导入 MetricDataLoader

def print_results(results, stock_code):
    if results:
        print(f"\n--- 结果: {stock_code} ---")
        print(f"公司名称: {results['company_name']}")
        print(f"报告日期: {results['report_date']}")
        print(f"计算统计: 成功 {results['statistics']['success_count']} 个指标，失败 {results['statistics']['failure_count']} 个指标")
        print(f"执行时间: {results['statistics']['execution_time']:.3f} 秒")

        print("\n指标结果:")
        for metric_cn, data in results['metrics'].items():
            value = data.get('value')
            indicator_en = data.get('indicator_name_en', 'N/A')
            error_msg = data.get('error')

            if error_msg:
                print(f"{metric_cn} ({indicator_en}): 计算错误 - {error_msg}")
            elif value is not None:
                try:
                    print(f"{metric_cn} ({indicator_en}): {float(value):.4f}")
                except (TypeError, ValueError):
                    print(f"{metric_cn} ({indicator_en}): {value}")
            else:
                print(f"{metric_cn} ({indicator_en}): N/A (无计算结果)")

            lower_bound = data.get('lower_bound')
            upper_bound = data.get('upper_bound')
            lower_bound_label = data.get('lower_bound_label')
            upper_bound_label = data.get('upper_bound_label')
            evaluation = '一般'
            if not error_msg and lower_bound is not None and upper_bound is not None and value is not None:
                try:
                    from decimal import Decimal, InvalidOperation
                    cv = Decimal(str(value))
                    lb = Decimal(str(lower_bound))
                    ub = Decimal(str(upper_bound))
                    
                    # 使用数据库中的标签，如果没有则使用默认标签
                    if cv < lb:
                        evaluation = lower_bound_label if lower_bound_label else "较差"
                    elif cv > ub:
                        evaluation = upper_bound_label if upper_bound_label else "优秀"
                    else:
                        evaluation = "良好"
                    
                    print(f"  评估: {evaluation} (范围: {lower_bound} - {upper_bound})")
                except (InvalidOperation, ValueError, TypeError):
                    print(f"  无法评估 {value} (范围: {lower_bound} - {upper_bound})")
            elif not error_msg:
                 print(f"  评估: {evaluation} (无可用范围)")
            print("-" * 30)

        if results['statistics']['error_logs']:
            print("\n计算错误摘要:")
            for log in results['statistics']['error_logs']:
                print(f"- 指标: {log['indicator_name_cn']} ({log['indicator_name_en']})")
                print(f"  错误: {log['error_message']}")
        print("=" * 50)
    else:
        print(f"计算股票 {stock_code} 的指标失败或无数据。")
def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='计算财务指标')
    parser.add_argument('--stock_code', type=str, help='可选：指定单个股票代码进行计算')
    parser.add_argument('--years', nargs='+', type=int, default=[2025], help='要处理的年份列表，例如：2023 2024')
    parser.add_argument('--quarters', nargs='+', type=str, default=['0331', '0630', '0930', '1231'], 
                       choices=['0331', '0630', '0930', '1231'], help='要处理的季度列表')
    parser.add_argument('--no_save', action='store_true', help='不保存结果到数据库')
    parser.add_argument('--force_recalculate', action='store_true', help='强制重新计算，即使数据已存在')
    parser.add_argument('--output_dir', type=str, help='可选：输出目录，将每个股票的结果保存到单独的JSON文件')
    parser.add_argument('--check_only', action='store_true', help='仅检查未处理的数据，不执行计算')
    parser.add_argument('--log_level', type=str, default='INFO', 
                       choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'], help='日志级别')

    args = parser.parse_args()

    # 设置日志级别
    import logging
    logging.basicConfig(
        level=getattr(logging, args.log_level),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(f'metrics_calculation_{datetime.now().strftime("%Y%m%d")}.log', encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    logger = logging.getLogger('MetricsCalculator')

    calculator = MetricsCalculatorRefactored(logger)
    data_loader = MetricDataLoader()
    save_to_db = not args.no_save

    # 生成需要处理的日期列表
    dates_to_process = []
    for year in args.years:
        for quarter in args.quarters:
            dates_to_process.append(f"{year}{quarter}")
    
    logger.info(f"配置信息:")
    logger.info(f"  年份: {args.years}")
    logger.info(f"  季度: {args.quarters}")
    logger.info(f"  日期列表: {dates_to_process}")
    logger.info(f"  保存到数据库: {save_to_db}")
    logger.info(f"  强制重新计算: {args.force_recalculate}")
    logger.info(f"  仅检查模式: {args.check_only}")
    
    stock_codes_to_process = []
    if args.stock_code:
        stock_codes_to_process.append(args.stock_code)
        logger.info(f"指定计算单个股票: {args.stock_code}")
        print(f"指定计算单个股票: {args.stock_code}")
    else:
        logger.info("未指定股票代码，将从数据库获取所有活跃股票代码...")
        print("未指定股票代码，将从数据库获取所有活跃股票代码...")
        stock_codes_to_process = data_loader.get_all_active_stock_codes()
        if not stock_codes_to_process:
            error_msg = "未能从数据库获取到任何活跃股票代码，程序退出。"
            logger.error(error_msg)
            print(error_msg)
            exit()
        logger.info(f"将处理 {len(stock_codes_to_process)} 个股票代码。")
        print(f"将处理 {len(stock_codes_to_process)} 个股票代码。")

    # 检查未处理的数据
    if args.check_only:
        logger.info("仅检查模式：开始检查未处理的数据...")
        print("仅检查模式：开始检查未处理的数据...")
        
        unprocessed_data = calculator.get_unprocessed_data(stock_codes_to_process, dates_to_process)
        
        print(f"\n===== 未处理数据检查结果 =====")
        print(f"总数据项: {len(stock_codes_to_process) * len(dates_to_process)}")
        print(f"已处理项: {len(stock_codes_to_process) * len(dates_to_process) - len(unprocessed_data)}")
        print(f"未处理项: {len(unprocessed_data)}")
        
        if unprocessed_data:
            print("\n未处理的数据项:")
            for stock_code, date_str in unprocessed_data:
                print(f"  {stock_code} - {date_str}")
        else:
            print("所有数据都已处理完成！")
        
        logger.info(f"检查完成：{len(unprocessed_data)} 个未处理项")
        exit()

    # 获取未处理的数据
    unprocessed_data = calculator.get_unprocessed_data(stock_codes_to_process, dates_to_process)
    
    if not args.force_recalculate and not unprocessed_data:
        logger.info("所有数据都已处理完成，无需重新计算")
        print("所有数据都已处理完成，无需重新计算")
        exit()
    
    if args.force_recalculate:
        logger.info("强制重新计算模式：将处理所有数据")
        print("强制重新计算模式：将处理所有数据")
        data_to_process = [(stock_code, date) for stock_code in stock_codes_to_process for date in dates_to_process]
    else:
        logger.info(f"将处理 {len(unprocessed_data)} 个未处理的数据项")
        print(f"将处理 {len(unprocessed_data)} 个未处理的数据项")
        data_to_process = unprocessed_data

    total_start_time = time.time()
    all_results = {}
    overall_success_count = 0
    overall_failure_count = 0
    overall_skipped_count = 0

    print(f"\n将处理以下日期: {', '.join(dates_to_process)}")
    
    for date in dates_to_process:
        logger.info(f"开始处理日期: {date}")
        print(f"\n===== 开始处理日期: {date} =====")
        
        # 获取当前日期需要处理的数据
        current_date_data = [item for item in data_to_process if item[1] == date]
        
        for i, (stock_code, date_str) in enumerate(current_date_data):
            logger.info(f"[{i+1}/{len(current_date_data)}] 开始计算 {stock_code} 在 {date_str} 的财务指标...")
            print(f"\n[{i+1}/{len(current_date_data)}] 开始计算 {stock_code} 在 {date_str} 的财务指标...")
            results = calculator.calculate_metrics(stock_code, date_str, save_to_db=save_to_db, force_recalculate=args.force_recalculate)
            
            if stock_code not in all_results:
                all_results[stock_code] = {}
            all_results[stock_code][date_str] = results
            
            print_results(results, stock_code)

            # 统计处理结果
            if results:
                if results['statistics'].get('skipped', False):
                    overall_skipped_count += 1
                    logger.info(f"股票 {stock_code} 在 {date_str} 的数据已跳过")
                elif results['statistics']['success_count'] > 0:
                    overall_success_count += 1
                    logger.info(f"股票 {stock_code} 在 {date_str} 的计算成功")
                else:
                    overall_failure_count += 1
                    logger.warning(f"股票 {stock_code} 在 {date_str} 的计算失败")
            else:
                overall_failure_count += 1
                logger.error(f"股票 {stock_code} 在 {date_str} 的计算返回空结果")

            if args.output_dir and results:
                import os
                os.makedirs(args.output_dir, exist_ok=True)
                output_file_path = os.path.join(args.output_dir, f"{stock_code}_{date_str}.json")
                try:
                    with open(output_file_path, 'w', encoding='utf-8') as f:
                        json.dump(results, f, ensure_ascii=False, indent=4)
                    logger.info(f"结果已保存到文件: {output_file_path}")
                    print(f"结果已保存到文件: {output_file_path}")
                except Exception as e:
                    logger.error(f"保存到文件 {output_file_path} 失败: {e}")
                    print(f"保存到文件 {output_file_path} 失败: {e}")
            
            if save_to_db and results:
                logger.info(f"{stock_code} 的结果已处理保存到数据库的逻辑 (由 MetricsCalculatorRefactored 完成)。")
                print(f"{stock_code} 的结果已处理保存到数据库的逻辑 (由 MetricsCalculatorRefactored 完成)。")

    total_end_time = time.time()
    total_execution_time = total_end_time - total_start_time

    # 记录总结信息
    summary_msg = f"""
===== 批量计算总结 =====
处理年份: {args.years}
处理季度: {args.quarters}
处理日期: {', '.join(dates_to_process)}
总共处理股票数量: {len(stock_codes_to_process)}
成功计算至少一个指标的股票数量: {overall_success_count}
完全失败或无数据的股票数量: {overall_failure_count}
跳过的股票数量: {overall_skipped_count}
总执行时间: {total_execution_time:.2f} 秒
"""
    
    if args.output_dir:
        summary_msg += f"详细结果已输出到目录: {args.output_dir}\n"
    if save_to_db:
        summary_msg += "结果已根据设置尝试保存到数据库。\n"
    summary_msg += "========================="
    
    logger.info(summary_msg)
    print(summary_msg)

if __name__ == "__main__":
    main()
