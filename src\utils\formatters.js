/**
 * 格式化指标值
 * @param {number|string} value - 要格式化的值
 * @param {string} valueType - 值的类型（%, 亿, 等）
 * @param {string} dataUnit - 数据单位
 * @param {Object} metric - 指标对象，包含指标的详细信息
 * @param {boolean} forExport - 是否用于导出（返回数字格式）
 * @returns {string|number} 格式化后的值
 */
export const formatMetricValue = (value, valueType, dataUnit, metric, forExport = false) => {
  // 处理undefined/null - 导出时返回null而不是0
  if (value === undefined || value === null) return forExport ? null : "-";
  
  // 处理0值 - 真实的0值应该显示，导出时保留两位小数
  if (value === 0 || value === "0") return forExport ? parseFloat((0).toFixed(2)) : "0.00";

  const numValue = Number(value);
  const trimmedType = valueType ? valueType.trim() : "";
  
  // 特殊处理分红金额和每万名研发人员主营业务收入
  if (metric && (metric.code === 'div_aualcashdividend' || metric.name === '分红金额' ||
                 metric.code === 'revenue_per_rd_personnel' || metric.name === '每万名研发人员主营业务收入')) {
    const result = numValue / 100000000;
    return forExport ? parseFloat(result.toFixed(2)) : result.toFixed(2);
  }

  if (trimmedType === "%") {
    const result = numValue * 100;
    return forExport ? parseFloat(result.toFixed(2)) : (result.toFixed(2) + "%");
  } else if (trimmedType === "亿") {
    const result = numValue / 100000000;
    return forExport ? parseFloat(result.toFixed(2)) : result.toFixed(2);
  } else {
    return forExport ? parseFloat(numValue.toFixed(2)) : numValue.toFixed(2);
  }
};

/**
 * 生成报告日期
 * @param {number} year - 年份
 * @param {string} quarterValue - 季度值
 * @param {Array} quarters - 季度信息数组
 * @returns {string} 格式化的日期字符串 (YYYY-MM-DD)
 */
export const generateReportDate = (year, quarterValue, quarters) => {
  const quarterInfo = quarters.find((q) => q.value === quarterValue);
  if (year && quarterInfo) {
    return `${year}-${quarterInfo.month}-${quarterInfo.day}`;
  }
  // 默认返回当前年份的年报日期
  const currentYear = new Date().getFullYear();
  return `${currentYear}-12-31`;
};

/**
 * 处理表格数据
 * @param {Object} data - 包含指标值、指标定义和公司信息的数据对象
 * @param {Array} metrics - 指标定义数组
 * @returns {Array} 处理后的表格数据
 */
export const processTableData = (data, metrics) => {
  const companiesMap = new Map(); // 使用 Map 按 company_id 分组

  data.metrics_values.forEach((item) => {
    if (!companiesMap.has(item.company_id)) {
      const companyInfo = data.companies.find((c) => c.id === item.company_id);
      companiesMap.set(item.company_id, {
        company_id: item.company_id,
        company_name: companyInfo ? companyInfo.name : item.company_id,
        key: item.company_id,
        isInternal: companyInfo ? companyInfo.IsInternal : false,
        companyGroup: companyInfo && companyInfo.IsInternal ? "集团内公司" : "集团外公司",
        metric_values: {},
      });
    }
    // 将当前指标的值和评估结果存入对应公司的 metric_values 对象中
    companiesMap.get(item.company_id).metric_values[item.metric_name] = {
      value: item.value,
      evaluation_result: item.EvaluationResult,
      translated_formula: item.TranslatedFormula,
    };
  });

  // 将 Map 转换为数组，作为表格的 dataSource
  const tableData = Array.from(companiesMap.values());

  // 按照内部公司和其他公司分组排序
  tableData.sort((a, b) => {
    // 首先按照是否为内部公司排序（内部公司在前）
    if (a.isInternal !== b.isInternal) {
      return a.isInternal ? -1 : 1;
    }
    // 然后按照公司名称排序
    return (a.company_name || "").localeCompare(b.company_name || "");
  });

  // 计算每个指标的排名
  const rankedData = tableData.map(item => {
    const rankedItem = {...item};
    rankedItem.metric_ranks = {};
    
    // 对每个指标进行排序并记录排名
    metrics.forEach(metric => {
      const sortedValues = [...tableData]
        .sort((a, b) => {
          const valA = a.metric_values[metric.code]?.value || 0;
          const valB = b.metric_values[metric.code]?.value || 0;
          return valB - valA; // 降序排列
        })
        .map((x, i) => ({
          company_id: x.company_id,
          rank: i + 1
        }));
      
      const companyRank = sortedValues.find(x => x.company_id === item.company_id);
      rankedItem.metric_ranks[metric.code] = companyRank ? companyRank.rank : '-';
    });
    
    return rankedItem;
  });

  // 添加固定排名行
  const rankRow = {
    company_id: 'rank_row',
    company_name: '',
    companyGroup: '',
    key: 'rank_row',
    isRankRow: true,
    isFixedRow: true,
    metric_values: {},
    metric_ranks: {}
  };

  // 对每个指标计算排名行的值
  metrics.forEach(metric => {
    rankRow.metric_values[metric.code] = {
      value: '排名',
      evaluation_result: '排名'
    };
    
    // 计算并存储排名
    const sorted = [...tableData].sort((a, b) => {
      const valA = a.metric_values[metric.code]?.value || 0;
      const valB = b.metric_values[metric.code]?.value || 0;
      return valB - valA; // 降序排列
    });
    
    // 存储所有公司的排名
    sorted.forEach((item, index) => {
      if (!rankRow.metric_ranks[item.company_id]) {
        rankRow.metric_ranks[item.company_id] = {};
      }
      rankRow.metric_ranks[item.company_id][metric.code] = index + 1;
    });
  });

  // 确保排名行在最前面
  return [rankRow, ...rankedData];
};

// 获取L2Metrics最新报告日期
export const getLatestL2MetricsReportDate = async () => {
  try {
    const response = await fetch('./api/report-times/latest-l2metrics');
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    const data = await response.json();
    return data;
  } catch (error) {
    console.error('获取最新L2Metrics报告日期失败:', error);
    // 返回默认值
    return {
      ReportDate: '2024-12-31',
      Year: 2024,
      Quarter: 4
    };
  }
};

// 根据L2Metrics最新报告日期获取默认年份和季度
export const getDefaultYearAndQuarter = async () => {
  try {
    const latestData = await getLatestL2MetricsReportDate();
    if (latestData && latestData.ReportDate) {
      const reportDate = new Date(latestData.ReportDate);
      const year = reportDate.getFullYear();
      const month = reportDate.getMonth() + 1;
      
      // 根据月份确定季度
      let quarter = 'ANNUAL';
      if (month === 3) quarter = 'Q1';
      else if (month === 6) quarter = 'Q2';
      else if (month === 9) quarter = 'Q3';
      else if (month === 12) quarter = 'ANNUAL';
      
      return { year, quarter };
    }
  } catch (error) {
    console.error('获取默认年份和季度失败:', error);
  }
  
  // 返回默认值
  return { year: 2024, quarter: 'ANNUAL' };
};
