import os
import sys
import requests
import pickle
from bs4 import BeautifulSoup

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database.db_handler import DatabaseHandler
from logging_config import setup_logging
from config import Config
import pandas as pd

logger = setup_logging()

BASE_URL = "https://vip.stock.finance.sina.com.cn/corp/go.php/vCB_Bulletin/stockid/{}/page_type/ndbg.phtml"

class SinaCrawler:
    def __init__(self):
        self.sessions = {}
        self.cookie_dir = './cookies'
        if not os.path.exists(self.cookie_dir):
            os.makedirs(self.cookie_dir)
            
    def _get_session(self, stock_code):
        """获取或创建指定股票代码的session"""
        if stock_code not in self.sessions:
            session = requests.Session()
            session.headers.update({
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
                'accept-language': 'zh-CN,zh;q=0.9',
                'cache-control': 'no-cache',
                'dnt': '1',
                'pragma': 'no-cache',
                'priority': 'u=0, i',
                'sec-ch-ua': '"Microsoft Edge";v="135", "Not-A.Brand";v="8", "Chromium";v="135"',
                'sec-ch-ua-mobile': '?0',
                'sec-ch-ua-platform': '"Windows"',
                'sec-fetch-dest': 'document',
                'sec-fetch-mode': 'navigate',
                'sec-fetch-site': 'none',
                'sec-fetch-user': '?1',
                'upgrade-insecure-requests': '1'
            })
            
            # 加载cookie文件
            cookie_file = os.path.join(self.cookie_dir, f'{stock_code}.cookie')
            if os.path.exists(cookie_file):
                try:
                    with open(cookie_file, 'rb') as f:
                        cookies = pickle.load(f)
                        if cookies:  # 检查cookie是否为空
                            session.cookies.update(cookies)
                except (EOFError, pickle.UnpicklingError) as e:
                    logger.warning(f"股票{stock_code}的cookie文件损坏或为空，将创建新的session: {str(e)}")
                    os.remove(cookie_file)  # 删除损坏的cookie文件
            
            self.sessions[stock_code] = session
        return self.sessions[stock_code]
        
    def _save_cookies(self, stock_code):
        """保存cookie到文件"""
        if stock_code in self.sessions:
            cookie_file = os.path.join(self.cookie_dir, f'{stock_code}.cookie')
            with open(cookie_file, 'wb') as f:
                pickle.dump(self.sessions[stock_code].cookies, f)

    def get_pdf_links(self, stock_code, target_year=None):
        """获取指定股票代码的年报PDF链接"""
        url = BASE_URL.format(stock_code)
        try:
            session = self._get_session(stock_code)
            response = session.get(url)
            response.raise_for_status()
            soup = BeautifulSoup(response.text, 'html.parser')
            self._save_cookies(stock_code)
            
            pdf_links = []
            
            # 解析表格中的年报链接
            for table in soup.select('table.table2'):
                for a in table.select('a[href*="vCB_AllBulletinDetail"]'):
                    link_text = a.get_text()
                    if '年度报告' in link_text:
                        # 如果指定了目标年份，检查是否匹配
                        if target_year and str(target_year) not in link_text:
                            continue
                            
                        detail_url = a['href']
                        if not detail_url.startswith('http'):
                            detail_url = 'https://vip.stock.finance.sina.com.cn' + detail_url
                        
                        # 获取详情页内容
                        try:
                            detail_response = session.get(detail_url)
                            detail_response.raise_for_status()
                            detail_soup = BeautifulSoup(detail_response.text, 'html.parser')
                            
                            # 从详情页中提取PDF链接
                            for pdf_a in detail_soup.select('a[href*="PDF"]'):
                                pdf_url = pdf_a['href']
                                if not pdf_url.startswith('http'):
                                    pdf_url = 'https:' + pdf_url
                                title = pdf_a.get_text(strip=True)
                                # 如果指定了目标年份，再次检查标题
                                if target_year and str(target_year) not in title:
                                    continue
                                pdf_links.append((title, pdf_url))
                                
                            # 检查详情页中的下载按钮
                            for download_a in detail_soup.select('a[href*="PDF"][target="_blank"]'):
                                if '下载公告' in download_a.get_text():
                                    pdf_url = download_a['href']
                                    if not pdf_url.startswith('http'):
                                        pdf_url = 'https:' + pdf_url
                                    title = download_a.parent.parent.get_text(strip=True).replace('下载公告', '').strip()
                                    # 如果指定了目标年份，检查标题
                                    if target_year and str(target_year) not in title:
                                        continue
                                    pdf_links.append((title, pdf_url))
                        except Exception as e:
                            logger.error(f"获取详情页{detail_url}失败: {str(e)}")
                            continue
            
            return pdf_links
        except Exception as e:
            logger.error(f"获取股票{stock_code}年报链接失败: {str(e)}")
            return []

    def download_pdf(self, url, save_path, stock_code=None, max_retries=3):
        """下载PDF文件，支持重试机制"""
        import time
        
        for attempt in range(max_retries):
            try:
                session = self._get_session(stock_code) if stock_code else requests.Session()
                
                # 添加超时设置
                response = session.get(url, stream=True, timeout=30)
                response.raise_for_status()
                
                if stock_code:
                    self._save_cookies(stock_code)
                
                # 检查响应内容类型
                content_type = response.headers.get('content-type', '').lower()
                if 'pdf' not in content_type and 'application/octet-stream' not in content_type:
                    logger.warning(f"响应内容可能不是PDF文件: {content_type}")
                
                with open(save_path, 'wb') as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        if chunk:
                            f.write(chunk)
                
                # 验证下载的文件大小
                if os.path.getsize(save_path) < 1024:
                    os.remove(save_path)
                    raise Exception("下载的文件过小，可能损坏")
                
                logger.info(f"成功下载PDF: {save_path}")
                return True
                
            except Exception as e:
                logger.error(f"下载PDF失败 (尝试 {attempt + 1}/{max_retries}): {url}, 错误: {str(e)}")
                
                # 如果文件存在但下载失败，删除它
                if os.path.exists(save_path):
                    try:
                        os.remove(save_path)
                    except:
                        pass
                
                if attempt < max_retries - 1:
                    time.sleep(2)  # 等待2秒后重试
                else:
                    return False
        
        return False

    def get_all_stock_codes(self):
        """从数据库获取所有股票代码"""
        codes = []
        try:
            with DatabaseHandler() as db:
                cursor = db.connection.cursor()
                cursor.execute("""
                    SELECT TickerSymbol FROM CompanyInfo WHERE IsActive=1
                """)
                for row in cursor.fetchall():
                    code = row.TickerSymbol.split('.')[0]  # 去除.SH/.SZ后缀
                    codes.append(code)
            logger.info(f"从数据库获取{len(codes)}条股票代码")
        except Exception as e:
            logger.error(f"获取股票代码失败: {str(e)}")
        return codes

    def check_2024_annual_report_exists(self, stock_code, output_dir='./SINA/'):
        """检查2024年年报是否已存在"""
        code_dir = os.path.join(output_dir, stock_code)
        if not os.path.exists(code_dir):
            return False
            
        for filename in os.listdir(code_dir):
            if '2024' in filename and ('年度报告' in filename or '年报' in filename) and filename.endswith('.pdf'):
                file_path = os.path.join(code_dir, filename)
                # 检查文件大小，确保不是损坏的文件
                if os.path.getsize(file_path) > 1024:  # 大于1KB
                    return True
        return False
    
    def download_2024_annual_reports(self, output_dir='./SINA/'):
        """专门下载2024年年报，只下载不存在的"""
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
            
        stock_codes = self.get_all_stock_codes()
        total_stocks = len(stock_codes)
        success_count = 0
        skip_count = 0
        fail_count = 0
        excel_data = []
        
        logger.info(f"开始批量下载2024年年报，共{total_stocks}只股票")
        
        for index, code in enumerate(stock_codes, 1):
            print(f'进度: {index}/{total_stocks} ({index/total_stocks*100:.1f}%) - 处理股票: {code}', end=' ')
            
            try:
                # 检查2024年年报是否已存在
                if self.check_2024_annual_report_exists(code, output_dir):
                    skip_count += 1
                    print('- 2024年报已存在，跳过')
                    continue
                
                # 获取2024年年报链接
                pdf_links = self.get_pdf_links(code, target_year=2024)
                if not pdf_links:
                    fail_count += 1
                    print('- 未找到2024年报')
                    logger.info(f"股票{code}没有找到2024年年报PDF")
                    continue
                    
                code_dir = os.path.join(output_dir, code)
                if not os.path.exists(code_dir):
                    os.makedirs(code_dir)
                
                downloaded_any = False
                for title, url in pdf_links:
                    # 确保是2024年年报
                    if '2024' not in title:
                        continue
                        
                    filename = f"{code}_{title}.pdf".replace('/', '_').replace('\\', '_')
                    save_path = os.path.join(code_dir, filename)
                    
                    excel_data.append({
                        '股票代码': code,
                        '标题': title,
                        'URL': url,
                        '保存路径': save_path,
                        '下载状态': '待下载'
                    })
                    
                    if os.path.exists(save_path):
                        file_size = os.path.getsize(save_path)
                        if file_size > 1024:  # 文件大于1KB认为有效
                            excel_data[-1]['下载状态'] = '已存在'
                            continue
                        else:
                            # 删除无效文件
                            os.remove(save_path)
                    
                    if self.download_pdf(url, save_path, code):
                        excel_data[-1]['下载状态'] = '下载成功'
                        downloaded_any = True
                        logger.info(f"成功下载2024年报: {save_path}")
                    else:
                        excel_data[-1]['下载状态'] = '下载失败'
                        logger.error(f"下载2024年报失败: {save_path}")
                
                if downloaded_any:
                    success_count += 1
                    print('- 下载成功')
                else:
                    fail_count += 1
                    print('- 下载失败')
                    
            except Exception as e:
                fail_count += 1
                logger.error(f'处理股票{code}时发生异常: {str(e)}')
                print('- 异常错误')
        
        # 输出最终统计
        print(f'\n任务完成！')
        print(f'总计: {total_stocks}只股票')
        print(f'成功下载: {success_count}个')
        print(f'已存在跳过: {skip_count}个')
        print(f'下载失败: {fail_count}个')
        logger.info(f"2024年年报批量下载完成 - 成功:{success_count}, 跳过:{skip_count}, 失败:{fail_count}")
        
        # 保存Excel文件
        if excel_data:
            df = pd.DataFrame(excel_data)
            excel_path = os.path.join(output_dir, '2024年报下载记录.xlsx')
            df.to_excel(excel_path, index=False)
            logger.info(f"已生成Excel文件: {excel_path}")
    
    def run(self, output_dir='./SINA/'):
        """运行爬虫"""
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
            
        stock_codes = self.get_all_stock_codes()
        excel_data = []
        
        for code in stock_codes:
            pdf_links = self.get_pdf_links(code)
            if not pdf_links:
                logger.info(f"股票{code}没有找到年报PDF")
                continue
                
            code_dir = os.path.join(output_dir, code)
            if not os.path.exists(code_dir):
                os.makedirs(code_dir)
                
            for title, url in pdf_links:
                filename = f"{code}_{title}.pdf".replace('/', '_')
                save_path = os.path.join(code_dir, filename)
                excel_data.append({
                    '股票代码': code,
                    '标题': title,
                    'URL': url,
                    '保存路径': save_path
                })
                
                if os.path.exists(save_path):
                    logger.info(f"文件已存在: {save_path}")
                    continue
                    
                if self.download_pdf(url, save_path, code):
                    logger.info(f"成功下载: {save_path}")
                else:
                    logger.error(f"下载失败: {save_path}")
        
        # 保存Excel文件
        if excel_data:
            df = pd.DataFrame(excel_data)
            excel_path = os.path.join(output_dir, '年报下载记录.xlsx')
            df.to_excel(excel_path, index=False)
            logger.info(f"已生成Excel文件: {excel_path}")

if __name__ == '__main__':
    crawler = SinaCrawler()
    
    # 选择下载模式
    print("请选择下载模式:")
    print("1. 下载所有年报")
    print("2. 只下载2024年年报（推荐）")
    
    choice = input("请输入选择 (1 或 2，默认为2): ").strip()
    
    if choice == '1':
        print("开始下载所有年报...")
        crawler.run()
    else:
        print("开始下载2024年年报...")
        crawler.download_2024_annual_reports()