import React, { useEffect, useState } from 'react';  
import ChartFilterPanel from './ChartFilterPanel'; 
import DashboardLayout from './DashboardLayout'; 
import { COLOR_PALETTE, DEFAULT_COMPANY_IDS, START_YEAR, defaultQuarters } from './utils/constants';
import { useFilterState } from './hooks/useFilterState';
import { useCompanyData } from './hooks/useCompanyData';
import { useChartData } from './hooks/useChartData'; 
import FullscreenWrapper from './FullscreenWrapper';
import { useRef } from 'react';
// 工具函数：生成reportDate
function generateReportDate(year, quarter, quarters) {
  const q = quarters.find(q => q.value === quarter);
  if (!q) return `${year}-12-31`;
  return `${year}-${q.month}-${q.day}`;
}

// API获取公司定义（公司列表）
const fetchApiData = async (reportDate) => {
  const apiBaseUrl = "./api";
  const res = await fetch(`${apiBaseUrl}/companies?table=L2Metrics&reportDate=${reportDate}`);
  if (!res.ok) {
    throw new Error(`HTTP error! status: ${res.status}`);
  }
  // 只返回公司定义
  const data = await res.json();
  data.forEach(c => {
    c.ticker_symbol = c.id;
  });
  return data;
};

// 通用数据处理函数
function generateChartData(keys, typeName, processedValues, comps, selectedCompanyIds) {
  return comps.filter(c => selectedCompanyIds.includes(c.ticker_symbol)).map(c => {
    const v = processedValues.find(val => val.TickerSymbol === c.ticker_symbol && keys.some(k => k.key === val.IndicatorNameEN));
    return {
      company: c.name || c.ticker_symbol,
      value: v ? v.value : null,
      displayValue: v ? v.displayValue : null,
      type: typeName,
    };
  });
}

const ChartsDisplay = ({ chartIndicators }) => {
  useEffect(() => {
    if (chartIndicators && chartIndicators.length > 0) {
      setFilteredIndicators(chartIndicators);
    }
  }, [chartIndicators]);
  
  // 筛选项 hook
  const {
    years, quarters,
    tempSelectedYear, setTempSelectedYear,
    tempSelectedQuarter, setTempSelectedQuarter,
    tempSelectedCompanies, setTempSelectedCompanies
  } = useFilterState();

  // 公司数据 hook
  const { companies, loading: companyLoading } = useCompanyData(tempSelectedYear, tempSelectedQuarter, quarters);

  // 选中项 - 设置默认值
  const [selectedYear, setSelectedYear] = useState();
  const [selectedQuarter, setSelectedQuarter] = useState();
  const [selectedCompanies, setSelectedCompanies] = useState([]);

  // 图表数据 hook
  const {
    loading: chartLoading,
    barData, radarData, radarIndicators,
    assetStackData, debtStackData,
    debtToAssetsData, totalAssetTurnoverData, equityTurnoverData, grossMarginData, threeFeeRatioData,
    netProfitMarginData, roeBasicData, twoFundNetData,twoFundIncomeData, inventoryTurnoverData,
    receivablesTurnoverData, netCashFlowsOperActData, cashCycleData,
    revenuePerRdPersonnelData,
    cashCollectionRatioData, rdInvestData, rdExpData, revenuePerPersonData,
    divAualCashDividendData, employeeData, nonRecurringProfitLossData,cashFlowData
  } = useChartData(selectedYear, selectedQuarter, selectedCompanies, quarters, companies, chartIndicators);

  // 自动更新选中的值（默认加载数据）
  const [hasInitialized, setHasInitialized] = useState(false);
  
  useEffect(() => {
    // 确保所有必要的筛选条件都已设置，且只初始化一次
    if (!hasInitialized && tempSelectedYear && tempSelectedQuarter && tempSelectedCompanies && tempSelectedCompanies.length > 0) {
      // 使用 setTimeout 来确保状态更新是异步的，避免重复触发
      const timer = setTimeout(() => {
        setSelectedYear(tempSelectedYear);
        setSelectedQuarter(tempSelectedQuarter);
        setSelectedCompanies(tempSelectedCompanies);
        setHasInitialized(true);
      }, 100);
      
      return () => clearTimeout(timer);
    }
  }, [tempSelectedYear, tempSelectedQuarter, tempSelectedCompanies, hasInitialized]);

  // 检索按钮（保留用于手动刷新）
  const handleSearch = () => {
    setSelectedYear(tempSelectedYear);
    setSelectedQuarter(tempSelectedQuarter);
    setSelectedCompanies(tempSelectedCompanies);
  };

  // 统计所有柱状图实际有数据的公司ID
  const allCompanies = React.useMemo(() => {
    if (!barData || !Array.isArray(barData)) return [];
    // 有些 chart 直接是公司名，有些在 data 下面
    return Array.from(new Set(
      barData.flatMap(chart => {
        if (Array.isArray(chart)) {
          // 直接是公司名数组
          return chart.map(d => d.company);
        } else if (chart && Array.isArray(chart.data)) {
          // 在 data 下面
          return chart.data.map(d => d.company);
        }
        return [];
      })
    ));
  }, [barData]);
  const companyColorMap = React.useMemo(() => {
    const map = {};
    if (allCompanies && Array.isArray(allCompanies)) {
      allCompanies.forEach((id, idx) => {
        map[id] = COLOR_PALETTE[idx % COLOR_PALETTE.length];
      });
    }
    return map;
  }, [allCompanies]);

  // 全屏相关
  const fullscreenRef = useRef();
  const [isFullscreen, setIsFullscreen] = useState(false);

  // 监听全屏状态变化
  useEffect(() => {
    if (!fullscreenRef.current) return;
    const handler = () => {
      setIsFullscreen(fullscreenRef.current.isFullscreen);
    };
    document.addEventListener('fullscreenchange', handler);
    document.addEventListener('webkitfullscreenchange', handler);
    document.addEventListener('mozfullscreenchange', handler);
    document.addEventListener('MSFullscreenChange', handler);
    return () => {
      document.removeEventListener('fullscreenchange', handler);
      document.removeEventListener('webkitfullscreenchange', handler);
      document.removeEventListener('mozfullscreenchange', handler);
      document.removeEventListener('MSFullscreenChange', handler);
    };
  }, []);

  return (
    <div style={{ paddingTop: '10px', overflow: 'auto',}}>
      <ChartFilterPanel
        years={years}
        selectedYear={tempSelectedYear}
        quarters={quarters}
        selectedQuarter={tempSelectedQuarter}
        companies={(companies || []).map(c => ({ id: c.id, name: c.name }))}
        selectedCompanies={tempSelectedCompanies}
        onYearChange={setTempSelectedYear}
        onQuarterChange={setTempSelectedQuarter}
        onCompanyChange={setTempSelectedCompanies}
        style={{ flex: 1 }}
      >
       
        <button
          onClick={handleSearch}
          disabled={companyLoading || chartLoading}
          style={{
            background: '#1677ff',
            color: '#fff',
            border: 'none',
            borderRadius: 4,
            padding: '0 20px',
            height: 32,
            fontSize: 14,
            cursor: 'pointer',
            marginLeft: 8,
            boxShadow: '0 1px 2px rgba(0,0,0,0.03)',
            transition: 'background 0.2s',
            fontWeight: 500,
            opacity: companyLoading || chartLoading ? 0.6 : 1,
          }}
        >
          {companyLoading || chartLoading ? '加载中...' : '检索'}
        </button>
        <button
          onClick={() => fullscreenRef.current?.toggleFullscreen()}
          style={{
            background: isFullscreen ? '#f5222d' : '#1783FF',
            color: '#fff',
            border: 'none',
            borderRadius: 4,
            padding: '0 16px',
            height: 32,
            fontSize: 14,
            cursor: 'pointer',
            marginLeft: 8,
            fontWeight: 500,
            transition: 'background 0.2s',
          }}
        >
          {isFullscreen ? '退出全屏' : '全屏显示'}
        </button>
      </ChartFilterPanel>
      {/* 仪表盘主内容区域 */}
      <FullscreenWrapper ref={fullscreenRef}>
        {(isFs) => (
          companyLoading || chartLoading ? <div style={{textAlign:'center',padding:'40px'}}>加载中...</div> : (
            <DashboardLayout
              barData={barData}
              radarData={radarData}
              radarIndicators={radarIndicators}
              companyColorMap={companyColorMap}
              allCompanies={allCompanies}
              assetStackData={assetStackData}
              debtStackData={debtStackData}
              debtToAssetsData={debtToAssetsData}
              totalAssetTurnoverData={totalAssetTurnoverData}
              equityTurnoverData={equityTurnoverData}
              grossMarginData={grossMarginData}
              threeFeeRatioData={threeFeeRatioData}
              netProfitMarginData={netProfitMarginData}
              roeBasicData={roeBasicData}
              twoFundNetData={twoFundNetData}
              twoFundIncomeData={twoFundIncomeData}
              inventoryTurnoverData={inventoryTurnoverData}
              receivablesTurnoverData={receivablesTurnoverData}
              netCashFlowsOperActData={netCashFlowsOperActData}
              cashCycleData={cashCycleData}
              revenuePerRdPersonnelData={revenuePerRdPersonnelData}
              cashCollectionRatioData={cashCollectionRatioData}
              rdInvestData={rdInvestData}
              rdExpData={rdExpData}
              revenuePerPersonData={revenuePerPersonData}
              divAualCashDividendData={divAualCashDividendData}
              employeeData={employeeData}
              nonRecurringProfitLossData={nonRecurringProfitLossData}
              cashFlowData={cashFlowData}
              isFullscreen={isFs}
            />
          )
        )}
      </FullscreenWrapper>
    </div>
  );
};

export default ChartsDisplay;
