# 财务指标计算工具

本工具用于计算上市公司的财务指标，支持单个股票计算和批量计算，并可将结果保存到数据库。

## 文件说明

- `metrics_calculator.py`: 主要的指标计算器，支持计算单个股票的财务指标
- `batch_calculate_metrics.py`: 批量计算多个股票的财务指标
- `create_l2metrics_tables.sql`: 创建L2Metrics表结构的SQL脚本
- `create_l2benchmark_indicators.sql`: 创建L2BenchmarkIndicators表结构的SQL脚本，用于存储指标定义信息
- `docs/指标定义.txt`: 指标定义文件，包含指标名称、公式等
- `docs/指标名称映射.csv`: 指标名称映射文件，将中文指标名称映射到英文指标名称和公式

## 安装依赖

```bash
pip install pandas
```

## 使用方法

### 1. 创建数据库表结构

在SQL Server中执行`create_l2metrics_tables.sql`和`create_l2benchmark_indicators.sql`脚本，创建必要的表结构。

```sql
-- 在SQL Server Management Studio中执行
USE YourDatabaseName
GO
-- 执行create_l2metrics_tables.sql脚本内容
-- 执行create_l2benchmark_indicators.sql脚本内容
```

`create_l2benchmark_indicators.sql`脚本会创建L2BenchmarkIndicators表，用于存储指标定义信息，并将这些信息关联到计算结果中。

### 2. 计算单个股票的财务指标

```bash
python metrics_calculator.py --stock_code 600850.SH --date 20231231 --output_file results.json
```

参数说明：
- `--stock_code`: 股票代码，例如"600850.SH"
- `--date`: 日期，格式为YYYYMMDD，例如"20231231"
- `--no_save`: 不保存结果到数据库（默认会保存到数据库）
- `--output_file`: 输出结果到文件，不指定则不保存到文件

### 3. 批量计算多个股票的财务指标

```bash
python batch_calculate_metrics.py --stock_list stocks.txt --date 20231231 --output_dir results
```

参数说明：
- `--stock_list`: 股票列表文件路径，支持CSV和TXT格式
- `--date`: 日期，格式为YYYYMMDD，例如"20231231"
- `--no_save`: 不保存结果到数据库（默认会保存到数据库）
- `--output_dir`: 输出目录，如果指定，则将结果保存到该目录下的JSON文件

## 指标计算逻辑

1. 从数据库获取基础数据，包括当期、上期和上期末的数据
2. 从数据库或指标名称映射文件获取指标公式
3. 使用公式执行器计算指标值
4. 将计算结果保存到数据库或输出到文件

## 数据库表结构

### 1. L2Metrics

存储指标计算结果的主表。

| 字段名 | 类型 | 说明 |
| --- | --- | --- |
| ID | INT | 主键ID |
| TickerSymbol | NVARCHAR(20) | 股票代码 |
| CompanyName | NVARCHAR(100) | 公司名称 |
| ReportDate | DATE | 报告日期 |
| IndicatorNameCN | NVARCHAR(100) | 指标中文名称 |
| IndicatorNameEN | NVARCHAR(100) | 指标英文名称 |
| FormulaDesc | NVARCHAR(MAX) | 公式中文描述 |
| FormulaEN | NVARCHAR(MAX) | 公式英文描述 |
| CalculatedValue | DECIMAL(38, 6) | 计算结果 |
| LowerBound | DECIMAL(38, 6) | 下限值 |
| UpperBound | DECIMAL(38, 6) | 上限值 |
| RangeValue | NVARCHAR(100) | 范围值描述 |
| EvaluationResult | NVARCHAR(50) | 评估结果（优秀/良好/一般/较差） |
| CalculationTime | DATETIME | 计算时间 |
| Remarks | NVARCHAR(MAX) | 备注 |
| CreateTime | DATETIME | 创建时间 |

### 2. L2MetricsErrorLog

存储计算错误日志的表。

| 字段名 | 类型 | 说明 |
| --- | --- | --- |
| ID | INT | 主键ID |
| TickerSymbol | NVARCHAR(20) | 股票代码 |
| ReportDate | DATE | 报告日期 |
| IndicatorNameCN | NVARCHAR(100) | 指标中文名称 |
| IndicatorNameEN | NVARCHAR(100) | 指标英文名称 |
| FormulaDesc | NVARCHAR(MAX) | 公式中文描述 |
| FormulaEN | NVARCHAR(MAX) | 公式英文描述 |
| ErrorMessage | NVARCHAR(MAX) | 错误信息 |
| ProcessedFormula | NVARCHAR(MAX) | 处理后的公式 |
| ErrorTime | DATETIME | 错误时间 |

### 3. L2MetricsCalculationLog

存储计算日志的表。

| 字段名 | 类型 | 说明 |
| --- | --- | --- |
| ID | INT | 主键ID |
| TickerSymbol | NVARCHAR(20) | 股票代码 |
| ReportDate | DATE | 报告日期 |
| CalculationTime | DATETIME | 计算时间 |
| SuccessCount | INT | 成功计算的指标数量 |
| FailureCount | INT | 计算失败的指标数量 |
| ExecutionTime | DECIMAL(10, 3) | 执行时间（秒） |
| LogMessage | NVARCHAR(MAX) | 日志信息 |

## 常见问题

### 1. 计算结果为0或者计算失败

可能的原因：
- 数据库中缺少必要的数据
- 公式中使用的变量名与数据库中的变量名不匹配
- 公式语法错误

解决方法：
- 检查数据库中是否有足够的数据
- 检查指标名称映射文件中的公式是否正确
- 查看错误日志，了解具体错误信息

### 2. 指标定义与指标名称映射不匹配

可能的原因：
- 指标定义文件和指标名称映射文件中的指标名称不一致
- 指标名称映射文件中的公式与指标定义文件中的公式不一致

解决方法：
- 检查指标定义文件和指标名称映射文件，确保指标名称一致
- 检查指标名称映射文件中的公式，确保与指标定义文件中的公式一致

## 注意事项

1. 计算指标时，需要确保数据库中有足够的数据，包括当期、上期和上期末的数据
2. 指标名称映射文件中的公式需要与指标定义文件中的公式一致
3. 保存结果到数据库时，需要确保数据库表结构已经创建
4. 批量计算时，建议使用较小的股票列表，避免一次性计算太多股票导致内存不足
