# 财务数据分析API

## 项目概述

本API为财务数据分析网站提供后端服务，主要功能包括公司数据、财务指标和指标值的查询与管理。

## 技术栈

- Python 3.x
- Flask
- SQLAlchemy

## 目录结构

```
api/
├── config/          # 配置文件
├── controllers/     # 控制器层
├── repositories/    # 数据访问层
├── routes/          # 路由定义
├── services/        # 业务逻辑层
├── utils/           # 工具类
├── app.py           # 应用入口
└── data_access.py   # 数据库连接
```

## 安装与运行

1. 安装依赖:
```bash
poetry install
```

2. 进入虚拟环境（可选）:
```bash
poetry shell
```

3. 配置数据库连接:
修改`config/database_config.py`中的数据库连接信息

4. 启动服务:
```bash
poetry run python app.py
```

## 常用Poetry命令

- 安装依赖：`poetry install`
- 添加依赖：`poetry add 包名`
- 移除依赖：`poetry remove 包名`
- 进入虚拟环境：`poetry shell`
- 运行脚本：`poetry run python xxx.py`

## 模块说明

- **controllers**: 处理HTTP请求
- **services**: 业务逻辑实现
- **repositories**: 数据库操作
- **routes**: API路由定义

## 开发规范

1. 遵循PEP8编码规范
2. 使用Flask-RESTful风格设计API
3. 数据库操作统一通过Repository层访问