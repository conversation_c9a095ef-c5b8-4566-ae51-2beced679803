# 财务数据分析前端项目

## 项目概述

本前端项目为财务数据分析网站提供用户界面，主要功能包括：

1. 财务报表展示
   - 资产负债表(BalanceSheet)展示
   - 利润表(IncomeStatement)展示
   - 现金流量表(CashFlowStatement)展示
   - 支持多期数据对比

2. 财务指标分析
   - 关键财务指标计算与展示
   - 趋势分析与可视化图表
   - 行业对比分析
3. 可视化展示
   - 多种图表类型展示(折线图、柱状图、饼图等)
   - 交互式数据探索
   - 自定义报表生成
4. 定期报告公布时间

## 技术栈

- React
- JavaScript/ES6+
- Vite
- CSS3

## 目录结构

```
web/
├── src/          # 源代码
│   ├── App.jsx   # 主应用组件
│   ├── main.jsx  # 入口文件
│   ├── components/ # 组件目录
│   │   ├── FinancialAnalysis/ # 财务分析组件
│   │   └── FinancialReports/  # 财务报表组件
│   └── utils/    # 工具函数
├── vite.config.js # Vite配置
└── index.html    # 主页面
```

## 安装与运行

1. 安装依赖:
```bash
npm install
```

2. 开发模式:
```bash
npm run dev
```

3. 生产构建:
```bash
npm run build
```

## 开发规范

1. 遵循ESLint代码规范
2. 使用模块化开发
3. 样式使用BEM命名规范
