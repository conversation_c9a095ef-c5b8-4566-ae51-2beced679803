/**
 * 启动脚本 - 用于加载特定环境变量文件并启动应用
 *
 * 使用方法:
 * node start.js [环境文件名]
 *
 * 示例:
 * node start.js .env.development  // 使用开发环境配置启动
 * node start.js .env.production   // 使用生产环境配置启动
 * node start.js                   // 使用默认环境配置启动
 */

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

// 获取命令行参数中的环境文件名
const envFile = process.argv[2] || '.env';
const envFilePath = path.resolve(__dirname, envFile);

// 检查环境文件是否存在
if (!fs.existsSync(envFilePath)) {
  console.error(`错误: 环境文件 "${envFile}" 不存在`);
  console.log('可用的环境文件:');

  // 列出所有可用的环境文件
  fs.readdirSync(__dirname)
    .filter(file => file.startsWith('.env'))
    .forEach(file => console.log(`- ${file}`));

  process.exit(1);
}

console.log(`使用环境文件: ${envFile}`);

// 设置环境变量
const env = { ...process.env, ENV_FILE: envFile };

// 确定要运行的命令
const isWindows = process.platform === 'win32';
const npmCmd = isWindows ? 'npm.cmd' : 'npm';

// 启动应用
console.log(`启动命令: ${npmCmd} run dev (ENV_FILE=${envFile})`);
const child = spawn(npmCmd, ['run', 'dev'], {
  env: env,
  stdio: 'inherit',
  shell: true
});

child.on('error', (error) => {
  console.error(`启动失败: ${error.message}`);
  process.exit(1);
});

child.on('close', (code) => {
  if (code !== 0) {
    console.error(`进程退出，退出码: ${code}`);
  }
});
