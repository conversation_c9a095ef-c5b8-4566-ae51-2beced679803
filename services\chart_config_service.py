# chart_config_service.py
from typing import Dict, List, Any, Optional
from datetime import datetime
from .chart_config import chart_configs
from ..utils.error_handler import log_error, DatabaseError

class ChartConfigService:
    """
    图表配置服务
    统一处理所有图表配置的数据获取和处理
    """
    
    def __init__(self):
        self.chart_configs = chart_configs
        
    def get_all_required_windcodes(self) -> List[str]:
        """
        获取所有图表配置中需要的windcodes
        用于一次性查询所有需要的数据
        """
        windcodes = set()
        
        for config_key, config in self.chart_configs.items():
            if 'indicators' in config:
                for indicator in config['indicators']:
                    # 添加主指标
                    windcodes.add(indicator['key'])
                    # 添加额外指标
                    if 'extra' in indicator:
                        for extra in indicator['extra']:
                            windcodes.add(extra['key'])
        
        return list(windcodes)
    
    def get_chart_data_for_dashboard(self, report_date: str, company_ids: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        获取仪表盘所有图表数据
        一次性查询所有需要的数据，减少数据库请求
        返回结构与chart_config.py一致，每条数据包含年份信息
        根据每个图表的years配置决定查询年份范围
        """
        from ..data_access import execute_query
        
        # 获取所有需要的windcodes
        windcodes = self.get_all_required_windcodes()
        
        # 解析报告日期获取年份
        current_year = int(report_date[:4])
        
        # 收集所有需要的年份
        all_years = set()
        for config in self.chart_configs.values():
            years = config.get('years', 1)
            for i in range(years):
                all_years.add(current_year - i)
        
        # 生成对应的日期列表（假设都是年末数据，格式：YYYY-12-31）
        # 但如果当前报告日期不是12-31，则保持原有的月-日格式
        date_suffix = report_date[4:]  # 例如：-03-31
        all_dates = []
        for year in sorted(all_years, reverse=True):
            # 如果是当前年份，使用提供的report_date
            if year == current_year:
                all_dates.append(report_date)
            else:
                # 对于历史年份，使用相同的月-日
                all_dates.append(f"{year}{date_suffix}")
        
        # 查询WindMetrics数据
        sql = '''
            SELECT
                _company.CompanyId,
                _company.CompanyName,
                _metri.WindCode,
                _metri.value,
                _metri.OriValue,
                _metri.TickerSymbol,
                _metri.Date
            FROM
                WindMetrics _metri
                LEFT JOIN CompanyInfo _company ON _company.CompanyID = _metri.CompanyId
            WHERE
                1=1
        '''
        params = []
        
        # 添加日期条件
        if all_dates:
            date_placeholders = ','.join(['?' for _ in all_dates])
            sql += f" AND _metri.Date IN ({date_placeholders})"
            params.extend(all_dates)
        
        if company_ids and len(company_ids) > 0:
            placeholders = ','.join(['?' for _ in company_ids])
            sql += f" AND _metri.TickerSymbol IN ({placeholders})"
            params.extend(company_ids)
        
        windcode_placeholders = ','.join(['?' for _ in windcodes])
        sql += f" AND WindCode IN ({windcode_placeholders})"
        params.extend(windcodes)
        
        wind_metrics = execute_query(sql, params)
        
        # 查询L2Metrics数据
        l2_sql = '''
            SELECT _l2.[ID]
            ,_l2.[TickerSymbol]
            ,_l2.[CompanyName]
            ,_l2.[ReportDate]
            ,_l2Indicators.[IndicatorNameCN]
            ,_l2.[IndicatorNameEN]
            ,_l2Indicators.[FormulaDesc]
            ,_l2.[FormulaEN]
            ,_l2.[CalculatedValue]
            ,_l2.[LowerBound]
            ,_l2.[UpperBound]
            ,_l2.[RangeValue]
            ,_l2.[EvaluationResult]
            ,_l2.[CalculationTime]
            ,_l2.[Remarks]
            ,_l2.[CreateTime]
            ,_l2.[IndicatorDesc]
            ,_l2.[Formula]
            ,_l2.[TranslatedFormula]
            ,_com.CompanyID
            FROM [L2Metrics] _l2
            left join CompanyInfo _com on _l2.TickerSymbol=_com.TickerSymbol
              join L2BenchmarkIndicators _l2Indicators on _l2Indicators.IndicatorNameEN=_l2.IndicatorNameEN and _l2Indicators.Enabled=1
            WHERE 1=1
        '''
        l2_params = []
        
        # 添加日期条件，支持多年数据
        if all_dates:
            date_placeholders = ','.join(['?' for _ in all_dates])
            l2_sql += f" AND _l2.[ReportDate] IN ({date_placeholders})"
            l2_params.extend(all_dates)
        
        if company_ids and len(company_ids) > 0:
            placeholders = ','.join(['?' for _ in company_ids])
            l2_sql += f" AND _com.TickerSymbol IN ({placeholders})"
            l2_params.extend(company_ids)
        
        l2_metrics = execute_query(l2_sql, l2_params)
        
        # 格式化数据，返回与chart_config.py结构一致的数据
        formatted_data = self._format_dashboard_data(wind_metrics, l2_metrics, report_date)
        
        return formatted_data
    
    def _format_dashboard_data(self, wind_metrics: List[Dict], l2_metrics: List[Dict], report_date: str) -> Dict[str, Any]:
        """
        格式化仪表盘数据，返回与chart_config.py结构一致的数据
        优先匹配L2指标，如果没有再匹配Wind指标
        统一格式：所有数据都放在indicators数组中，每个记录包含date和year字段
        """
        # 按公司、日期组织L2指标数据
        l2_metrics_by_company_date = {}
        for metric in l2_metrics:
            ticker = metric['TickerSymbol']
            date = metric.get('ReportDate', report_date)
            
            # 处理日期格式，支持字符串和datetime对象
            if date:
                if isinstance(date, str):
                    year = date[:4]
                    date_str = date
                elif isinstance(date, datetime):
                    # 如果是datetime对象，转换为字符串
                    year = str(date.year)
                    date_str = date.strftime('%Y-%m-%d')
                else:
                    # 其他类型尝试转换为字符串
                    date_str = str(date)
                    year = date_str[:4] if len(date_str) >= 4 else None
            else:
                year = None
                date_str = report_date
            
            if ticker not in l2_metrics_by_company_date:
                l2_metrics_by_company_date[ticker] = {
                    'CompanyId': metric.get('CompanyID'),
                    'CompanyName': metric['CompanyName'],
                    'TickerSymbol': ticker,
                    'dates': {}
                }
            
            if date_str not in l2_metrics_by_company_date[ticker]['dates']:
                l2_metrics_by_company_date[ticker]['dates'][date_str] = {
                    'year': year,
                    'metrics': {}
                }
            
            # 使用IndicatorNameEN作为key，并包含额外的指标信息
            l2_metrics_by_company_date[ticker]['dates'][date_str]['metrics'][metric['IndicatorNameEN']] = {
                'value': metric['CalculatedValue'],
                'OriValue': metric['CalculatedValue'],
                # 添加额外的指标信息字段
                'IndicatorNameCN': metric.get('IndicatorNameCN'),
                'IndicatorNameEN': metric.get('IndicatorNameEN'),
                'FormulaDesc': metric.get('FormulaDesc'),
                'FormulaEN': metric.get('FormulaEN'),
                'Formula': metric.get('Formula'),
                'TranslatedFormula': metric.get('TranslatedFormula'),
                'IndicatorDesc': metric.get('IndicatorDesc'),
                'LowerBound': metric.get('LowerBound'),
                'UpperBound': metric.get('UpperBound'),
                'RangeValue': metric.get('RangeValue'),
                'EvaluationResult': metric.get('EvaluationResult')
            }
        
        # 按公司、日期和WindCode组织Wind指标数据
        wind_metrics_by_company_date = {}
        for metric in wind_metrics:
            ticker = metric['TickerSymbol']
            date = metric.get('Date', report_date)
            
            # 处理日期格式，支持字符串和datetime对象
            if date:
                if isinstance(date, str):
                    year = date[:4]
                    date_str = date
                elif isinstance(date, datetime):
                    # 如果是datetime对象，转换为字符串
                    year = str(date.year)
                    date_str = date.strftime('%Y-%m-%d')
                else:
                    # 其他类型尝试转换为字符串
                    date_str = str(date)
                    year = date_str[:4] if len(date_str) >= 4 else None
            else:
                year = None
                date_str = report_date
            
            if ticker not in wind_metrics_by_company_date:
                wind_metrics_by_company_date[ticker] = {
                    'CompanyId': metric['CompanyId'],
                    'CompanyName': metric['CompanyName'],
                    'TickerSymbol': ticker,
                    'dates': {}
                }
            
            if date_str not in wind_metrics_by_company_date[ticker]['dates']:
                wind_metrics_by_company_date[ticker]['dates'][date_str] = {
                    'year': year,
                    'metrics': {}
                }
            
            wind_metrics_by_company_date[ticker]['dates'][date_str]['metrics'][metric['WindCode']] = {
                'value': metric['value'],
                'OriValue': metric['OriValue']
            }
        
        # 按chart_config.py的结构组织数据
        result = {}
        current_year = int(report_date[:4])
        
        for chart_type, config in self.chart_configs.items():
            years_needed = config.get('years', 1)
            
            result[chart_type] = {
                'title': config['title'],
                'years': years_needed,
                'indicators': []
            }
            
            # 为每个公司创建指标数据
            for ticker in set(list(l2_metrics_by_company_date.keys()) + list(wind_metrics_by_company_date.keys())):
                # 获取公司信息，优先使用L2数据中的公司信息
                company_data = l2_metrics_by_company_date.get(ticker) or wind_metrics_by_company_date.get(ticker)
                
                # 确保company_data不为None
                if company_data is None:
                    continue
                
                # 根据years配置收集需要的年份数据
                if years_needed == 1:
                    # 单年数据：只查找当前年份
                    l2_date_info = None
                    wind_date_info = None
                    
                    # 查找L2数据
                    if ticker in l2_metrics_by_company_date:
                        for date, date_info in l2_metrics_by_company_date[ticker]['dates'].items():
                            if date_info['year'] == str(current_year):
                                l2_date_info = date_info
                                break
                    
                    # 查找Wind数据
                    if ticker in wind_metrics_by_company_date:
                        for date, date_info in wind_metrics_by_company_date[ticker]['dates'].items():
                            if date_info['year'] == str(current_year):
                                wind_date_info = date_info
                                break
                    
                    # 处理指标数据，优先使用L2数据
                    for indicator in config['indicators']:
                        # 确定年份
                        year_to_use = str(current_year)
                        if l2_date_info and l2_date_info.get('year'):
                            year_to_use = l2_date_info['year']
                        elif wind_date_info and wind_date_info.get('year'):
                            year_to_use = wind_date_info['year']
                        
                        indicator_data = self._create_indicator_data_with_fallback(
                            indicator, l2_date_info, wind_date_info, year_to_use
                        )
                        # 添加公司信息到指标数据中
                        indicator_data.update({
                            'CompanyId': company_data['CompanyId'],
                            'CompanyName': company_data['CompanyName'],
                            'TickerSymbol': ticker
                        })
                        result[chart_type]['indicators'].append(indicator_data)
                else:
                    # 多年数据：按年份正序收集（从早到晚）
                    for i in range(years_needed - 1, -1, -1):  # 从 years_needed-1 到 0，倒序循环
                        year = str(current_year - i)
                        l2_date_info = None
                        wind_date_info = None
                        
                        # 查找L2数据
                        if ticker in l2_metrics_by_company_date:
                            for date, date_info in l2_metrics_by_company_date[ticker]['dates'].items():
                                if date_info['year'] == year:
                                    l2_date_info = date_info
                                    break
                        
                        # 查找Wind数据
                        if ticker in wind_metrics_by_company_date:
                            for date, date_info in wind_metrics_by_company_date[ticker]['dates'].items():
                                if date_info['year'] == year:
                                    wind_date_info = date_info
                                    break
                        
                        # 处理指标数据，优先使用L2数据
                        for indicator in config['indicators']:
                            indicator_data = self._create_indicator_data_with_fallback(
                                indicator, l2_date_info, wind_date_info, year
                            )
                            # 添加公司信息到指标数据中
                            indicator_data.update({
                                'CompanyId': company_data['CompanyId'],
                                'CompanyName': company_data['CompanyName'],
                                'TickerSymbol': ticker
                            })
                            result[chart_type]['indicators'].append(indicator_data)
        
        # 对每个图表的指标数据进行排序
        for chart_type in result:
            result[chart_type]['indicators'] = self._sort_indicators_by_year_and_company(
                result[chart_type]['indicators']
            )
        
        return result
    
    def _create_indicator_data_with_fallback(self, indicator: Dict, l2_date_info: Optional[Dict], wind_date_info: Optional[Dict], year: str) -> Dict:
        """
        创建指标数据，优先使用L2指标，如果没有再使用Wind指标
        """
        # 获取原始数据 - 优先使用L2数据，如果没有再使用Wind数据
        windcode = indicator['key']
        
        # 先尝试从L2数据获取
        raw_value = None
        raw_ori_value = None
        l2_extra_fields = {}  # 存储L2指标的额外字段
        
        if l2_date_info and l2_date_info.get('metrics'):
            # 创建大小写不敏感的查找字典
            l2_metrics_lower = {k.lower(): v for k, v in l2_date_info['metrics'].items()}
            windcode_lower = windcode.lower()
            
            l2_metric_data = l2_metrics_lower.get(windcode_lower, {})
            raw_value = l2_metric_data.get('value')
            raw_ori_value = l2_metric_data.get('OriValue')
            
            # 提取L2指标的额外字段
            if l2_metric_data:
                l2_extra_fields = {
                    'IndicatorNameCN': l2_metric_data.get('IndicatorNameCN'),
                    'IndicatorNameEN': l2_metric_data.get('IndicatorNameEN'),
                    'FormulaDesc': l2_metric_data.get('FormulaDesc'),
                    'FormulaEN': l2_metric_data.get('FormulaEN'),
                    'Formula': l2_metric_data.get('Formula'),
                    'TranslatedFormula': l2_metric_data.get('TranslatedFormula'),
                    'IndicatorDesc': l2_metric_data.get('IndicatorDesc'),
                    'LowerBound': l2_metric_data.get('LowerBound'),
                    'UpperBound': l2_metric_data.get('UpperBound'),
                    'RangeValue': l2_metric_data.get('RangeValue'),
                    'EvaluationResult': l2_metric_data.get('EvaluationResult')
                }
        
        # 如果L2数据没有，再尝试从Wind数据获取
        if raw_value is None and wind_date_info and wind_date_info.get('metrics'):
            # 创建大小写不敏感的查找字典
            wind_metrics_lower = {k.lower(): v for k, v in wind_date_info['metrics'].items()}
            windcode_lower = windcode.lower()
            
            wind_metric_data = wind_metrics_lower.get(windcode_lower, {})
            raw_value = wind_metric_data.get('value')
            raw_ori_value = wind_metric_data.get('OriValue')
        
        # 根据单位换算数据
        unit = indicator.get('unit', '')
        converted_value = self._convert_value_by_unit(raw_value, unit)
        converted_ori_value = self._convert_value_by_unit(raw_ori_value, unit)
        
        # 确定日期
        date_to_use = ''
        if l2_date_info and l2_date_info.get('year'):
            date_to_use = l2_date_info['year']
        elif wind_date_info and wind_date_info.get('year'):
            date_to_use = wind_date_info['year']
        
        indicator_data = {
            'key': indicator['key'],
            'name': indicator['name'],
            'unit': unit,
            'desc': indicator.get('desc', ''),
            'value': converted_value,
            'OriValue': converted_ori_value,
            'displayValue': self._format_display_value(converted_value, unit),  # 添加格式化显示值
            'Date': date_to_use,
            'Year': year,
            # 添加L2指标的额外字段
            **l2_extra_fields
        }
        
        # 处理额外指标
        if 'extra' in indicator:
            indicator_data['extra'] = []
            for extra in indicator['extra']:
                extra_windcode = extra['key']
                extra_raw_value = None
                extra_raw_ori_value = None
                extra_l2_fields = {}  # 存储额外指标的L2字段
                
                # 先尝试从L2数据获取
                if l2_date_info and l2_date_info.get('metrics'):
                    l2_metrics_lower = {k.lower(): v for k, v in l2_date_info['metrics'].items()}
                    extra_windcode_lower = extra_windcode.lower()
                    extra_l2_metric_data = l2_metrics_lower.get(extra_windcode_lower, {})
                    extra_raw_value = extra_l2_metric_data.get('value')
                    extra_raw_ori_value = extra_l2_metric_data.get('OriValue')
                    
                    # 提取额外指标的L2字段
                    if extra_l2_metric_data:
                        extra_l2_fields = {
                            'IndicatorNameCN': extra_l2_metric_data.get('IndicatorNameCN'),
                            'IndicatorNameEN': extra_l2_metric_data.get('IndicatorNameEN'),
                            'FormulaDesc': extra_l2_metric_data.get('FormulaDesc'),
                            'FormulaEN': extra_l2_metric_data.get('FormulaEN'),
                            'Formula': extra_l2_metric_data.get('Formula'),
                            'TranslatedFormula': extra_l2_metric_data.get('TranslatedFormula'),
                            'IndicatorDesc': extra_l2_metric_data.get('IndicatorDesc'),
                            'LowerBound': extra_l2_metric_data.get('LowerBound'),
                            'UpperBound': extra_l2_metric_data.get('UpperBound'),
                            'RangeValue': extra_l2_metric_data.get('RangeValue'),
                            'EvaluationResult': extra_l2_metric_data.get('EvaluationResult')
                        }
                
                # 如果L2数据没有，再尝试从Wind数据获取
                if extra_raw_value is None and wind_date_info and wind_date_info.get('metrics'):
                    wind_metrics_lower = {k.lower(): v for k, v in wind_date_info['metrics'].items()}
                    extra_windcode_lower = extra_windcode.lower()
                    extra_wind_metric_data = wind_metrics_lower.get(extra_windcode_lower, {})
                    extra_raw_value = extra_wind_metric_data.get('value')
                    extra_raw_ori_value = extra_wind_metric_data.get('OriValue')
                
                extra_unit = extra.get('unit', '')
                extra_converted_value = self._convert_value_by_unit(extra_raw_value, extra_unit)
                extra_converted_ori_value = self._convert_value_by_unit(extra_raw_ori_value, extra_unit)
                
                extra_data = {
                    'key': extra['key'],
                    'name': extra['name'],
                    'unit': extra_unit,
                    'desc': extra.get('desc', ''),
                    'value': extra_converted_value,
                    'OriValue': extra_converted_ori_value,
                    'displayValue': self._format_display_value(extra_converted_value, extra_unit),  # 添加格式化显示值
                    'Date': date_to_use,
                    'Year': year,
                    # 添加额外指标的L2字段
                    **extra_l2_fields
                }
                indicator_data['extra'].append(extra_data)
        
        return indicator_data
    
    def _create_indicator_data(self, indicator: Dict, metrics: Dict, date: Optional[str], year: Optional[str]) -> Dict:
        """
        创建指标数据，根据单位换算数据
        """
        # 获取原始数据 - 使用indicator['key']作为WindCode来查找数据，忽略大小写
        windcode = indicator['key']
        
        # 创建大小写不敏感的查找字典
        metrics_lower = {k.lower(): v for k, v in metrics.items()}
        windcode_lower = windcode.lower()
        
        metric_data = metrics_lower.get(windcode_lower, {})
        raw_value = metric_data.get('value')
        raw_ori_value = metric_data.get('OriValue')
        
        # 根据单位换算数据
        unit = indicator.get('unit', '')
        converted_value = self._convert_value_by_unit(raw_value, unit)
        converted_ori_value = self._convert_value_by_unit(raw_ori_value, unit)
        
        indicator_data = {
            'key': indicator['key'],
            'name': indicator['name'],
            'unit': unit,
            'desc': indicator.get('desc', ''),
            'value': converted_value,
            'OriValue': converted_ori_value,
            'displayValue': self._format_display_value(converted_value, unit),  # 添加格式化显示值
            'Date': date or '',
            'Year': year or ''
        }
        
        # 处理额外指标
        if 'extra' in indicator:
            indicator_data['extra'] = []
            for extra in indicator['extra']:
                extra_windcode = extra['key']
                extra_windcode_lower = extra_windcode.lower()
                extra_metric_data = metrics_lower.get(extra_windcode_lower, {})
                extra_raw_value = extra_metric_data.get('value')
                extra_raw_ori_value = extra_metric_data.get('OriValue')
                extra_unit = extra.get('unit', '')
                extra_converted_value = self._convert_value_by_unit(extra_raw_value, extra_unit)
                extra_converted_ori_value = self._convert_value_by_unit(extra_raw_ori_value, extra_unit)
                
                extra_data = {
                    'key': extra['key'],
                    'name': extra['name'],
                    'unit': extra_unit,
                    'desc': extra.get('desc', ''),
                    'value': extra_converted_value,
                    'OriValue': extra_converted_ori_value,
                    'displayValue': self._format_display_value(extra_converted_value, extra_unit),  # 添加格式化显示值
                    'Date': date or '',
                    'Year': year or ''
                }
                indicator_data['extra'].append(extra_data)
        
        return indicator_data
    
    def _format_display_value(self, value, unit: str):
        """
        格式化显示值，包含单位信息
        """
        if value is None:
            return None
        
        try:
            # 尝试转换为数字进行格式化
            numeric_value = float(value)
            
            # 根据单位和数值大小选择合适的格式
            if unit == '%':
                # 百分比保留1位小数
                return f"{numeric_value:.1f}%"
            elif unit in ['万元', '亿元']:
                # 金额单位保留2位小数
                return f"{numeric_value:.2f}{unit}"
            elif unit in ['次', '天', '人']:
                # 整数单位不显示小数
                return f"{int(numeric_value)}{unit}"
            elif unit:
                # 其他有单位的情况保留2位小数
                return f"{numeric_value:.2f}{unit}"
            else:
                # 无单位情况保留2位小数
                return f"{numeric_value:.2f}"
                
        except (ValueError, TypeError):
            # 如果无法转换为数字，返回原值加单位
            return f"{value}{unit}" if unit else str(value)
    
    def _convert_value_by_unit(self, value, unit: str):
        """
        根据单位换算数据值，保留2位小数
        """
        if value is None:
            return None
        
        try:
            # 尝试转换为数字
            numeric_value = float(value)
            
            # 根据单位进行换算
            if unit == '万元':
                # 假设原始数据是元，换算为万元
                return round(numeric_value / 10000, 2)
            elif unit == '亿元':
                # 假设原始数据是元，换算为亿元
                return round(numeric_value / 100000000, 2)
            elif unit == '%':
                # 假设原始数据是小数格式（如0.15），换算为百分比格式（如15）
                return round(numeric_value * 100, 2)
            elif unit in ['次', '天', '人']:
                # 次数、天数、人数保留整数
                return round(numeric_value, 0)
            else:
                # 其他单位保留2位小数
                return round(numeric_value, 2)
                
        except (ValueError, TypeError) as e:
            # 如果无法转换为数字，记录错误并返回原值
            log_error(e, "数值转换失败", {"value": value, "unit": unit})
            return value
    
    def _sort_indicators_by_year_and_company(self, indicators: List[Dict]) -> List[Dict]:
        """
        对指标数据按年份和公司名称进行排序
        排序规则：年份正序，公司名称（如果有电科数字，电科数字排在第一位）
        """
        def get_sort_key(indicator):
            # 获取年份，确保是数字格式用于排序
            year = indicator.get('Year', '')
            try:
                year_num = int(year) if year else 0
            except (ValueError, TypeError):
                year_num = 0
            
            # 获取公司名称
            company_name = indicator.get('CompanyName', '')
            
            # 如果公司名称包含"电科数字"，给予最高优先级
            if '电科数字' in company_name:
                company_priority = 0
            else:
                company_priority = 1
            
            # 返回排序键：(公司优先级, 年份, 公司名称)
            # 公司优先级：0表示电科数字（最高优先级），1表示其他公司
            # 年份：正序排列
            # 公司名称：按字母顺序排列
            return (company_priority, year_num, company_name)
        
        # 对指标列表进行排序
        sorted_indicators = sorted(indicators, key=get_sort_key)
        
        return sorted_indicators 