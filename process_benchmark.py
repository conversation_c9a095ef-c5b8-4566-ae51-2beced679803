import openpyxl
from openpyxl import load_workbook
import pandas as pd

def read_excel_sheet(file_path, sheet_name):
    """读取Excel指定sheet页内容"""
    wb = load_workbook(file_path)
    sheet = wb[sheet_name]
    data = []
    
    # 读取表头(第二行)
    headers = [cell.value for cell in sheet[2]]
    
    # 读取数据行(从第三行开始)
    for row in sheet.iter_rows(min_row=3, values_only=True):
        data.append(dict(zip(headers, row)))
    
    return pd.DataFrame(data)

def translate_indicators(df):
    """智能翻译指标名称"""
    # 使用大模型进行指标名称翻译
    df['IndicatorNameCN'] = df['对标指标'].apply(lambda x: translate_with_llm(x))
    df['IndicatorNameEN'] = df['对标指标'].apply(lambda x: translate_to_english(x)) 
    return df

def translate_with_llm(text):
    """调用大模型翻译中文指标名称"""
    # 这里可以添加实际的模型调用代码
    # 示例翻译逻辑
    translations = {
        "现金分红总额": "Total Cash Dividends",
        "研发费用": "R&D Expenses",
        "营业收入": "Operating Revenue"
    }
    return translations.get(text, f"[已翻译]{text}")

def translate_to_english(text):
    """翻译为英文简称"""
    translations = {
        "现金分红总额": "CashDiv",
        "研发费用": "R&D",
        "营业收入": "Revenue"
    }
    return translations.get(text, "N/A")

def generate_sql_script(df, table_name='BenchmarkIndicators'):
    """生成SQL Server建表脚本"""
    sql = f"""
-- 创建对标指标表
CREATE TABLE {table_name} (
    NO INT PRIMARY KEY IDENTITY(1,1),
    IndicatorNameCN NVARCHAR(200) NOT NULL,
    IndicatorNameEN NVARCHAR(200),
    IndicatorDesc NVARCHAR(500),
    Formula NVARCHAR(500),
    FormulaDesc NVARCHAR(500),
    CreateTime DATETIME DEFAULT GETDATE(),
    LowerBound DECIMAL(18,4),
    UpperBound DECIMAL(18,4),
    RangeValue NVARCHAR(200)
)
GO
"""

    # 添加表注释
    sql += f"""
EXEC sp_addextendedproperty 
    'MS_Description', '对标指标主表', 
    'SCHEMA', 'dbo', 
    'TABLE', '{table_name}'
GO
"""

    # 添加字段注释
    comments = {
        'NO': '序号',
        'IndicatorNameCN': '指标中文名称',
        'IndicatorNameEN': '指标英文名称',
        'IndicatorDesc': '指标解释',
        'Formula': '计算公式',
        'FormulaDesc': '公式解释',
        'CreateTime': '创建时间',
        'LowerBound': '异常标准-低于',
        'UpperBound': '异常标准-高于',
        'RangeValue': '异常标准-区间'
    }

    for col, desc in comments.items():
        sql += f"""
EXEC sp_addextendedproperty 
    'MS_Description', '{desc}', 
    'SCHEMA', 'dbo', 
    'TABLE', '{table_name}', 
    'COLUMN', '{col}'
GO
"""
    return sql

def convert_formulas(df):
    """转换公式为Python可执行格式并处理上年数据引用"""
    # 转换数学符号
    df['PythonFormula'] = df['公式'].str.replace('×', '*').str.replace('÷', '/')
    
    # 处理上年数据引用
    for idx, row in df.iterrows():
        if pd.notna(row.get('FormulaConversion')):
            conversions = row['FormulaConversion'].split(';')
            for conv in conversions:
                if '=' in conv:
                    old, new = conv.split('=')
                    df.at[idx, 'PythonFormula'] = df.at[idx, 'PythonFormula'].replace(old.strip(), new.strip())
    
    df['FormulaDesc'] = df['指标解释']  # 使用指标解释作为公式说明
    return df

if __name__ == '__main__':
    # 读取Excel数据
    excel_path = 'docs/对标公司及对标指标列示v1.xlsx'
    df = read_excel_sheet(excel_path, '对标指标')
    
    # 翻译指标名称
    df = translate_indicators(df)
    
    # 转换公式和处理异常标准
    df = convert_formulas(df)
    df['LowerBound'] = None
    df['UpperBound'] = None 
    df['RangeValue'] = df['设定异常标准']
    
    # 生成SQL脚本
    sql_script = generate_sql_script(df)
    with open('benchmark_indicators.sql', 'w', encoding='utf-8') as f:
        f.write(sql_script)
    
    # 生成INSERT语句文件
    with open('benchmark_inserts.sql', 'w', encoding='utf-8') as f:
        for _, row in df.iterrows():
            insert_sql = f"""
INSERT INTO BenchmarkIndicators (
    IndicatorNameCN, IndicatorNameEN, IndicatorDesc,
    Formula, FormulaDesc, LowerBound, UpperBound, RangeValue
) VALUES (
    '{row['IndicatorNameCN']}', '{row['IndicatorNameEN']}',
    '{row['指标解释']}', '{row['公式']}', '{row['FormulaDesc']}',
    NULL, NULL, '{row['RangeValue']}'
)
GO
"""
            f.write(insert_sql)
    
    # 保存处理后的数据
    df.to_excel('processed_benchmark.xlsx', index=False)
    
    print("处理完成！已生成以下文件：")
    print("- benchmark_indicators.sql (建表脚本)")
    print("- benchmark_inserts.sql (INSERT语句)")
    print("- processed_benchmark.xlsx (处理后的Excel数据)")
