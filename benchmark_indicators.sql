
-- 创建对标指标表
CREATE TABLE BenchmarkIndicators (
    NO INT PRIMARY KEY IDENTITY(1,1),
    IndicatorNameCN NVARCHAR(200) NOT NULL,
    IndicatorNameEN NVARCHAR(200),
    IndicatorDesc NVARCHAR(500),
    Formula NVARCHAR(500),
    FormulaDesc NVARCHAR(500),
    CreateTime DATETIME DEFAULT GETDATE(),
    LowerBound DECIMAL(18,4),
    UpperBound DECIMAL(18,4),
    RangeValue NVARCHAR(200)
)
GO

EXEC sp_addextendedproperty 
    'MS_Description', '对标指标主表', 
    'SCHEMA', 'dbo', 
    'TABLE', 'BenchmarkIndicators'
GO

EXEC sp_addextendedproperty 
    'MS_Description', '序号', 
    'SCHEMA', 'dbo', 
    'TABLE', 'BenchmarkIndicators', 
    'COLUMN', 'NO'
GO

EXEC sp_addextendedproperty 
    'MS_Description', '指标中文名称', 
    'SCHEMA', 'dbo', 
    'TABLE', 'BenchmarkIndicators', 
    'COLUMN', 'IndicatorNameCN'
GO

EXEC sp_addextendedproperty 
    'MS_Description', '指标英文名称', 
    'SCHEMA', 'dbo', 
    'TABLE', 'BenchmarkIndicators', 
    'COLUMN', 'IndicatorNameEN'
GO

EXEC sp_addextendedproperty 
    'MS_Description', '指标解释', 
    'SCHEMA', 'dbo', 
    'TABLE', 'BenchmarkIndicators', 
    'COLUMN', 'IndicatorDesc'
GO

EXEC sp_addextendedproperty 
    'MS_Description', '计算公式', 
    'SCHEMA', 'dbo', 
    'TABLE', 'BenchmarkIndicators', 
    'COLUMN', 'Formula'
GO

EXEC sp_addextendedproperty 
    'MS_Description', '公式解释', 
    'SCHEMA', 'dbo', 
    'TABLE', 'BenchmarkIndicators', 
    'COLUMN', 'FormulaDesc'
GO

EXEC sp_addextendedproperty 
    'MS_Description', '创建时间', 
    'SCHEMA', 'dbo', 
    'TABLE', 'BenchmarkIndicators', 
    'COLUMN', 'CreateTime'
GO

EXEC sp_addextendedproperty 
    'MS_Description', '异常标准-低于', 
    'SCHEMA', 'dbo', 
    'TABLE', 'BenchmarkIndicators', 
    'COLUMN', 'LowerBound'
GO

EXEC sp_addextendedproperty 
    'MS_Description', '异常标准-高于', 
    'SCHEMA', 'dbo', 
    'TABLE', 'BenchmarkIndicators', 
    'COLUMN', 'UpperBound'
GO

EXEC sp_addextendedproperty 
    'MS_Description', '异常标准-区间', 
    'SCHEMA', 'dbo', 
    'TABLE', 'BenchmarkIndicators', 
    'COLUMN', 'RangeValue'
GO
