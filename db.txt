-- 数据库表结构定义

-- StockList表
CREATE TABLE StockList (
    StockCode VARCHAR(20) NOT NULL PRIMARY KEY, -- 股票代码
    StockName VARCHAR(100) NOT NULL, -- 股票名称
    Industry VARCHAR(100), -- 行业
    MarketCap DECIMAL(18,2) -- 市值
);

-- MetricResult表
CREATE TABLE MetricResult (
    StockCode VARCHAR(20) NOT NULL, -- 股票代码
    Date DATE NOT NULL, -- 日期
    MetricName VARCHAR(100) NOT NULL, -- 指标名称
    MetricValue DECIMAL(18,6), -- 指标值
    PRIMARY KEY (StockCode, Date, MetricName)
);

-- MetricDefinition表
CREATE TABLE MetricDefinition (
    MetricID VARCHAR(50) NOT NULL PRIMARY KEY, -- 指标ID
    MetricName VARCHAR(100) NOT NULL, -- 指标名称
    Formula VARCHAR(500), -- 公式
    Description VARCHAR(500) -- 描述
);

-- ExecutionLog表
CREATE TABLE ExecutionLog (
    LogID INT NOT NULL PRIMARY KEY, -- 日志ID
    StockCode VARCHAR(20) NOT NULL, -- 股票代码
    Date DATE NOT NULL, -- 日期
    Status VARCHAR(20) NOT NULL, -- 状态
    ErrorMessage VARCHAR(500), -- 错误信息
    ExecutionTime DATECREATED -- 执行时间
);

-- Indexes
CREATE INDEX idx_StockList_StockCode ON StockList(StockCode);
CREATE INDEX idx_MetricResult_StockCode_Date ON MetricResult(StockCode, Date);
CREATE INDEX idx_MetricDefinition_MetricID ON MetricDefinition(MetricID);
CREATE INDEX idx_ExecutionLog_StockCode_Date ON ExecutionLog(StockCode, Date);
