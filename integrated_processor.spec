# -*- mode: python ; coding: utf-8 -*-


a = Analysis(
    ['integrated_processor.py'],
    pathex=[],
    binaries=[],
    datas=[('database', 'database'), ('logging_config.py', '.'), ('import_wind_data.py', '.'), ('metrics_calculator.py', '.'), ('config.py', '.')],
    hiddenimports=['import_wind_data', 'metrics_calculator', 'pandas', 'numpy', 'pyodbc', 'WindPy', 'decimal', 'argparse', 'logging', 'datetime', 'uuid', 'math', 'concurrent.futures', 'threading', 'queue', 'json', 'time', 'os', 'sys', 'shutil', 'subprocess'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='integrated_processor',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
