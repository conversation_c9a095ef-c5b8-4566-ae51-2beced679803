from decimal import Decimal, InvalidOperation
import pandas as pd
from WindPy import w
import uuid
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor
from database.db_handler import DatabaseHandler
from logging_config import setup_logging
import os

logger = setup_logging()


def supplement_metrics():
    # 检查执行记录文件
    # record_file = 'execution_record.txt'
    # if os.path.exists(record_file):
    #     with open(record_file, 'r') as f:
    #         last_executed = f.read().strip()
    #         if last_executed == 'completed':
    #             logger.info('数据已补充完成，无需重复执行。')
    #             return
    db = DatabaseHandler()
    w.start()
    
    # 查询20250319之后创建的指标
    select_new_metrics_query = "SELECT Name, WindCode FROM MetricsDefinition WHERE [CreateDate]>'2025-07-24'"
    try:
        with db as db:
            cursor = db.connection.cursor()
            cursor.execute(select_new_metrics_query)
            new_metrics = cursor.fetchall()
            logger.info(f"成功获取 {len(new_metrics)} 条20250319之后创建的指标定义")
    except Exception as e:
        logger.error(f"获取20250319之后创建的指标定义失败: {str(e)}", exc_info=True)
        return
    
    # 查询api日志表
    select_log_query = "SELECT TickerSymbol, RptDate FROM WindApiCallLog"
    try:
        with db as db:
            cursor = db.connection.cursor()
            cursor.execute(select_log_query)
            log_records = cursor.fetchall()
    except Exception as e:
        logger.error(f"查询api日志表失败: {str(e)}", exc_info=True)
        return
    
    indicator_codes = [metric[1] for metric in new_metrics]
    fields_str = ",".join(indicator_codes)
    
    def check_company_date_execution(company_symbol, date_obj):
        record_file = 'company_date_execution_record.txt'
        if os.path.exists(record_file):
            with open(record_file, 'r') as f:
                lines = f.readlines()
                for line in lines:
                    symbol, date_str = line.strip().split(',')
                    if symbol == company_symbol and date_str == date_obj.strftime('%Y-%m-%d'):
                        return True
        return False


    # 查询股票代码对应的公司信息
    select_company_query = "SELECT TickerSymbol, CompanyId FROM CompanyINfo"
    try:
        with db as db:
            cursor = db.connection.cursor()
            cursor.execute(select_company_query)
            company_records = cursor.fetchall()
            company_dict = {record[0]: record[1] for record in company_records}
            logger.info(f"成功获取 {len(company_dict)} 条股票代码对应的公司信息")
    except Exception as e:
        logger.error(f"获取股票代码对应的公司信息失败: {str(e)}", exc_info=True)
        return
    for symbol, date_obj in log_records:
        try:
            if check_company_date_execution(symbol, date_obj):
                logger.info(f"{symbol} 在 {date_obj.strftime('%Y-%m-%d')} 已经执行过，跳过。")
                continue
            rpt_date = date_obj.strftime("%Y%m%d")
            # 查询指标日期1的数据
            wind_data = get_wind_data(symbol, fields_str, rpt_date, 1)
            
            
            
            if wind_data:
                # 查询指标日期3的数据（同比数据）
                yoy_data = get_yoy_data(symbol, fields_str, rpt_date) or {}
                with DatabaseHandler() as local_db:
                    cursor = local_db.connection.cursor()
                    insert_metrics_data(cursor, wind_data, yoy_data, date_obj, symbol, company_dict)
                    local_db.connection.commit()
            else:
                # 当数据全空时，为每个指标插入空值记录
                empty_data = {code: None for code in fields_str.split(',')}
                with DatabaseHandler() as local_db:
                    cursor = local_db.connection.cursor()
                    insert_metrics_data(cursor, empty_data, {}, date_obj, symbol, company_dict)
                    local_db.connection.commit()
            
            # 记录执行完成状态
            # with open(record_file, 'w') as f:
            #     f.write('completed')
            logger.info('数据补充完成。')
        except Exception as e:
            logger.error(f'执行过程中出现错误: {str(e)}', exc_info=True)
            # 记录执行中断状态
            # with open(record_file, 'w') as f:
            #     f.write('interrupted')
    # 原代码此处缩进错误，将下面的函数定义代码恢复到正确的缩进

def get_yoy_data( symbol, fields, rpt_date):
        """获取同比数据"""

        last_year = str(int(rpt_date[:4]) - 1) + rpt_date[4:]
        return get_wind_data(symbol, fields, last_year, 3) or {}


def get_wind_data(TickerSymbol, fields_str, rpt_date, rpt_type):
    
    result = w.wss(
        TickerSymbol,
        fields_str,
        f"unit=1;rptDate={rpt_date};rptType={rpt_type};tradeDate={rpt_date};year={rpt_date[:4]}"
    )
    if result.ErrorCode != 0:
        print(f'error code: {result.ErrorCode}')
        return None
    
    data = {}
    for i, field in enumerate(result.Fields):
        value = result.Data[i][0]
        if pd.isna(value):
            value = None
        data[field] = value
    
    if all(v is None for v in data.values()):
        logger.warning(f"全空数据告警: {TickerSymbol} {rpt_date} 所有指标值均为空")
        return None
    return data


def insert_metrics_data(cursor, wind_data, yoy_data, date_obj, symbol, company_dict):
    invalid_rows = []
    valid_data = []
    
    for code in wind_data.keys():
        raw_value = wind_data.get(code)
        value_dec, is_value_valid = safe_convert_decimal(raw_value, (18, 6))
        
        raw_yoy = yoy_data.get(code)
        yoy_dec, is_yoy_valid = safe_convert_decimal(raw_yoy, (18, 6))
        
        if not all([is_value_valid, is_yoy_valid]):
            invalid_rows.append({
                "wind_code": code,
                "value": value_dec,
                "orivalue": raw_value,
                "yoy_value": yoy_dec,
                "reason": f"Value valid: {is_value_valid}, YoY valid: {is_yoy_valid}"
            })
        
        ori_value_str = convert_large_number(raw_value)
        # 在插入数据时使用 company_dict 获取 CompanyId
        valid_data.append((
            str(uuid.uuid4()),
            code,
            value_dec,
            date_obj,
            yoy_dec,
            datetime.now(),
            symbol,
            ori_value_str,
            company_dict.get(symbol)
        ))
    
    if invalid_rows:
        logger.warning("发现 %d 条无效数据，样例：", len(invalid_rows))
        for row in invalid_rows[:3]:
            logger.warning("WindCode=%s | Value=%s | YoYValue=%s | 原因=%s",
                           row['wind_code'], row['value'], row['yoy_value'], row['reason'])
    
    if valid_data:
        cursor.executemany("""
            INSERT INTO WindMetrics 
            (ID, WindCode, Value, Date, YoYValue, CreateDate, TickerSymbol, OriValue, CompanyId)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, valid_data)
        logger.info("成功插入 %d 条有效数据", len(valid_data))
    else:
        logger.warning("没有有效数据需要插入")


def safe_convert_decimal(value, precision=(18, 6)):
    if value is None:
        return None, True
    
    try:
        dec_value = Decimal(str(value))
    except (TypeError, InvalidOperation):
        return None, False
    return dec_value, True


def convert_large_number(value):
    if value is None:
        return None
    try:
        if isinstance(value, str) and 'e' in value.lower():
            d = Decimal(value.lower().replace('e', 'E'))
            return format(d, 'f').rstrip('0').rstrip('.')
        elif isinstance(value, (float, int)):
            return format(Decimal(str(value)), 'f').rstrip('0').rstrip('.')
        else:
            return str(value)
    except:
        return str(value)[:500]


if __name__ == "__main__":
    supplement_metrics()