import { useState, useEffect } from 'react';
import { generateReportDate } from '../utils/generateReportDate';

export function useCompanyData(tempSelectedYear, tempSelectedQuarter, quarters) {
  const [companies, setCompanies] = useState([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (!tempSelectedYear || !tempSelectedQuarter) return;
    const reportDate = generateReportDate(tempSelectedYear, tempSelectedQuarter, quarters);
    setLoading(true);
    fetch(`./api/companies?table=L2Metrics&reportDate=${reportDate}`)
      .then(res => res.json())
      .then(data => {
        data.forEach(c => { c.ticker_symbol = c.id; });
        setCompanies(data);
      })
      .finally(() => setLoading(false));
  }, [tempSelectedYear, tempSelectedQuarter, quarters]);

  return { companies, loading };
} 