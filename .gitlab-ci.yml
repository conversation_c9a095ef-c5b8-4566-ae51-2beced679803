# ------------------------------
# 公共变量与模板定义
# ------------------------------
stages:
  - build
  - trigger_gitops

variables: 
  APP_NAME: "financialcrossanalyzerapi"
  PROJECT_PATH: "fms/$APP_NAME"
  IMAGE_NAME: "$DOCKER_REGISTRY/$PROJECT_PATH"  
  IMAGE_TAG: "$CI_PIPELINE_ID"
  ENV: "dev"  # 默认 dev 环境
  RELEASE_TAG: "$CI_PIPELINE_ID"
  VERSION: ""
  IS_VERSION_TAG: "false"

# 公共模板：变量解析逻辑（通过锚点复用）
.before_script_vars: &base_before_script_vars
  before_script: &base_before_script
    - |
      # 解析标签生成版本变量（核心逻辑）
      TAG_NAME="${CI_COMMIT_TAG:-$CI_COMMIT_REF_NAME}"  # 标签或分支名

      case "$TAG_NAME" in
        release-*)
          VERSION="${TAG_NAME#release-}"       # 提取版本号（如 release-1.0 → 1.0）
          RELEASE_TAG="release-$VERSION"       # 版本标签（如 release-1.0）
          IS_VERSION_TAG="true"                # 标记为版本标签
          ENV="prod"                           # 生产环境
          ;;
        test-*)
          VERSION="${TAG_NAME#test-}"          # 提取版本号（如 test-1.0 → 1.0）
          RELEASE_TAG="test-$VERSION"          # 测试标签（如 test-1.0）
          IS_VERSION_TAG="true"                # 标记为版本标签
          ENV="test"                           # 测试环境
          ;;
        dev-*)
          VERSION="${TAG_NAME#dev-}"           # 提取版本号（如 dev-1.0 → 1.0）
          RELEASE_TAG="dev-$VERSION"           # 开发标签（如 dev-1.0）
          IS_VERSION_TAG="true"                # 标记为版本标签
          ENV="dev"                            # 开发环境
          ;;
      esac 

    # 输出调试信息（验证变量是否正确解析）
    - echo "🔍 标签解析结果："
    - echo "   TAG_NAME=$TAG_NAME"
    - echo "   VERSION=$VERSION"
    - echo "   RELEASE_TAG=$RELEASE_TAG"
    - echo "   IS_VERSION_TAG=$IS_VERSION_TAG"
    - echo "   ENV=$ENV"

# ------------------------------
# 构建并推送镜像作业
# ------------------------------
build-and-push:
  stage: build
  tags:
    - internal  # 使用内部 Runner（需提前配置）
  image:
    name: harbor.shecc.com/tools/kaniko-excutor:debug  # Kaniko 执行器（带调试模式）
    entrypoint: [""]
  # 继承公共模板并扩展 before_script
  extends: 
    - .before_script_vars
  before_script: 
    # 1. 先执行公共变量解析逻辑（通过锚点引用）
    - *base_before_script
    # 2. 自定义 Kaniko 认证配置（追加到公共逻辑后）
    - |
      echo "🔐 配置 Docker 认证..."
      AUTH_STRING=$(echo -n "$CI_REGISTRY_USER:$CI_REGISTRY_PASSWORD" | base64)  # 生成 Base64 认证串
      mkdir -p /kaniko/.docker  # 创建 Docker 配置目录
      cat <<EOF > /kaniko/.docker/config.json
      {
        "auths": {
          "$DOCKER_REGISTRY": {
            "auth": "$AUTH_STRING"
          }
        }
      }
      EOF

  script: |
    echo "🚀 开始构建镜像：$IMAGE_NAME"
    # 使用 Kaniko 构建并推送镜像（支持多标签）

    DESTINATIONS="--destination $IMAGE_NAME:$IMAGE_TAG"
    if [[ "$IS_VERSION_TAG" == "true" ]]; then
      DESTINATIONS="$DESTINATIONS --destination $IMAGE_NAME:$RELEASE_TAG"
    fi
    echo "镜像目标 $DESTINATIONS"
    echo "最终执行命令：/kaniko/executor --context . --dockerfile ./Dockerfile $DESTINATIONS --cache=true --verbosity=info"


    /kaniko/executor --context . --dockerfile ./Dockerfile $DESTINATIONS --cache=true --verbosity=info

      
  rules:
    # 触发条件：
    # 1. 提交信息包含 "deploy"（手动或自动触发）
    # 2. 标签匹配 dev/test/release-前缀（核心触发场景）
    # 3. 手动触发（备用）
    - if: '$CI_COMMIT_MESSAGE =~ /deploy/'
      when: always
    - if: '$CI_COMMIT_TAG =~ /^(dev|test|release)-.+$/'
      when: always
    - when: manual
      allow_failure: false  # 手动触发不允许失败

# ------------------------------
# 触发 GitOps 流水线作业
# ------------------------------
trigger-gitops:
  stage: trigger_gitops
  tags:
    - internal  # 使用内部 Runner
  image: harbor.shecc.com/tools/curl:latest  # 仅需要 curl 工具
  # 继承公共模板（自动执行变量解析）
  extends: .before_script_vars
  script: |
    echo "📢 触发 GitOps 流水线..."
    # 构造触发请求参数
    RESPONSE=$(curl -s -w "\n%{http_code}" -X POST \
      -F "token=$GITOPS_TRIGGER_TOKEN" \
      -F "ref=main" \
      -F "variables[APP_NAME]=$APP_NAME" \
      -F "variables[ENV]=$ENV" \
      -F "variables[IMAGE_TAG]=$RELEASE_TAG" \
      "https://gitlab.shecc.com/api/v4/projects/$GITOPS_PROJECT_ID/trigger/pipeline")

    # 解析响应状态码和内容
    HTTP_CODE=$(echo "$RESPONSE" | tail -n1)
    BODY=$(echo "$RESPONSE" | sed '$d')

    if [ "$HTTP_CODE" -ne 201 ]; then
      echo "❌ GitOps 触发失败！状态码：$HTTP_CODE"
      echo "   响应内容：$BODY"
      exit 1  # 失败时终止流水线
    else
      echo "✅ GitOps 触发成功！响应：$BODY"
    fi

  rules:
    # 与 build-and-push 保持一致（标签或提交信息触发）
    - if: '$CI_COMMIT_MESSAGE =~ /deploy/'
      when: always
    - if: '$CI_COMMIT_TAG =~ /^(dev|test|release)-.+$/'
      when: always
    - when: manual