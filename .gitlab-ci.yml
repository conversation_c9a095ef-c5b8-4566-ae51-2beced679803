stages:
  - build
  - trigger_gitops

variables:
  DOCKER_REGISTRY: "harbor.shecc.com"
  PROJECT_PATH: "fms/financialcrossanalyzerweb"
  IMAGE_NAME: "$DOCKER_REGISTRY/$PROJECT_PATH"
  APP_NAME: "financialcrossanalyzerweb"
  IMAGE_TAG: "$CI_PIPELINE_ID"
  ENV: "dev"  # 默认 dev 环境
  RELEASE_TAG: ""
  VERSION: ""
  IS_VERSION_TAG: "false"

# 公共变量解析，不含 docker 登录
.before_script_vars:
  before_script:
    - |
      TAG_NAME="${CI_COMMIT_TAG:-$CI_COMMIT_REF_NAME}"

      case "$TAG_NAME" in
        release-*)
          VERSION="${TAG_NAME#release-}"
          RELEASE_TAG="release-$VERSION"
          IS_VERSION_TAG="true"
          ENV="prod"
          ;;
        test-*)
          VERSION="${TAG_NAME#test-}"
          RELEASE_TAG="test-$VERSION"
          IS_VERSION_TAG="true"
          ENV="test"
          ;;
        dev-*)
          VERSION="${TAG_NAME#dev-}"
          RELEASE_TAG="dev-$VERSION"
          IS_VERSION_TAG="true"
          ENV="dev"
          ;;
      esac 

    - echo "🔧 VERSION=$VERSION"
    - echo "🔧 RELEASE_TAG=$RELEASE_TAG"
    - echo "🔧 IS_VERSION_TAG=$IS_VERSION_TAG"
    - echo "🔧 ENV=$ENV"

# build 阶段：先调用变量解析，再做 docker 登录
build-and-push:
  stage: build
  tags:
    - internal
  image:
    name: harbor.shecc.com/tools/kaniko-excutor:debug
    entrypoint: [""]
  extends: .before_script_vars
  before_script:
    - AUTH_STRING=$(echo -n "$CI_REGISTRY_USER:$CI_REGISTRY_PASSWORD" | base64)
    - mkdir -p /kaniko/.docker
    - echo "{\"auths\":{\"$DOCKER_REGISTRY\":{\"auth\":\"$AUTH_STRING\"}}}" > /kaniko/.docker/config.json
  script: |
    echo "🚀 构建并推送镜像: $IMAGE_NAME:$IMAGE_TAG"
    /kaniko/executor \
      --context . \
      --dockerfile ./Dockerfile \
      --destination $IMAGE_NAME:$IMAGE_TAG \
      $( [[ "$IS_VERSION_TAG" == "true" ]] && echo "--destination $IMAGE_NAME:$RELEASE_TAG" ) \
      --cache=true
  rules:
    - if: '$CI_COMMIT_MESSAGE =~ /deploy/'
      when: always
    - if: '$CI_COMMIT_TAG =~ /^(dev|test|release)-.+$/'
      when: always
    - when: manual

# trigger_gitops 阶段：只调用变量解析，省略 docker 登录
trigger-gitops:
  stage: trigger_gitops
  image: harbor.shecc.com/tools/curl:latest
  extends: .before_script_vars
  script: |
    if [[ "$IS_VERSION_TAG" == "true" ]]; then
      IMAGE_TAG="$RELEASE_TAG"
    fi

    echo "🚀 触发 GitOps: app=$APP_NAME, env=$ENV, tag=$IMAGE_TAG"
    STATUS_CODE=$(curl -s -o response.txt -w "%{http_code}" -X POST \
      -F token=$GITOPS_TRIGGER_TOKEN \
      -F ref=main \
      -F "variables[APP_NAME]=$APP_NAME" \
      -F "variables[ENV]=$ENV" \
      -F "variables[IMAGE_TAG]=$IMAGE_TAG" \
      https://gitlab.shecc.com/api/v4/projects/$GITOPS_PROJECT_ID/trigger/pipeline)

    if [ "$STATUS_CODE" -ne 201 ]; then
      echo "❌ GitOps 触发失败。状态码: $STATUS_CODE"
      cat response.txt
      exit 1
    else
      echo "✅ GitOps 触发成功"
    fi
  rules:
    - if: '$CI_COMMIT_MESSAGE =~ /deploy/'
      when: always
    - if: '$CI_COMMIT_TAG =~ /^(dev|test|release)-.+$/'
      when: always
    - when: manual
