#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
财务报表相关API路由
"""

from flask import Blueprint, request, jsonify, current_app
from werkzeug.utils import secure_filename
import os
import tempfile
from ..services.financial_report_service import FinancialReportService
from ..utils.error_handler import handle_exception, ValidationError, NotFoundError

# 创建蓝图
bp = Blueprint('financial', __name__, url_prefix='/api/financial')

# 创建服务实例
financial_service = FinancialReportService()

@bp.route('/windcodes', methods=['GET'])
def get_windcodes():
    """
    获取所有可用的WindCode列表
    
    Returns:
        JSON: WindCode列表
    """
    try:
        windcodes = financial_service.get_all_windcodes()
        
        return jsonify({
            "success": True,
            "data": windcodes,
            "count": len(windcodes)
        })
        
    except Exception as e:
        error_response, status_code = handle_exception(e, "获取WindCode列表")
        return jsonify(error_response), status_code

@bp.route('/metrics/batch', methods=['POST'])
def get_metrics_batch():
    """
    根据WindCode列表批量查询指标数据
    
    Request Body:
        {
            "windcodes": ["*********", "*********"],
            "report_date": "2023-12-31",  # 可选
            "company_id": "000001"        # 可选
        }
    
    Returns:
        JSON: 指标数据列表
    """
    try:
        data = request.get_json()
        
        if not data or 'windcodes' not in data:
            return jsonify({
                "success": False,
                "error": "缺少windcodes参数"
            }), 400
        
        windcodes = data['windcodes']
        report_date = data.get('report_date')
        company_id = data.get('company_id')
        
        if not isinstance(windcodes, list) or not windcodes:
            return jsonify({
                "success": False,
                "error": "windcodes必须是非空数组"
            }), 400
        
        metrics_data = financial_service.get_metrics_by_windcodes(
            windcodes=windcodes,
            report_date=report_date,
            company_id=company_id
        )
        
        return jsonify({
            "success": True,
            "data": metrics_data,
            "count": len(metrics_data)
        })
        
    except Exception as e:
        error_response, status_code = handle_exception(e, "批量查询指标数据")
        return jsonify(error_response), status_code

@bp.route('/reports/<report_type>/structure', methods=['GET'])
def get_report_structure(report_type):
    """
    获取指定报表类型的结构配置
    
    Args:
        report_type: 报表类型 (资产负债表, 利润表, 现金流量表)
    
    Returns:
        JSON: 报表结构配置
    """
    try:
        valid_types = ["资产负债表", "利润表", "现金流量表"]
        if report_type not in valid_types:
            return jsonify({
                "success": False,
                "error": f"不支持的报表类型: {report_type}"
            }), 400
        
        structure = financial_service.get_report_structure(report_type)
        
        if not structure:
            return jsonify({
                "success": False,
                "error": f"未找到报表类型: {report_type}"
            }), 404
        
        return jsonify({
            "success": True,
            "data": {
                "reportType": report_type,
                **structure
            }
        })
        
    except Exception as e:
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@bp.route('/reports/<report_type>/data', methods=['POST'])
def get_report_data(report_type):
    """
    获取指定报表类型的完整数据
    
    Request Body:
        {
            "report_date": "2023-12-31",  # 可选
            "company_id": "000001",       # 可选
            "format_amounts": true        # 可选，是否格式化金额
        }
    
    Args:
        report_type: 报表类型
    
    Returns:
        JSON: 完整的报表数据
    """
    try:
        print(f"[DEBUG] get_all_reports called with method: {request.method}")
        
        # 支持GET和POST请求
        if request.method == 'POST':
            data = request.get_json() or {}
        else:  # GET请求
            data = request.args.to_dict()
        
        print(f"[DEBUG] Request data: {data}")
        
        report_date = data.get('report_date')
        company_id = data.get('company_id')
        format_amounts = data.get('format_amounts', True)
        
        # 确保format_amounts是布尔值
        if isinstance(format_amounts, str):
            format_amounts = format_amounts.lower() in ['true', '1', 'yes']
        
        print(f"[DEBUG] Parsed parameters - report_date: {report_date}, company_id: {company_id}, format_amounts: {format_amounts}")
        
        report_data = financial_service.get_complete_report_data(
            report_type=report_type,
            report_date=report_date,
            company_id=company_id,
            format_amounts=format_amounts
        )
        
        return jsonify({
            "success": True,
            "data": report_data
        })
        
    except Exception as e:
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@bp.route('/config/upload', methods=['POST'])
def upload_excel_config():
    """
    上传Excel文件并生成财务报表配置
    
    Form Data:
        file: Excel文件
    
    Returns:
        JSON: 生成的配置数据
    """
    try:
        if 'file' not in request.files:
            return jsonify({
                "success": False,
                "error": "未找到上传文件"
            }), 400
        
        file = request.files['file']
        
        if file.filename == '':
            return jsonify({
                "success": False,
                "error": "未选择文件"
            }), 400
        
        if not file.filename.endswith(('.xlsx', '.xls')):
            return jsonify({
                "success": False,
                "error": "文件格式不支持，请上传Excel文件"
            }), 400
        
        # 保存临时文件
        filename = secure_filename(file.filename)
        temp_path = os.path.join(tempfile.gettempdir(), filename)
        file.save(temp_path)
        
        try:
            config = financial_service.generate_config_from_excel(temp_path)
            
            return jsonify({
                "success": True,
                "data": config,
                "message": "配置生成成功"
            })
            
        finally:
            # 清理临时文件
            if os.path.exists(temp_path):
                os.remove(temp_path)
        
    except Exception as e:
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@bp.route('/reports/all', methods=['GET', 'POST'])
def get_all_reports():
    """
    一次性获取所有财务报表数据
    
    Request Body:
        {
            "report_date": "2023-12-31",  # 可选
            "company_id": "000001",       # 可选
            "format_amounts": true        # 可选，是否格式化金额
        }
    
    Returns:
        JSON: 包含三表数据的对象
        {
            "balanceSheet": [...],
            "incomeStatement": [...], 
            "cashFlowStatement": [...]
        }
    """
    try:
        print(f"[DEBUG] get_all_reports called with method: {request.method}")
        
        # 支持GET和POST请求
        if request.method == 'POST':
            data = request.get_json() or {}
        else:  # GET请求
            data = request.args.to_dict()
        
        print(f"[DEBUG] Request data: {data}")
        
        report_date = data.get('report_date')
        company_id = data.get('company_id')
        format_amounts = data.get('format_amounts', True)
        
        # 确保format_amounts是布尔值
        if isinstance(format_amounts, str):
            format_amounts = format_amounts.lower() in ['true', '1', 'yes']
        
        print(f"[DEBUG] Parsed parameters - report_date: {report_date}, company_id: {company_id}, format_amounts: {format_amounts}")
        
        print("[DEBUG] Starting to fetch balance sheet data...")
        balance_data = financial_service.get_complete_report_data(
            report_type="资产负债表",
            report_date=report_date,
            company_id=company_id,
            format_amounts=format_amounts
        )
        print(f"[DEBUG] Balance sheet data fetched, count: {len(balance_data) if balance_data else 0}")
        
        print("[DEBUG] Starting to fetch income statement data...")
        income_data = financial_service.get_complete_report_data(
            report_type="利润表",
            report_date=report_date,
            company_id=company_id,
            format_amounts=format_amounts
        )
        print(f"[DEBUG] Income statement data fetched, count: {len(income_data) if income_data else 0}")
        
        print("[DEBUG] Starting to fetch cash flow data...")
        cashflow_data = financial_service.get_complete_report_data(
            report_type="现金流量表",
            report_date=report_date,
            company_id=company_id,
            format_amounts=format_amounts
        )
        print(f"[DEBUG] Cash flow data fetched, count: {len(cashflow_data) if cashflow_data else 0}")
        
        return jsonify({
            "success": True,
            "data": {
                "balanceSheet": balance_data,
                "incomeStatement": income_data,
                "cashFlowStatement": cashflow_data
            }
        })
        
    except Exception as e:
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@bp.route('/company-metrics/dashboard', methods=['POST'])
def get_company_metrics_for_dashboard():
    """
    获取指定日期、公司ID列表和WindCode列表的公司指标数据（含公司名），合并L2Metrics
    Request Body:
        {
            "report_date": "2024-12-31",
            "windcodes": ["oper_rev", ...],
            "company_ids": ["000001", ...],  # 可选
            "l2_indicators": ["IndicatorNameEN1", ...]  # 可选
        }
    Returns:
        JSON: {"wind_metrics": [...], "l2_metrics": [...]}  # 不再返回merged
    """
    try:
        data = request.get_json()
        report_date = data.get('report_date')
        company_ids = data.get('company_ids')
        if not report_date :
            return jsonify({
                "success": False,
                "error": "参数report_date和windcodes为必填"
            }), 400
        result = financial_service.get_company_metrics_for_dashboard(report_date, company_ids)
        return jsonify({
            "success": True,
            "wind_metrics": result.get('wind_metrics', []),
            "l2_metrics": result.get('l2_metrics', [])
        })
    except Exception as e:
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@bp.route('/health', methods=['GET'])
def health_check():
    """
    健康检查接口
    
    Returns:
        JSON: 服务状态信息
    """
    try:
        # 测试数据库连接
        windcodes = financial_service.get_all_windcodes()
        
        return jsonify({
            "success": True,
            "message": "财务报表服务运行正常",
            "timestamp": str(__import__('datetime').datetime.now()),
            "database_status": "connected",
            "windcode_count": len(windcodes)
        })
        
    except Exception as e:
        return jsonify({
            "success": False,
            "message": "财务报表服务异常",
            "timestamp": str(__import__('datetime').datetime.now()),
            "database_status": "error",
            "error": str(e)
        }), 500
